package com.example.cos.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 管理员更新DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "管理员更新请求")
public class AdminUpdateDTO {

    @ApiModelProperty(value = "管理员ID", example = "1", required = true)
    @NotNull(message = "管理员ID不能为空")
    private Integer id;

    @ApiModelProperty(value = "密码", example = "newpassword123")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    @ApiModelProperty(value = "角色", example = "admin")
    @Pattern(regexp = "^(super_admin|admin|normal_admin)$", message = "角色只能是super_admin、admin或normal_admin")
    private String role;

    @ApiModelProperty(value = "账号状态", example = "1")
    @Pattern(regexp = "^[01]$", message = "账号状态只能是0（禁用）或1（启用）")
    private Integer status;

    public AdminUpdateDTO() {}

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "AdminUpdateDTO{" +
                "id=" + id +
                ", password='" + (password != null ? "[PROTECTED]" : null) + '\'' +
                ", role='" + role + '\'' +
                ", status=" + status +
                '}';
    }
}
