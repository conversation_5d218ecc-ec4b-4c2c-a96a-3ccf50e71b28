package com.example.cos.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.cos.dto.HouseSaleRegistrationCreateDTO;
import com.example.cos.entity.HouseSaleRegistration;
import com.example.cos.mapper.HouseSaleRegistrationMapper;
import com.example.cos.service.HouseSaleRegistrationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 房源出售登记服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class HouseSaleRegistrationServiceImpl extends ServiceImpl<HouseSaleRegistrationMapper, HouseSaleRegistration> implements HouseSaleRegistrationService {

    private static final Logger logger = LoggerFactory.getLogger(HouseSaleRegistrationServiceImpl.class);

    @Override
    public HouseSaleRegistration createRegistration(HouseSaleRegistrationCreateDTO createDTO) {
        logger.info("创建房源出售登记: {}", createDTO);
        
        HouseSaleRegistration registration = new HouseSaleRegistration();
        BeanUtils.copyProperties(createDTO, registration);
        
        boolean saved = save(registration);
        if (!saved) {
            throw new RuntimeException("房源出售登记创建失败");
        }
        
        logger.info("房源出售登记创建成功: {}", registration.getId());
        return registration;
    }

    @Override
    public List<HouseSaleRegistration> findByUserId(Integer userId) {
        return baseMapper.findByUserId(userId);
    }

    @Override
    public List<HouseSaleRegistration> findByCity(String city) {
        return baseMapper.findByCity(city);
    }

    @Override
    public List<HouseSaleRegistration> findByCommunityName(String communityName) {
        return baseMapper.findByCommunityName(communityName);
    }

    @Override
    public List<HouseSaleRegistration> findByHouseType(String houseType) {
        return baseMapper.findByHouseType(houseType);
    }

    @Override
    public boolean updateRegistration(HouseSaleRegistration registration) {
        logger.info("更新房源出售登记: {}", registration);
        return updateById(registration);
    }

    @Override
    public boolean deleteRegistration(Integer id) {
        logger.info("删除房源出售登记: {}", id);
        return removeById(id);
    }
}
