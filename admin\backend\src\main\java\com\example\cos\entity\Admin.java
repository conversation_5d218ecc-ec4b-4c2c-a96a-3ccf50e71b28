package com.example.cos.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 管理员实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "管理员信息")
@TableName(value = "admin_table", autoResultMap = true)
public class Admin implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "管理员ID", example = "1")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "管理员账号", example = "admin", required = true)
    @TableField("username")
    private String username;

    @ApiModelProperty(value = "密码", example = "admin123", required = true)
    @TableField("password")
    private String password;

    @ApiModelProperty(value = "角色", example = "超级管理员", required = true)
    @TableField("role")
    private String role;

    @ApiModelProperty(value = "账号创建时间", example = "2024-01-15 10:30:00")
    @TableField("created_time")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "状态", example = "1", notes = "1=启用，0=禁用")
    @TableField("status")
    private Integer status;

    public Admin() {}

    public Admin(String username, String password, String role) {
        this.username = username;
        this.password = password;
        this.role = role;
        this.status = 1;
        this.createdTime = LocalDateTime.now();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "Admin{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", role='" + role + '\'' +
                ", createdTime=" + createdTime +
                ", status=" + status +
                '}';
    }
}
