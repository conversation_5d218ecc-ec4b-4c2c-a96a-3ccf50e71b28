// pages/webview/webview.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    url: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('WebView页面加载，参数:', options)
    
    if (options.url) {
      const url = decodeURIComponent(options.url)
      console.log('WebView加载URL:', url)
      
      this.setData({
        url: url
      })
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '正在加载...'
      })
    } else {
      console.error('WebView页面缺少URL参数')
      wx.showToast({
        title: '页面链接错误',
        icon: 'none'
      })
      
      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  /**
   * WebView加载完成
   */
  onWebViewLoad(e) {
    console.log('WebView加载完成:', e)
    wx.setNavigationBarTitle({
      title: '外部链接'
    })
  },

  /**
   * WebView加载错误
   */
  onError(e) {
    console.error('WebView加载错误:', e)
    wx.showToast({
      title: '页面加载失败',
      icon: 'none'
    })
    
    wx.setNavigationBarTitle({
      title: '加载失败'
    })
  },

  /**
   * WebView消息处理
   */
  onMessage(e) {
    console.log('WebView消息:', e)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉刷新
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '锦绣资产 - 创造资产价值',
      path: '/pages/index/index'
    }
  }
})
