package com.example.cos.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 微信登录请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "微信登录请求")
public class WechatLoginDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "微信授权码", example = "081234567890abcdef", required = true)
    @NotBlank(message = "微信授权码不能为空")
    private String code;

    @ApiModelProperty(value = "用户昵称", example = "张三")
    private String nickname;

    @ApiModelProperty(value = "用户头像URL", example = "https://thirdwx.qlogo.cn/mmopen/xxx")
    private String avatarUrl;

    @ApiModelProperty(value = "用户手机号", example = "13800138000")
    private String phoneNumber;

    public WechatLoginDTO() {}

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    @Override
    public String toString() {
        return "WechatLoginDTO{" +
                "code='" + code + '\'' +
                ", nickname='" + nickname + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                '}';
    }
}
