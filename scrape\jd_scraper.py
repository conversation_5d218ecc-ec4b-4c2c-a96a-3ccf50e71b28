# -*- coding: utf-8 -*-
"""
京东拍卖爬虫模块 - 专门处理京东拍卖网站的房源信息爬取
"""
import time
import json
import re
import os
import requests
import tempfile
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
from config import Config
from api_client import APIClient

class JDScraper:
    """京东拍卖爬虫类"""
    
    def __init__(self, progress_callback: Optional[Callable] = None, log_callback: Optional[Callable] = None):
        self.driver = None
        self.wait = None
        self.api_client = APIClient()
        self.progress_callback = progress_callback
        self.log_callback = log_callback
        
        # 爬取控制
        self.is_running = False
        self.is_paused = False
        self.should_stop = False
        
        # 统计信息
        self.stats = {'total': 0, 'success': 0, 'error': 0}
        
        # 京东拍卖配置
        self.target_url = "https://pmsearch.jd.com/"
        self.scrape_mode = "auto"
        self.max_pages = 10  # 默认最大页数
        
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}"
        print(log_message)
        
        if self.log_callback:
            self.log_callback(log_message)
    
    def init_driver(self) -> bool:
        """初始化WebDriver - 使用与阿里爬虫相同的多重初始化策略"""
        methods = [
            self._init_driver_with_manager,
            self._init_driver_local,
            self._init_driver_system_chrome
        ]

        for i, method in enumerate(methods, 1):
            try:
                self.log(f"尝试方法 {i}: {method.__name__}")
                if method():
                    return True
            except Exception as e:
                self.log(f"方法 {i} 失败: {str(e)}", "WARNING")
                continue

        self.log("所有初始化方法都失败了", "ERROR")
        return False

    def _init_driver_with_manager(self) -> bool:
        """使用WebDriverManager初始化"""
        self.log("正在使用WebDriverManager下载ChromeDriver...")

        try:
            # 获取Chrome选项
            options = Config.get_chrome_options()

            # 创建WebDriver服务
            service = Service(ChromeDriverManager().install())

            # 创建WebDriver实例
            self.driver = webdriver.Chrome(service=service, options=options)

            return self._configure_driver()
        except Exception as e:
            if "Could not reach host" in str(e) or "offline" in str(e).lower():
                self.log("网络连接问题，跳过WebDriverManager方法", "WARNING")
                raise Exception("网络连接问题")
            else:
                raise e

    def _init_driver_local(self) -> bool:
        """使用本地ChromeDriver初始化"""
        self.log("正在尝试使用本地ChromeDriver...")

        # 获取Chrome选项
        options = Config.get_chrome_options()

        # 检查本地ChromeDriver路径
        import os
        local_paths = [
            "./chromedriver.exe",  # 当前目录
            "chromedriver.exe",    # 当前目录
            "chromedriver",        # Linux/Mac
        ]

        driver_path = None
        for path in local_paths:
            if os.path.exists(path):
                driver_path = path
                self.log(f"找到本地ChromeDriver: {path}")
                break

        if driver_path:
            service = Service(driver_path)
            self.driver = webdriver.Chrome(service=service, options=options)
        else:
            # 尝试使用系统PATH中的chromedriver
            self.driver = webdriver.Chrome(options=options)

        return self._configure_driver()

    def _init_driver_system_chrome(self) -> bool:
        """使用系统Chrome浏览器初始化"""
        self.log("正在尝试使用系统Chrome浏览器...")

        # 获取Chrome选项
        options = Config.get_chrome_options()

        # 尝试指定Chrome二进制路径
        import os
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(
                os.environ.get('USERNAME', 'User')
            )
        ]

        for chrome_path in chrome_paths:
            if os.path.exists(chrome_path):
                options.binary_location = chrome_path
                self.log(f"找到Chrome浏览器: {chrome_path}")
                break

        self.driver = webdriver.Chrome(options=options)

        return self._configure_driver()

    def _configure_driver(self) -> bool:
        """配置WebDriver"""
        try:
            # 配置等待和超时
            self.driver.implicitly_wait(Config.WEBDRIVER_CONFIG['implicit_wait'])
            self.driver.set_page_load_timeout(Config.WEBDRIVER_CONFIG['page_load_timeout'])
            self.driver.set_script_timeout(Config.WEBDRIVER_CONFIG['script_timeout'])

            # 确保浏览器全屏显示
            try:
                self.driver.maximize_window()
                self.log("浏览器窗口已最大化")

                # 获取屏幕尺寸并设置窗口大小
                screen_width = self.driver.execute_script("return screen.width;")
                screen_height = self.driver.execute_script("return screen.height;")
                self.driver.set_window_size(screen_width, screen_height)
                self.log(f"浏览器窗口设置为全屏: {screen_width}x{screen_height}")

                # 将窗口移动到屏幕左上角
                self.driver.set_window_position(0, 0)

            except Exception as e:
                self.log(f"设置全屏失败，但继续执行: {str(e)}", "WARNING")

            # 创建WebDriverWait实例
            self.wait = WebDriverWait(self.driver, Config.WEBDRIVER_CONFIG['explicit_wait'])

            self.log("✓ 浏览器初始化成功")
            return True

        except Exception as e:
            self.log(f"✗ 浏览器配置失败: {str(e)}", "ERROR")
            return False
    
    def navigate_to_target(self) -> bool:
        """导航到京东拍卖首页并加载cookies - 新流程"""
        try:
            # 第一步：访问京东司法拍卖页面
            sifa_url = "https://auction.jd.com/sifa.html"
            self.log(f"第一步：正在访问京东司法拍卖页面: {sifa_url}")
            self.driver.get(sifa_url)

            # 等待页面加载
            time.sleep(Config.get_random_delay('page_load'))
            self.log("✓ 京东司法拍卖页面加载完成")

            # 尝试加载保存的认证数据
            self.load_auth_data()

            # 第二步：定位并点击指定的XPath元素
            target_xpath = Config.JD_XPATH_SELECTORS['sifa_navigation']
            self.log(f"第二步：定位XPath元素: {target_xpath}")

            try:
                # 等待元素可点击
                target_element = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, target_xpath))
                )

                # 使用ActionChains进行模拟鼠标点击
                actions = ActionChains(self.driver)
                actions.move_to_element(target_element).click().perform()

                self.log("✓ 成功点击目标元素")

                # 等待新标签页打开
                time.sleep(3)

                # 检查是否有新标签页打开
                all_windows = self.driver.window_handles
                if len(all_windows) > 1:
                    # 切换到新标签页
                    self.driver.switch_to.window(all_windows[-1])
                    self.log("✓ 已切换到新标签页")

                    # 等待新页面加载
                    time.sleep(Config.get_random_delay('page_load'))

                    # 第三步：直接进行房源链接获取（取消分类选择）
                    self.log("✓ 页面加载完成，准备获取房源链接")
                    return True
                else:
                    self.log("✗ 未检测到新标签页打开", "ERROR")
                    return False

            except TimeoutException:
                self.log(f"✗ 未找到目标元素: {target_xpath}", "ERROR")
                return False
            except Exception as e:
                self.log(f"✗ 点击目标元素失败: {str(e)}", "ERROR")
                return False

        except Exception as e:
            self.log(f"✗ 导航失败: {str(e)}", "ERROR")
            return False

    def save_cookies(self, filename: str = "jd_cookies.json"):
        """保存当前会话的Cookies"""
        try:
            cookies = self.driver.get_cookies()

            # 创建cookies目录
            cookies_dir = os.path.join(os.getcwd(), "cookies")
            if not os.path.exists(cookies_dir):
                os.makedirs(cookies_dir)

            cookie_file = os.path.join(cookies_dir, filename)

            with open(cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)

            self.log(f"✓ Cookies已保存到: {cookie_file}")
            return True

        except Exception as e:
            self.log(f"✗ 保存Cookies失败: {str(e)}", "ERROR")
            return False

    def load_cookies(self, filename: str = "jd_cookies.json"):
        """加载保存的Cookies（兼容性方法）"""
        return self.load_auth_data()

    def load_auth_data(self):
        """加载完整的认证数据（cookies + tokens）"""
        try:
            cookies_dir = os.path.join(os.getcwd(), "cookies")
            auth_file = os.path.join(cookies_dir, "jd_auth_data.json")

            # 优先加载完整的认证数据
            if os.path.exists(auth_file):
                return self._load_full_auth_data(auth_file)
            else:
                # 回退到传统的cookies文件
                return self._load_legacy_cookies()

        except Exception as e:
            self.log(f"✗ 加载认证数据失败: {str(e)}", "ERROR")
            return False

    def _load_full_auth_data(self, auth_file: str):
        """加载完整的认证数据文件"""
        try:
            with open(auth_file, 'r', encoding='utf-8') as f:
                auth_data = json.load(f)

            cookies = auth_data.get('cookies', [])
            localStorage_tokens = auth_data.get('localStorage_tokens', {})
            sessionStorage_tokens = auth_data.get('sessionStorage_tokens', {})

            # 添加cookies
            added_cookies = 0
            for cookie in cookies:
                try:
                    if 'jd.com' in cookie.get('domain', ''):
                        cookie.pop('sameSite', None)
                        cookie.pop('httpOnly', None)
                        self.driver.add_cookie(cookie)
                        added_cookies += 1
                except Exception as e:
                    self.log(f"添加Cookie失败: {str(e)}", "WARNING")
                    continue

            # 刷新页面以应用cookies
            if added_cookies > 0:
                self.driver.refresh()
                time.sleep(3)

            # 设置localStorage tokens
            if localStorage_tokens:
                try:
                    for key, value in localStorage_tokens.items():
                        script = f"localStorage.setItem('{key}', '{value}');"
                        self.driver.execute_script(script)
                    self.log(f"✓ 已设置 {len(localStorage_tokens)} 个localStorage tokens")
                except Exception as e:
                    self.log(f"设置localStorage tokens失败: {str(e)}", "WARNING")

            # 设置sessionStorage tokens
            if sessionStorage_tokens:
                try:
                    for key, value in sessionStorage_tokens.items():
                        script = f"sessionStorage.setItem('{key}', '{value}');"
                        self.driver.execute_script(script)
                    self.log(f"✓ 已设置 {len(sessionStorage_tokens)} 个sessionStorage tokens")
                except Exception as e:
                    self.log(f"设置sessionStorage tokens失败: {str(e)}", "WARNING")

            self.log(f"✓ 已加载完整认证数据: {added_cookies} cookies, {len(localStorage_tokens)} localStorage, {len(sessionStorage_tokens)} sessionStorage")
            return True

        except Exception as e:
            self.log(f"加载完整认证数据失败: {str(e)}", "ERROR")
            return False

    def _load_legacy_cookies(self):
        """加载传统的cookies文件（向后兼容）"""
        try:
            cookies_dir = os.path.join(os.getcwd(), "cookies")
            cookie_file = os.path.join(cookies_dir, "jd_cookies.json")

            if not os.path.exists(cookie_file):
                self.log("未找到认证数据文件")
                return False

            with open(cookie_file, 'r', encoding='utf-8') as f:
                cookies = json.load(f)

            added_count = 0
            for cookie in cookies:
                try:
                    if 'jd.com' in cookie.get('domain', ''):
                        cookie.pop('sameSite', None)
                        cookie.pop('httpOnly', None)
                        self.driver.add_cookie(cookie)
                        added_count += 1
                except Exception as e:
                    self.log(f"添加Cookie失败: {str(e)}", "WARNING")
                    continue

            if added_count > 0:
                self.log(f"✓ 已加载 {added_count} 个传统Cookies")
                self.driver.refresh()
                time.sleep(3)
                return True
            else:
                self.log("未找到有效的京东Cookies")
                return False

        except Exception as e:
            self.log(f"加载传统Cookies失败: {str(e)}", "ERROR")
            return False

    def check_login_status(self) -> bool:
        """检查京东登录状态"""
        try:
            # 检查是否存在登录相关的cookie
            cookies = self.driver.get_cookies()
            login_cookies = [c for c in cookies if 'login' in c['name'].lower() or 'session' in c['name'].lower() or 'token' in c['name'].lower() or 'jd' in c['name'].lower()]

            if login_cookies:
                self.log(f"✓ 检测到登录相关Cookies: {len(login_cookies)} 个")
                return True

            # 检查页面中是否有登录用户信息
            try:
                user_elements = self.driver.find_elements(By.XPATH, "//a[contains(text(), '退出') or contains(text(), '登出') or contains(@class, 'logout')]")
                if user_elements:
                    self.log("✓ 检测到登录状态（找到退出链接）")
                    return True
            except:
                pass

            self.log("⚠ 未检测到登录状态")
            return False

        except Exception as e:
            self.log(f"检查登录状态失败: {str(e)}", "WARNING")
            return False
    
    def click_category_buttons(self) -> bool:
        """点击分类按钮：全部 -> 住宅用房（已废弃，新流程不再使用）"""
        # 注意：此方法已废弃，新的导航流程直接进入房源页面，不再需要分类选择
        self.log("⚠ click_category_buttons方法已废弃，新流程不再使用分类选择", "WARNING")
        return True
    
    def get_house_links(self) -> List[str]:
        """获取当前页面的房源链接列表"""
        house_links = []
        try:
            self.log("正在获取房源链接...")
            
            # 获取房源项目
            house_items_xpath = Config.JD_XPATH_SELECTORS['house_items']
            house_items = self.driver.find_elements(By.XPATH, house_items_xpath)
            
            self.log(f"找到 {len(house_items)} 个房源项目")
            
            for i, item in enumerate(house_items, 1):
                try:
                    # 获取链接元素
                    link_element = item.find_element(By.TAG_NAME, 'a')
                    href = link_element.get_attribute('href')
                    
                    if href:
                        house_links.append(href)
                        self.log(f"获取第{i}个房源链接: {href}")
                    
                except Exception as e:
                    self.log(f"获取第{i}个房源链接失败: {str(e)}", "WARNING")
                    continue
            
            self.log(f"✓ 成功获取 {len(house_links)} 个房源链接")
            return house_links
            
        except Exception as e:
            self.log(f"✗ 获取房源链接失败: {str(e)}", "ERROR")
            return house_links
    
    def extract_house_data(self, url: str) -> Optional[Dict[str, Any]]:
        """从房源详情页提取数据"""
        try:
            self.log(f"正在提取房源数据: {url}")

            # 如果是单个房源提取，需要先建立登录状态
            if not hasattr(self, '_navigation_completed'):
                self.log("单个房源提取模式，先建立登录状态...")

                # 先访问京东主页建立登录状态
                self.driver.get("https://www.jd.com/")
                time.sleep(2)

                # 加载认证数据
                self.load_auth_data()

                # 标记已完成导航设置
                self._navigation_completed = True

            # 访问房源详情页
            self.driver.get(url)

            # 设置浏览器缩放到80%
            self.driver.execute_script("document.body.style.zoom='0.8'")

            # 等待页面加载
            time.sleep(Config.get_random_delay('page_load'))
            
            house_data = {
                'sourceUrl': url,
                'houseType': '住宅'  # 所有住宅用房的house_type都为住宅
            }
            
            # 1. 获取房源标题（遍历span元素并拼接）
            try:
                title_container_xpath = '//*[@id="pageContainer"]/div[2]/div[1]/div[2]/div[1]'
                title_container = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, title_container_xpath))
                )

                # 查找容器内的所有span元素
                span_elements = title_container.find_elements(By.TAG_NAME, 'span')

                title_parts = []
                for span in span_elements:
                    span_text = span.text.strip()
                    if span_text:  # 只添加非空的文本
                        title_parts.append(span_text)

                # 拼接所有span的文本
                if title_parts:
                    house_data['title'] = ' '.join(title_parts)
                    self.log(f"✓ 获取标题 (拼接{len(title_parts)}个span): {house_data['title']}")
                else:
                    # 如果没有找到span元素，使用容器的整体文本
                    house_data['title'] = title_container.text.strip() or "未知标题"
                    self.log(f"⚠ 未找到span元素，使用容器文本: {house_data['title']}")

            except TimeoutException:
                house_data['title'] = "未知标题"
                self.log("✗ 未找到标题容器元素", "WARNING")
            except Exception as e:
                house_data['title'] = "未知标题"
                self.log(f"✗ 提取标题时出错: {str(e)}", "WARNING")

            # 2. 获取拍卖状态
            try:
                auction_type_xpath = Config.JD_XPATH_SELECTORS['auction_type']
                auction_type_element = self.driver.find_element(By.XPATH, auction_type_xpath)
                auction_type_text = auction_type_element.text.strip()

                # 确保拍卖状态不为空
                if auction_type_text:
                    house_data['auctionType'] = auction_type_text
                    self.log(f"✓ 获取拍卖状态: {house_data['auctionType']}")
                else:
                    # 如果元素存在但文本为空，尝试获取其他属性或设置默认值
                    house_data['auctionType'] = "拍卖中"  # 设置默认状态
                    self.log("⚠ 拍卖状态元素文本为空，设置默认状态: 拍卖中", "WARNING")

            except NoSuchElementException:
                house_data['auctionType'] = "拍卖中"  # 设置默认状态
                self.log("✗ 未找到拍卖状态元素，设置默认状态: 拍卖中", "WARNING")

            # 3. 获取结束时间
            try:
                end_time_xpath = Config.JD_XPATH_SELECTORS['end_time']
                end_time_element = self.driver.find_element(By.XPATH, end_time_xpath)
                end_time_text = end_time_element.text.strip()

                # 解析结束时间（例如："预计今日10.00结束"）
                house_data['endTime'] = self.parse_end_time(end_time_text)
                self.log(f"✓ 获取结束时间: {house_data['endTime']}")
            except NoSuchElementException:
                # 使用默认值：次日10:00结束
                current_date = datetime.now()
                next_date = current_date + timedelta(days=1)
                default_end_time = next_date.replace(hour=10, minute=0, second=0, microsecond=0)
                house_data['endTime'] = default_end_time.strftime("%Y-%m-%d %H:%M:%S")
                self.log(f"✗ 未找到结束时间元素，使用默认值：次日10:00 ({house_data['endTime']})", "WARNING")

            # 4. 获取起拍价
            try:
                starting_price_xpath = Config.JD_XPATH_SELECTORS['starting_price']
                starting_price_element = self.driver.find_element(By.XPATH, starting_price_xpath)
                starting_price_text = starting_price_element.text.strip().replace(',', '').replace('万', '')

                # 尝试转换为数字
                try:
                    house_data['startingPrice'] = float(starting_price_text) if starting_price_text.replace('.', '').isdigit() else 0.0
                except ValueError:
                    house_data['startingPrice'] = 0.0

                self.log(f"✓ 获取起拍价: {house_data['startingPrice']}")
            except NoSuchElementException:
                house_data['startingPrice'] = 0.0
                self.log("✗ 未找到起拍价元素", "WARNING")

            # 5. 获取加价幅度
            try:
                price_increment_xpath = Config.JD_XPATH_SELECTORS['price_increment']
                price_increment_element = self.driver.find_element(By.XPATH, price_increment_xpath)
                price_increment_text = price_increment_element.text.strip().replace(',', '').replace('万', '')

                # 尝试转换为数字
                try:
                    house_data['priceIncrement'] = float(price_increment_text) if price_increment_text.replace('.', '').isdigit() else 0.0
                except ValueError:
                    house_data['priceIncrement'] = 0.0

                self.log(f"✓ 获取加价幅度: {house_data['priceIncrement']}")
            except NoSuchElementException:
                house_data['priceIncrement'] = 0.0
                self.log("✗ 未找到加价幅度元素", "WARNING")

            # 6. 获取评估价（如果有的话）
            try:
                # 京东拍卖可能没有单独的评估价字段，可以尝试从其他地方获取
                # 这里先设置为起拍价的1.2倍作为估算
                house_data['evaluationPrice'] = house_data['startingPrice'] * 1.2
                self.log(f"✓ 评估价（估算）: {house_data['evaluationPrice']}")
            except Exception:
                house_data['evaluationPrice'] = 0.0
                self.log("✗ 无法获取评估价，设置为0", "WARNING")

            # 7. 获取竞价周期
            try:
                bidding_cycle_xpath = Config.JD_XPATH_SELECTORS['bidding_cycle']
                bidding_cycle_element = self.driver.find_element(By.XPATH, bidding_cycle_xpath)
                bidding_cycle_text = bidding_cycle_element.text.strip()

                # 提取数字（例如："1天" -> 1）
                cycle_match = re.search(r'(\d+)', bidding_cycle_text)
                if cycle_match:
                    house_data['biddingCycle'] = int(cycle_match.group(1))
                else:
                    house_data['biddingCycle'] = 1  # 默认1天
                    self.log("竞价周期格式解析失败，默认为1天", "WARNING")

                self.log(f"✓ 获取竞价周期: {house_data['biddingCycle']}天")

            except NoSuchElementException:
                house_data['biddingCycle'] = 1  # 默认1天
                self.log("✗ 未找到竞价周期元素，默认为1天", "WARNING")

            # 根据竞价周期和结束时间计算开始时间
            house_data['startTime'] = self.calculate_start_time(house_data['endTime'], house_data['biddingCycle'])
            self.log(f"✓ 计算开始时间: {house_data['startTime']}")

            # 8. 获取房源图片
            house_data['detailImageList'] = self.get_house_images()

            # 9. 获取竞买公告截图
            house_data['situationSurvey'] = self.get_announcement_screenshot()

            # 10. 获取相关文档链接
            evaluation_report, execution_order, property_report = self.get_document_links()
            house_data['evaluationReport'] = evaluation_report
            house_data['executionOrder'] = execution_order
            house_data['propertyReport'] = property_report
            
            self.log("✓ 房源数据提取完成")
            return house_data

        except Exception as e:
            self.log(f"✗ 提取房源数据失败: {str(e)}", "ERROR")
            return None

    def parse_end_time(self, end_time_text: str) -> str:
        """解析结束时间文本"""
        try:
            # 例如："预计今日10.00结束" -> "2024-01-01 10:00:00"
            current_date = datetime.now()

            # 提取时间部分
            time_match = re.search(r'(\d{1,2})\.(\d{2})', end_time_text)
            if time_match:
                hour = int(time_match.group(1))
                minute = int(time_match.group(2))

                # 如果是"今日"，使用当前日期
                if "今日" in end_time_text:
                    end_datetime = current_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
                elif "次日" in end_time_text or "明日" in end_time_text:
                    # 如果是次日，日期加1天
                    next_date = current_date + timedelta(days=1)
                    end_datetime = next_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
                else:
                    # 默认使用当前日期
                    end_datetime = current_date.replace(hour=hour, minute=minute, second=0, microsecond=0)

                return end_datetime.strftime("%Y-%m-%d %H:%M:%S")
            else:
                # 如果无法解析时间，使用默认值：次日10:00
                next_date = current_date + timedelta(days=1)
                default_end_time = next_date.replace(hour=10, minute=0, second=0, microsecond=0)
                self.log(f"无法解析结束时间'{end_time_text}'，使用默认值：次日10:00", "WARNING")
                return default_end_time.strftime("%Y-%m-%d %H:%M:%S")

        except Exception as e:
            # 异常情况下也使用默认值
            current_date = datetime.now()
            next_date = current_date + timedelta(days=1)
            default_end_time = next_date.replace(hour=10, minute=0, second=0, microsecond=0)
            self.log(f"解析结束时间失败: {str(e)}，使用默认值：次日10:00", "WARNING")
            return default_end_time.strftime("%Y-%m-%d %H:%M:%S")

    def calculate_start_time(self, end_time: str, bidding_cycle_days: int) -> str:
        """根据结束时间和竞价周期计算开始时间"""
        try:
            # 如果结束时间无效，使用默认值
            if not end_time or end_time == "未知时间":
                current_time = datetime.now()
                default_start_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
                self.log("结束时间无效，使用当前时间作为开始时间", "WARNING")
                return default_start_time

            # 如果竞价周期无效，使用默认值1天
            if bidding_cycle_days <= 0:
                bidding_cycle_days = 1
                self.log("竞价周期无效，使用默认值1天", "WARNING")

            end_datetime = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
            start_datetime = end_datetime - timedelta(days=bidding_cycle_days)

            return start_datetime.strftime("%Y-%m-%d %H:%M:%S")

        except Exception as e:
            # 异常情况下使用当前时间
            current_time = datetime.now()
            default_start_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
            self.log(f"计算开始时间失败: {str(e)}，使用当前时间", "WARNING")
            return default_start_time

    def get_house_images(self) -> List[str]:
        """获取房源图片并上传"""
        images = []
        try:
            self.log("正在获取房源图片...")

            # 尝试获取多个房源图片
            for i in range(1, 11):  # 最多尝试10张图片
                try:
                    img_xpath = Config.JD_XPATH_SELECTORS['house_images_base'].format(i)
                    img_element = self.driver.find_element(By.XPATH, img_xpath)
                    img_src = img_element.get_attribute('src')

                    if img_src:
                        # 下载并上传图片
                        uploaded_url = self.download_and_upload_image(img_src, f"house_image_{i}")
                        if uploaded_url:
                            images.append(uploaded_url)
                            self.log(f"✓ 第{i}张房源图片上传成功")
                        else:
                            self.log(f"✗ 第{i}张房源图片上传失败", "WARNING")

                except NoSuchElementException:
                    # 没有更多图片了
                    break
                except Exception as e:
                    self.log(f"获取第{i}张图片失败: {str(e)}", "WARNING")
                    continue

            self.log(f"✓ 共上传 {len(images)} 张房源图片")
            return images

        except Exception as e:
            self.log(f"✗ 获取房源图片失败: {str(e)}", "ERROR")
            return images

    def get_announcement_screenshot(self) -> str:
        """获取竞买公告截图 - 基于子元素内容验证的多XPath策略"""
        try:
            self.log("正在获取竞买公告截图...")

            # 从配置文件获取XPath列表和验证关键词
            announcement_xpaths = Config.JD_XPATH_SELECTORS.get('announcement_xpaths', [
                '//*[@id="pmMainFloor"]/ul/li[1]',
                '//*[@id="pmMainFloor"]/ul/li[2]',
                '//*[@id="pmMainFloor"]/ul/li[3]',
                '//*[@id="pmMainFloor"]'
            ])

            validation_keywords = Config.JD_XPATH_SELECTORS.get('announcement_validation_keywords', ["标的", "拍品", "竞买公告"])

            # 逐个尝试每个XPath
            for i, xpath in enumerate(announcement_xpaths, 1):
                try:
                    self.log(f"尝试XPath {i}: {xpath}")

                    # 查找元素
                    announcement_element = self.driver.find_element(By.XPATH, xpath)

                    # 验证子元素中是否包含关键词
                    if self._validate_announcement_content(announcement_element, validation_keywords):
                        self.log(f"✓ XPath {i} 验证通过，找到包含关键词的子元素")

                        # 从元素顶部开始截图
                        uploaded_url = self._screenshot_from_top(announcement_element, f"announcement_xpath_{i}")

                        if uploaded_url:
                            self.log(f"✓ 竞买公告截图上传成功 (使用XPath {i})")
                            return uploaded_url
                        else:
                            self.log(f"✗ XPath {i} 截图上传失败", "WARNING")
                    else:
                        self.log(f"⚠ XPath {i} 验证失败，子元素中未找到关键词")

                except NoSuchElementException:
                    self.log(f"⚠ XPath {i} 未找到元素，继续尝试下一个")
                    continue
                except Exception as e:
                    self.log(f"⚠ XPath {i} 执行失败: {str(e)}，继续尝试下一个")
                    continue

            # 所有XPath都失败了
            self.log("✗ 所有提供的XPath都未能找到有效的竞买公告元素", "ERROR")
            return ""

        except Exception as e:
            self.log(f"✗ 获取竞买公告截图失败: {str(e)}", "ERROR")
            return ""

    def _validate_announcement_content(self, element, keywords: List[str]) -> bool:
        """验证元素及其子元素中是否包含指定关键词"""
        try:
            # 获取元素及其所有子元素的文本内容
            element_text = element.text

            # 检查主元素文本
            for keyword in keywords:
                if keyword in element_text:
                    self.log(f"在主元素中找到关键词: {keyword}")
                    return True

            # 检查所有子元素
            try:
                # 获取所有子元素
                child_elements = element.find_elements(By.XPATH, ".//*")

                for child in child_elements:
                    try:
                        child_text = child.text
                        for keyword in keywords:
                            if keyword in child_text:
                                self.log(f"在子元素中找到关键词: {keyword}")
                                return True
                    except Exception:
                        continue

            except Exception as e:
                self.log(f"检查子元素时出错: {str(e)}", "WARNING")

            # 使用JavaScript获取更深层的文本内容
            try:
                all_text = self.driver.execute_script("""
                    function getAllText(element) {
                        var text = element.textContent || element.innerText || '';
                        return text;
                    }
                    return getAllText(arguments[0]);
                """, element)

                for keyword in keywords:
                    if keyword in all_text:
                        self.log(f"在深层文本中找到关键词: {keyword}")
                        return True

            except Exception as e:
                self.log(f"JavaScript文本检查时出错: {str(e)}", "WARNING")

            return False

        except Exception as e:
            self.log(f"验证公告内容时出错: {str(e)}", "ERROR")
            return False

    def _screenshot_from_top(self, element, filename_prefix: str) -> str:
        """从元素顶部开始截图并上传"""
        try:
            # 将元素滚动到页面顶部可见位置
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'start'});", element)
            time.sleep(1)

            # 确保元素完全可见
            self.driver.execute_script("""
                arguments[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                    inline: 'nearest'
                });
            """, element)
            time.sleep(2)

            # 截图元素
            screenshot_path = f"{filename_prefix}_{int(time.time())}.png"
            element.screenshot(screenshot_path)

            # 上传截图并获取URL（upload_image方法已包含水印处理）
            uploaded_url = self.upload_image(screenshot_path)

            # 删除本地文件
            try:
                os.remove(screenshot_path)
            except:
                pass

            return uploaded_url if uploaded_url else ""

        except Exception as e:
            self.log(f"从顶部截图失败: {str(e)}", "ERROR")
            return ""

    def find_announcement_elements(self) -> List[Dict[str, Any]]:
        """智能发现页面中的竞买公告元素"""
        try:
            self.log("正在智能搜索竞买公告元素...")

            announcement_elements = []

            # 搜索策略1: 通过文本内容搜索
            text_keywords = ["竞买公告", "拍卖公告", "公告", "竞买须知", "拍卖须知"]

            for keyword in text_keywords:
                try:
                    elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                    for element in elements:
                        # 检查元素是否可见且有足够的内容
                        if element.is_displayed() and len(element.text.strip()) > 10:
                            announcement_elements.append({
                                'element': element,
                                'xpath': self._get_element_xpath(element),
                                'text_preview': element.text.strip()[:100] + "..." if len(element.text.strip()) > 100 else element.text.strip(),
                                'keyword': keyword,
                                'score': self._calculate_announcement_score(element, keyword)
                            })
                except Exception as e:
                    self.log(f"搜索关键词'{keyword}'时出错: {str(e)}", "WARNING")
                    continue

            # 搜索策略2: 通过常见的公告容器ID/Class搜索
            common_selectors = [
                '//*[@id="pmMainFloor"]//li',
                '//*[@class*="announcement"]',
                '//*[@class*="notice"]',
                '//*[@class*="bulletin"]',
                '//*[@id*="announcement"]',
                '//*[@id*="notice"]'
            ]

            for selector in common_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and any(keyword in element.text for keyword in text_keywords):
                            announcement_elements.append({
                                'element': element,
                                'xpath': selector,
                                'text_preview': element.text.strip()[:100] + "..." if len(element.text.strip()) > 100 else element.text.strip(),
                                'keyword': 'selector_match',
                                'score': self._calculate_announcement_score(element, 'selector_match')
                            })
                except Exception as e:
                    self.log(f"搜索选择器'{selector}'时出错: {str(e)}", "WARNING")
                    continue

            # 去重并按分数排序
            unique_elements = []
            seen_texts = set()

            for item in announcement_elements:
                text_hash = hash(item['text_preview'])
                if text_hash not in seen_texts:
                    seen_texts.add(text_hash)
                    unique_elements.append(item)

            # 按分数降序排序
            unique_elements.sort(key=lambda x: x['score'], reverse=True)

            self.log(f"发现 {len(unique_elements)} 个潜在的竞买公告元素")
            for i, item in enumerate(unique_elements[:5], 1):  # 只显示前5个
                self.log(f"  {i}. 关键词: {item['keyword']}, 分数: {item['score']}, 预览: {item['text_preview'][:50]}...")

            return unique_elements

        except Exception as e:
            self.log(f"智能搜索竞买公告元素失败: {str(e)}", "ERROR")
            return []

    def _get_element_xpath(self, element) -> str:
        """获取元素的XPath"""
        try:
            return self.driver.execute_script("""
                function getXPath(element) {
                    if (element.id !== '') {
                        return '//*[@id="' + element.id + '"]';
                    }
                    if (element === document.body) {
                        return '/html/body';
                    }
                    var ix = 0;
                    var siblings = element.parentNode.childNodes;
                    for (var i = 0; i < siblings.length; i++) {
                        var sibling = siblings[i];
                        if (sibling === element) {
                            return getXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';
                        }
                        if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
                            ix++;
                        }
                    }
                }
                return getXPath(arguments[0]);
            """, element)
        except:
            return "unknown_xpath"

    def _calculate_announcement_score(self, element, keyword: str) -> int:
        """计算公告元素的相关性分数"""
        try:
            score = 0
            text = element.text.lower()

            # 基础分数
            if "竞买公告" in text:
                score += 100
            elif "拍卖公告" in text:
                score += 90
            elif "公告" in text:
                score += 70
            elif "竞买须知" in text:
                score += 80
            elif "拍卖须知" in text:
                score += 75

            # 长度分数（内容越多分数越高，但有上限）
            text_length = len(text)
            if text_length > 500:
                score += 50
            elif text_length > 200:
                score += 30
            elif text_length > 100:
                score += 20
            elif text_length > 50:
                score += 10

            # 位置分数（在特定容器中的元素分数更高）
            element_html = element.get_attribute('outerHTML').lower()
            if 'pmMainFloor' in element_html:
                score += 30
            if 'announcement' in element_html or 'notice' in element_html:
                score += 20

            # 可见性分数
            if element.is_displayed():
                score += 10

            return score

        except Exception as e:
            return 0

    def get_document_links(self) -> tuple:
        """获取相关文档链接"""
        try:
            self.log("正在获取相关文档链接...")

            evaluation_report = ""
            execution_order = ""
            property_report = ""

            try:
                attachment_xpath = Config.JD_XPATH_SELECTORS['attachment']
                attachment_element = self.driver.find_element(By.XPATH, attachment_xpath)

                if attachment_element:
                    href = attachment_element.get_attribute('href')
                    if href:
                        # 根据文档内容判断类型，这里简化处理
                        evaluation_report = href
                        execution_order = href
                        property_report = href

                        self.log("✓ 获取文档链接成功")

            except NoSuchElementException:
                self.log("✗ 未找到文档链接元素", "WARNING")

            return evaluation_report, execution_order, property_report

        except Exception as e:
            self.log(f"✗ 获取文档链接失败: {str(e)}", "ERROR")
            return "", "", ""

    def upload_image(self, image_path: str) -> str:
        """上传图片到服务器"""
        try:
            import requests
            import tempfile
            from image_watermark import add_watermark_to_image_data

            # 读取图片数据并添加水印
            with open(image_path, 'rb') as f:
                original_data = f.read()

            try:
                watermarked_data = add_watermark_to_image_data(original_data)
                self.log(f"✓ 成功为图片添加水印: {os.path.basename(image_path)}")
            except Exception as e:
                self.log(f"添加水印失败，使用原图: {str(e)}", "WARNING")
                watermarked_data = original_data

            # 创建临时文件保存水印图片
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                temp_file.write(watermarked_data)
                watermarked_path = temp_file.name

            upload_url = f"{self.api_client.base_url}/api/cos/upload"

            with open(watermarked_path, 'rb') as f:
                files = {'file': (os.path.basename(image_path), f, 'image/png')}

                # 为文件上传创建专门的headers
                upload_headers = {}
                for key, value in self.api_client.session.headers.items():
                    if key.lower() != 'content-type':
                        upload_headers[key] = value

                response = requests.post(
                    upload_url,
                    files=files,
                    headers=upload_headers,
                    timeout=30
                )

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 200 and result.get('data'):
                    file_url = result['data'].get('fileUrl', '')
                    self.log(f"✓ 图片上传成功: {file_url}")
                    return file_url
                else:
                    self.log(f"图片上传响应错误: {result}", "ERROR")
                    return ""
            else:
                self.log(f"图片上传请求失败: {response.status_code} - {response.text}", "ERROR")
                return ""

        except Exception as e:
            self.log(f"图片上传失败: {str(e)}", "ERROR")
            return ""
        finally:
            # 清理临时水印文件
            try:
                if 'watermarked_path' in locals():
                    os.remove(watermarked_path)
            except:
                pass

    def download_and_upload_image(self, img_url: str, filename_prefix: str) -> str:
        """下载图片并上传到服务器"""
        try:
            import requests
            import tempfile

            # 下载图片
            response = requests.get(img_url, timeout=30)
            if response.status_code != 200:
                self.log(f"下载图片失败: {img_url}", "ERROR")
                return ""

            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png', prefix=f'{filename_prefix}_') as temp_file:
                temp_file.write(response.content)
                temp_file_path = temp_file.name

            try:
                # 上传图片
                uploaded_url = self.upload_image(temp_file_path)
                return uploaded_url
            finally:
                # 删除临时文件
                try:
                    os.remove(temp_file_path)
                except:
                    pass

        except Exception as e:
            self.log(f"下载并上传图片失败: {str(e)}", "ERROR")
            return ""

    def start_scraping(self) -> Dict[str, int]:
        """开始爬取任务"""
        try:
            self.log("=" * 60)
            self.log("开始京东拍卖房源爬取任务")
            self.log("=" * 60)

            self.is_running = True
            self.should_stop = False
            self.stats = {'total': 0, 'success': 0, 'error': 0}

            # 初始化浏览器
            if not self.init_driver():
                return self.stats

            # 导航到目标网站（新流程：直接进入房源页面）
            if not self.navigate_to_target():
                return self.stats

            # 标记导航已完成，避免在extract_house_data中重复建立登录状态
            self._navigation_completed = True

            # 获取房源链接（取消分类选择，直接获取）
            house_links = self.get_house_links()

            if not house_links:
                self.log("✗ 未获取到任何房源链接", "ERROR")
                return self.stats

            # 限制爬取数量
            if len(house_links) > self.max_pages:
                house_links = house_links[:self.max_pages]
                self.log(f"限制爬取数量为 {self.max_pages} 个房源")

            self.stats['total'] = len(house_links)

            # 逐个处理房源
            for i, url in enumerate(house_links, 1):
                if self.should_stop:
                    self.log("收到停止信号，终止爬取")
                    break

                # 暂停检查
                while self.is_paused and not self.should_stop:
                    time.sleep(1)

                if self.should_stop:
                    break

                self.log(f"\n处理第 {i}/{len(house_links)} 个房源")

                # 提取房源数据
                house_data = self.extract_house_data(url)

                if house_data:
                    # 上传到API
                    result = self.api_client.create_house_with_retry(house_data)

                    if result['success']:
                        self.stats['success'] += 1
                        self.log(f"✓ 房源 {i} 处理成功: {house_data.get('title', '未知标题')}")
                    else:
                        self.stats['error'] += 1
                        self.log(f"✗ 房源 {i} 上传失败: {result.get('error', '未知错误')}", "ERROR")
                else:
                    self.stats['error'] += 1
                    self.log(f"✗ 房源 {i} 数据提取失败", "ERROR")

                # 更新进度
                if self.progress_callback:
                    self.progress_callback(i, len(house_links), f"处理房源 {i}/{len(house_links)}")

                # 随机延迟
                if i < len(house_links):  # 不是最后一个
                    delay = Config.get_random_delay('request')
                    time.sleep(delay)

            self.log("=" * 60)
            self.log(f"京东拍卖爬取任务完成！总计: {self.stats['total']}, 成功: {self.stats['success']}, 失败: {self.stats['error']}")
            self.log("=" * 60)

            # 任务完成时保存cookies和tokens
            self.log("任务完成，保存认证数据...")
            self.save_cookies_and_tokens()

            return self.stats

        except Exception as e:
            self.log(f"✗ 爬取任务异常: {str(e)}", "ERROR")
            self.stats['error'] += 1
            return self.stats

        finally:
            self.is_running = False
            self.cleanup()

    def pause_scraping(self):
        """暂停爬取"""
        self.is_paused = True
        self.log("爬取任务已暂停")

    def resume_scraping(self):
        """恢复爬取"""
        self.is_paused = False
        self.log("爬取任务已恢复")

    def stop_scraping(self):
        """停止爬取"""
        self.should_stop = True
        self.is_paused = False
        self.log("正在停止爬取任务...")

    def cleanup(self):
        """清理资源 - 在关闭前保存cookies和token"""
        try:
            # 在关闭浏览器前保存cookies和token
            if self.driver:
                self.log("正在保存cookies和token...")
                self.save_cookies_and_tokens()

                self.driver.quit()
                self.log("✓ 浏览器已关闭")

            if self.api_client:
                self.api_client.close()
                self.log("✓ API客户端已关闭")

        except Exception as e:
            self.log(f"清理资源时出错: {str(e)}", "WARNING")

    def save_cookies_and_tokens(self):
        """保存cookies和token信息"""
        try:
            if not self.driver:
                return False

            # 获取所有cookies
            cookies = self.driver.get_cookies()

            # 获取localStorage中的token信息
            tokens = {}
            try:
                # 执行JavaScript获取localStorage中的认证相关信息
                local_storage_script = """
                var tokens = {};
                for (var i = 0; i < localStorage.length; i++) {
                    var key = localStorage.key(i);
                    if (key && (key.toLowerCase().includes('token') ||
                               key.toLowerCase().includes('auth') ||
                               key.toLowerCase().includes('login') ||
                               key.toLowerCase().includes('session') ||
                               key.toLowerCase().includes('user'))) {
                        tokens[key] = localStorage.getItem(key);
                    }
                }
                return tokens;
                """
                tokens = self.driver.execute_script(local_storage_script)
                self.log(f"获取到 {len(tokens)} 个localStorage token")
            except Exception as e:
                self.log(f"获取localStorage tokens失败: {str(e)}", "WARNING")

            # 获取sessionStorage中的信息
            session_tokens = {}
            try:
                session_storage_script = """
                var sessionTokens = {};
                for (var i = 0; i < sessionStorage.length; i++) {
                    var key = sessionStorage.key(i);
                    if (key && (key.toLowerCase().includes('token') ||
                               key.toLowerCase().includes('auth') ||
                               key.toLowerCase().includes('login') ||
                               key.toLowerCase().includes('session') ||
                               key.toLowerCase().includes('user'))) {
                        sessionTokens[key] = sessionStorage.getItem(key);
                    }
                }
                return sessionTokens;
                """
                session_tokens = self.driver.execute_script(session_storage_script)
                self.log(f"获取到 {len(session_tokens)} 个sessionStorage token")
            except Exception as e:
                self.log(f"获取sessionStorage tokens失败: {str(e)}", "WARNING")

            # 创建完整的认证信息数据结构
            auth_data = {
                'cookies': cookies,
                'localStorage_tokens': tokens,
                'sessionStorage_tokens': session_tokens,
                'timestamp': datetime.now().isoformat(),
                'domain': 'jd.com'
            }

            # 保存到文件
            cookies_dir = os.path.join(os.getcwd(), "cookies")
            if not os.path.exists(cookies_dir):
                os.makedirs(cookies_dir)

            auth_file = os.path.join(cookies_dir, "jd_auth_data.json")

            with open(auth_file, 'w', encoding='utf-8') as f:
                json.dump(auth_data, f, ensure_ascii=False, indent=2)

            self.log(f"✓ 认证数据已保存到: {auth_file}")
            self.log(f"包含 {len(cookies)} 个cookies, {len(tokens)} 个localStorage tokens, {len(session_tokens)} 个sessionStorage tokens")

            # 同时保存传统的cookies文件以保持兼容性
            self.save_cookies("jd_cookies.json")

            return True

        except Exception as e:
            self.log(f"✗ 保存认证数据失败: {str(e)}", "ERROR")
            return False
