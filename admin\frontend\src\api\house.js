import request from '@/utils/request'

// 房源管理API

/**
 * 分页查询房源
 * @param {Object} data 查询条件
 */
export function getHouseList(data) {
  return request({
    url: '/house/page',
    method: 'post',
    data
  })
}

/**
 * 根据ID查询房源详情
 * @param {Number} id 房源ID
 */
export function getHouseById(id) {
  return request({
    url: `/house/${id}`,
    method: 'get'
  })
}

/**
 * 创建房源
 * @param {Object} data 房源数据
 */
export function createHouse(data) {
  return request({
    url: '/house/create',
    method: 'post',
    data
  })
}

/**
 * 更新房源
 * @param {Object} data 房源数据
 */
export function updateHouse(data) {
  return request({
    url: '/house/update',
    method: 'put',
    data
  })
}

/**
 * 删除房源
 * @param {Number} id 房源ID
 */
export function deleteHouse(id) {
  return request({
    url: `/house/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除房源
 * @param {Array} ids 房源ID列表
 */
export function batchDeleteHouses(ids) {
  return request({
    url: '/house/batch',
    method: 'delete',
    data: ids
  })
}

/**
 * 根据拍卖状态查询房源
 * @param {Number} auctionStatus 拍卖状态（0-未开拍，1-一拍中，2-二拍中，3-变卖中，4-已结束）
 */
export function getHousesByAuctionStatus(auctionStatus) {
  return request({
    url: `/house/auction-status/${auctionStatus}`,
    method: 'get'
  })
}

/**
 * 根据户型查询房源
 * @param {String} houseType 户型
 */
export function getHousesByHouseType(houseType) {
  return request({
    url: '/house/house-type',
    method: 'get',
    params: { houseType }
  })
}

/**
 * 根据小区名称查询房源
 * @param {String} communityName 小区名称
 */
export function getHousesByCommunityName(communityName) {
  return request({
    url: '/house/community',
    method: 'get',
    params: { communityName }
  })
}

/**
 * 根据起拍价区间查询房源
 * @param {Number} minPrice 最低起拍价
 * @param {Number} maxPrice 最高起拍价
 */
export function getHousesByStartingPriceRange(minPrice, maxPrice) {
  return request({
    url: '/house/starting-price-range',
    method: 'get',
    params: { minPrice, maxPrice }
  })
}

/**
 * 根据房屋类型查询房源
 * @param {Number} houseCategory 房屋类型（0-住宅，1-商办）
 */
export function getHousesByHouseCategory(houseCategory) {
  return request({
    url: `/house/house-category/${houseCategory}`,
    method: 'get'
  })
}

/**
 * 根据是否特殊房屋查询房源
 * @param {Number} isSpecial 是否特殊房屋（0-否，1-是）
 */
export function getHousesByIsSpecial(isSpecial) {
  return request({
    url: `/house/special/${isSpecial}`,
    method: 'get'
  })
}

/**
 * 根据是否精选查询房源
 * @param {Number} isSelected 是否精选（0-否，1-是）
 */
export function getHousesByIsSelected(isSelected) {
  return request({
    url: `/house/selected/${isSelected}`,
    method: 'get'
  })
}

/**
 * 搜索房源
 * @param {String} keyword 搜索关键词
 */
export function searchHouses(keyword) {
  return request({
    url: '/house/search',
    method: 'get',
    params: { keyword }
  })
}

/**
 * 获取推荐房源
 * @param {Number} limit 数量限制
 */
export function getRecommendedHouses(limit = 10) {
  return request({
    url: '/house/recommended',
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取即将开拍的房源
 * @param {Number} limit 数量限制
 */
export function getUpcomingAuctions(limit = 10) {
  return request({
    url: '/house/upcoming',
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取房源统计信息
 */
export function getHouseStatistics() {
  return request({
    url: '/house/statistics',
    method: 'get'
  })
}

/**
 * 批量更新房源状态
 * @param {Array} ids 房源ID列表
 * @param {Number} status 新状态
 */
export function batchUpdateHouseStatus(ids, status) {
  return request({
    url: '/house/batch-status',
    method: 'put',
    data: { ids, status }
  })
}
