<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-image">
        <img src="/404.svg" alt="404" />
      </div>
      
      <div class="error-info">
        <h1>404</h1>
        <h2>页面不存在</h2>
        <p>抱歉，您访问的页面不存在或已被删除</p>
        
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 建议链接 -->
    <div class="suggestions">
      <h3>您可能想要访问：</h3>
      <div class="suggestion-links">
        <router-link to="/admin/dashboard" class="suggestion-item">
          <el-icon><Odometer /></el-icon>
          <span>仪表板</span>
        </router-link>
        <router-link to="/admin/houses" class="suggestion-item">
          <el-icon><House /></el-icon>
          <span>房源管理</span>
        </router-link>
        <router-link to="/admin/users" class="suggestion-item">
          <el-icon><User /></el-icon>
          <span>用户管理</span>
        </router-link>
        <router-link to="/admin/consultations" class="suggestion-item">
          <el-icon><ChatDotRound /></el-icon>
          <span>咨询管理</span>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/')
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FFE4E1 0%, #FFC0CB 50%, #FFB6C1 100%);
  padding: 40px 20px;
}

.error-content {
  display: flex;
  align-items: center;
  max-width: 800px;
  margin-bottom: 60px;

  .error-image {
    flex: 1;
    text-align: center;
    margin-right: 40px;

    img {
      max-width: 300px;
      width: 100%;
      height: auto;
    }
  }

  .error-info {
    flex: 1;

    h1 {
      font-size: 120px;
      font-weight: 700;
      color: var(--primary-color);
      margin: 0;
      line-height: 1;
    }

    h2 {
      font-size: 32px;
      color: #303133;
      margin: 10px 0 20px 0;
      font-weight: 600;
    }

    p {
      font-size: 16px;
      color: #606266;
      margin-bottom: 30px;
      line-height: 1.6;
    }

    .error-actions {
      display: flex;
      gap: 15px;

      .el-button {
        padding: 12px 24px;
        font-size: 14px;
      }
    }
  }
}

.suggestions {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);

  h3 {
    margin: 0 0 20px 0;
    color: #303133;
    font-size: 18px;
    text-align: center;
  }

  .suggestion-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;

    .suggestion-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;
      background: #fff;
      border-radius: 8px;
      text-decoration: none;
      color: #606266;
      transition: all 0.3s;
      border: 1px solid #e4e7ed;

      &:hover {
        color: var(--primary-color);
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 182, 193, 0.3);
      }

      .el-icon {
        font-size: 24px;
        margin-bottom: 8px;
      }

      span {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

@media (max-width: 768px) {
  .error-content {
    flex-direction: column;
    text-align: center;

    .error-image {
      margin-right: 0;
      margin-bottom: 30px;

      img {
        max-width: 200px;
      }
    }

    .error-info {
      h1 {
        font-size: 80px;
      }

      h2 {
        font-size: 24px;
      }

      .error-actions {
        justify-content: center;
        flex-wrap: wrap;
      }
    }
  }

  .suggestions {
    width: 100%;
    max-width: 400px;

    .suggestion-links {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}
</style>
