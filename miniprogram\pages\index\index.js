// pages/index/index.js
const util = require('../../utils/util.js')
const api = require('../../config/api.js')
const carouselService = require('../../services/carousel.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    searchValue: '',
    showBuyHouseModal: false, // 控制我要买房弹窗显示
    carousels: [], // 轮播图数据
    statsData: {
      todayNew: 86,
      auctioning: 116,
      upcoming: 2068,
      avgPrice: 7650
    },
    // 导航栏相关
    statusBarHeight: 0,
    navBarHeight: 88,
    selectedCity: '重庆市区',
    // 房源列表相关
    houseList: [],
    filterType: 'all',
    currentPage: 1,
    pageSize: 20,
    hasMore: true,
    loading: false,
    // 筛选面板显示状态
    showRegionPanel: false,
    showAreaPanel: false,
    showPricePanel: false,
    showMorePanel: false,
    showSortPanel: false,
    // 当前筛选条件
    regionFilter: 'all',
    areaFilter: 'all',
    priceFilter: 'all',
    auctionType: 'all',
    auctionStatus: 'all',
    sortType: 'smart',
    customMinPrice: '',
    customMaxPrice: '',
    // 临时筛选条件（用于面板中的选择）
    tempRegionFilter: 'all',
    tempAreaFilter: 'all',
    tempPriceFilter: 'all',
    tempAuctionType: 'all',
    tempAuctionStatus: 'all',
    tempSortType: 'smart',
    // 筛选文本显示
    regionFilterText: '区域',
    areaFilterText: '面积',
    priceFilterText: '价格',
    sortFilterText: '智能排序',
    // 当前城市对应的区域选项
    currentRegionOptions: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initNavBar();
    this.loadStatsData();
    this.loadCarousels();
    this.loadHouseList();
    this.loadSelectedCity();
    this.updateRegionOptions();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 从城市选择页面返回时更新选中的城市
    this.loadSelectedCity();
    this.updateRegionOptions();
  },

  /**
   * 页面导航处理
   */
  navigateTo(e) {
    const page = e.currentTarget.dataset.page;
    console.log('导航到页面:', page);
    
    // 根据不同的页面类型进行导航
    switch(page) {
      case 'auction-house':
        wx.navigateTo({
          url: '/pages/house-list/house-list?type=auction-house',
          success: () => {
            console.log('成功跳转到法拍住宅列表');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      case 'auction-commercial':
        wx.navigateTo({
          url: '/pages/house-list/house-list?type=auction-commercial',
          success: () => {
            console.log('成功跳转到法拍商办列表');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      case 'special-assets':
        wx.navigateTo({
          url: '/pages/house-list/house-list?type=special-assets',
          success: () => {
            console.log('成功跳转到特殊资产列表');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      case 'premium-house':
        wx.navigateTo({
          url: '/pages/house-list/house-list?type=premium-house',
          success: () => {
            console.log('成功跳转到精选房源列表');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      case 'map':
        wx.showToast({
          title: '地图找房功能开发中',
          icon: 'none'
        });
        break;
      case 'buy-house':
        // 我要买房功能现在通过弹窗处理
        this.showBuyHouseModal();
        break;
      case 'sell-house':
        wx.navigateTo({
          url: '/pages/sell-house/sell-house',
          success: () => {
            console.log('成功跳转到卖房页面');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      case 'live-stream':
        wx.showToast({
          title: '直播看房功能开发中',
          icon: 'none'
        });
        break;
      case 'service':
        wx.showToast({
          title: '服务流程功能开发中',
          icon: 'none'
        });
        break;
      case 'calculator':
        wx.navigateTo({
          url: '/pages/calculator/calculator'
        });
        break;
      case 'transfer':
        wx.showToast({
          title: '装修服务功能开发中',
          icon: 'none'
        });
        break;
      case 'cooperation':
        wx.navigateTo({
          url: '/pages/cooperation/cooperation',
          success: () => {
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      default:
        wx.showToast({
          title: '直播看房功能开发中',
          icon: 'none'
        });
    }
  },

  /**
   * 搜索输入处理
   */
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  /**
   * 搜索确认处理
   */
  onSearchConfirm(e) {
    const searchValue = e.detail.value.trim();
    if (searchValue) {
      console.log('搜索内容:', searchValue);
      // 跳转到房源列表页面，并传递搜索关键词
      wx.navigateTo({
        url: `/pages/house-list/house-list?search=${encodeURIComponent(searchValue)}`,
        success: () => {
          console.log('跳转到搜索结果页面');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '搜索失败',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 定位按钮点击处理
   */
  onLocationTap() {
    wx.getLocation({
      type: 'wgs84',
      success: (res) => {
        console.log('当前位置:', res);
        wx.showToast({
          title: '定位成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('定位失败:', err);
        wx.showToast({
          title: '定位失败，请检查权限',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 加载统计数据
   */
  loadStatsData() {
    util.showLoading('加载中...')

    // 调用真实API获取统计数据
    Promise.all([
      api.get(`${api.API.HOUSE_BY_TYPE}?houseType=住宅`).catch(() => ({code: 0, data: []})), // 住宅房源
      api.get(`${api.API.HOUSE_BY_TYPE}?houseType=商办`).catch(() => ({code: 0, data: []})), // 商办房源
      api.get(`${api.API.HOUSE_BY_SPECIAL}/1`).catch(() => ({code: 0, data: []})), // 特殊资产
      api.get(`${api.API.HOUSE_BY_FEATURED}/1`).catch(() => ({code: 0, data: []})) // 精选房源
    ])
    .then(([houseRes, commercialRes, specialRes, featuredRes]) => {
      console.log('统计API响应:', {houseRes, commercialRes, specialRes, featuredRes});

      // 合并所有房源数据
      const allHouses = [];

      if (houseRes.code === 200 && Array.isArray(houseRes.data)) {
        allHouses.push(...houseRes.data);
      }
      if (commercialRes.code === 200 && Array.isArray(commercialRes.data)) {
        allHouses.push(...commercialRes.data);
      }
      if (specialRes.code === 200 && Array.isArray(specialRes.data)) {
        allHouses.push(...specialRes.data);
      }
      if (featuredRes.code === 200 && Array.isArray(featuredRes.data)) {
        allHouses.push(...featuredRes.data);
      }

      console.log('合并后的房源数据:', allHouses);

      // 计算统计数据
      const now = new Date();

      // 今日新增（模拟）
      const todayNew = Math.floor(Math.random() * 20) + 10;

      // 拍卖中的房源
      const auctioning = allHouses.filter(house => {
        if (!house.startTime || !house.endTime) return false;
        const startTime = new Date(house.startTime);
        const endTime = new Date(house.endTime);
        return now >= startTime && now <= endTime;
      }).length;

      // 即将拍卖的房源
      const upcoming = allHouses.filter(house => {
        if (!house.startTime) return false;
        const startTime = new Date(house.startTime);
        return now < startTime;
      }).length;

      // 平均价格（起拍价）
      const validPrices = allHouses.filter(house => house.startingPrice && house.startingPrice > 0);
      const totalPrice = validPrices.reduce((sum, house) => sum + house.startingPrice, 0);
      const avgPrice = validPrices.length > 0 ? Math.floor(totalPrice / validPrices.length / 10000) : 7650;

      this.setData({
        statsData: {
          todayNew: todayNew,
          auctioning: auctioning || 116, // 如果没有数据使用默认值
          upcoming: upcoming || 2068,
          avgPrice: avgPrice || 7650
        }
      });

      console.log('统计数据设置完成:', this.data.statsData);
      util.hideLoading();
    })
    .catch(err => {
      console.error('加载统计数据失败:', err);

      // API失败时使用模拟数据
      this.setData({
        statsData: {
          todayNew: Math.floor(Math.random() * 100) + 50,
          auctioning: Math.floor(Math.random() * 200) + 100,
          upcoming: Math.floor(Math.random() * 1000) + 2000,
          avgPrice: Math.floor(Math.random() * 2000) + 7000
        }
      });

      util.hideLoading();
    });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadStatsData();
    this.loadHouseList(true);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1500);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadHouseList();
    }
  },

  /**
   * 显示我要买房弹窗
   */
  showBuyHouseModal() {
    console.log('显示我要买房弹窗');
    this.setData({
      showBuyHouseModal: true
    });
  },

  /**
   * 隐藏我要买房弹窗
   */
  hideBuyHouseModal() {
    console.log('隐藏我要买房弹窗');
    this.setData({
      showBuyHouseModal: false
    });
  },

  /**
   * 阻止弹窗内容区域点击关闭
   */
  preventClose() {
    // 空方法，阻止事件冒泡
  },

  /**
   * 手机号登录方式1
   */
  onPhoneLogin1() {
    console.log('选择手机号登录方式1');
    this.hideBuyHouseModal();
    wx.showToast({
      title: '手机号登录功能开发中',
      icon: 'none'
    });
  },

  /**
   * 手机号登录方式2
   */
  onPhoneLogin2() {
    console.log('选择手机号登录方式2');
    this.hideBuyHouseModal();
    wx.showToast({
      title: '手机号登录功能开发中',
      icon: 'none'
    });
  },

  /**
   * 统计卡片点击处理
   */
  onCard1Tap() {
    console.log('点击了卡片1');
    wx.showToast({
      title: '卡片1功能开发中',
      icon: 'none'
    });
  },

  onCard2Tap() {
    console.log('点击了卡片2');
    wx.showToast({
      title: '卡片2功能开发中',
      icon: 'none'
    });
  },

  onCard3Tap() {
    console.log('点击了卡片3');
    wx.showToast({
      title: '卡片3功能开发中',
      icon: 'none'
    });
  },

  onCard4Tap() {
    console.log('点击了卡片4');
    wx.showToast({
      title: '卡片4功能开发中',
      icon: 'none'
    });
  },

  /**
   * 加载轮播图数据
   */
  loadCarousels() {
    console.log('开始加载轮播图数据')

    carouselService.getCarousels()
      .then(carousels => {
        console.log('轮播图数据加载成功:', carousels)

        // 格式化轮播图数据
        const formattedCarousels = carouselService.formatCarousels(carousels)

        this.setData({
          carousels: formattedCarousels
        })

        // 预加载轮播图
        if (formattedCarousels.length > 0) {
          carouselService.preloadCarouselImages(formattedCarousels)
        }
      })
      .catch(error => {
        console.error('轮播图数据加载失败:', error)
        // 不显示错误提示，静默失败
      })
  },

  /**
   * 轮播图点击处理
   */
  onCarouselTap(e) {
    const carousel = e.currentTarget.dataset.carousel
    console.log('轮播图点击:', carousel)

    if (carousel) {
      carouselService.handleCarouselClick(carousel)
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '锦绣资产 - 创造资产价值',
      path: '/pages/index/index'
    };
  },

  /**
   * 初始化导航栏
   */
  initNavBar() {
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navBarHeight: systemInfo.statusBarHeight + 88
    });
  },

  /**
   * 加载选中的城市
   */
  loadSelectedCity() {
    const selectedCity = wx.getStorageSync('selectedCity') || '重庆市区';
    this.setData({
      selectedCity: selectedCity
    });
  },

  /**
   * 位置选择点击事件
   */
  onLocationSelect() {
    wx.navigateTo({
      url: '/pages/city-select/index',
      success: () => {
        console.log('跳转到城市选择页面');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 加载房源列表
   */
  loadHouseList(refresh = false) {
    if (this.data.loading) return;

    this.setData({
      loading: true
    });

    if (refresh) {
      this.setData({
        currentPage: 1,
        houseList: [],
        hasMore: true
      });
    }

    // 构建API参数
    const params = {
      page: this.data.currentPage,
      pageSize: this.data.pageSize,
      houseType: this.getHouseTypeFromFilter(),
      region: this.data.regionFilter !== 'all' ? this.data.regionFilter : '',
      minPrice: this.getMinPriceFromFilter(),
      maxPrice: this.getMaxPriceFromFilter(),
      minArea: this.getMinAreaFromFilter(),
      maxArea: this.getMaxAreaFromFilter(),
      auctionType: this.data.auctionType !== 'all' ? this.data.auctionType : '',
      auctionStatus: this.data.auctionStatus !== 'all' ? this.data.auctionStatus : '',
      sortBy: this.getSortByFromFilter(),
      sortOrder: this.getSortOrderFromFilter()
    };

    // 调用真实API
    this.callHouseListAPI(params);
  },

  /**
   * 调用房源列表API
   */
  callHouseListAPI(params) {
    // 根据房源类型选择不同的API
    let apiUrl = '';
    if (this.data.filterType === 'house' || this.data.filterType === 'all') {
      apiUrl = `${api.API.HOUSE_BY_TYPE}?houseType=住宅`;
    } else if (this.data.filterType === 'commercial') {
      apiUrl = `${api.API.HOUSE_BY_TYPE}?houseType=商办`;
    } else if (this.data.filterType === 'special') {
      apiUrl = `${api.API.HOUSE_BY_SPECIAL}/1`;
    } else if (this.data.filterType === 'premium') {
      apiUrl = `${api.API.HOUSE_BY_FEATURED}/1`;
    } else {
      // 默认获取所有房源
      apiUrl = api.API.HOUSE_LIST || `${api.API.HOUSE_BY_TYPE}?houseType=住宅`;
    }

    // 添加分页和筛选参数
    const queryParams = new URLSearchParams();
    queryParams.append('page', params.page);
    queryParams.append('pageSize', params.pageSize);

    if (params.region) queryParams.append('region', params.region);
    if (params.minPrice) queryParams.append('minPrice', params.minPrice);
    if (params.maxPrice) queryParams.append('maxPrice', params.maxPrice);
    if (params.minArea) queryParams.append('minArea', params.minArea);
    if (params.maxArea) queryParams.append('maxArea', params.maxArea);
    if (params.auctionType) queryParams.append('auctionType', params.auctionType);
    if (params.auctionStatus) queryParams.append('auctionStatus', params.auctionStatus);
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const fullUrl = `${apiUrl}${apiUrl.includes('?') ? '&' : '?'}${queryParams.toString()}`;

    console.log('调用房源列表API:', fullUrl);

    api.get(fullUrl)
      .then(response => {
        console.log('房源列表API响应:', response);

        if (response.code === 200 && Array.isArray(response.data)) {
          const formattedHouseList = this.formatHouseListData(response.data);

          let newHouseList = [];
          if (this.data.currentPage === 1) {
            newHouseList = formattedHouseList;
          } else {
            newHouseList = [...this.data.houseList, ...formattedHouseList];
          }

          this.setData({
            houseList: newHouseList,
            hasMore: formattedHouseList.length >= this.data.pageSize,
            currentPage: this.data.currentPage + 1,
            loading: false
          });
        } else {
          console.warn('房源列表API返回数据格式异常:', response);
          this.handleAPIError();
        }
      })
      .catch(error => {
        console.error('房源列表API调用失败:', error);
        this.handleAPIError();
      });
  },

  /**
   * 处理API错误
   */
  handleAPIError() {
    // API失败时使用模拟数据
    const mockData = this.generateMockHouseData({
      page: this.data.currentPage,
      pageSize: this.data.pageSize
    });

    let newHouseList = [];
    if (this.data.currentPage === 1) {
      newHouseList = mockData.list;
    } else {
      newHouseList = [...this.data.houseList, ...mockData.list];
    }

    this.setData({
      houseList: newHouseList,
      hasMore: mockData.hasMore,
      currentPage: this.data.currentPage + 1,
      loading: false
    });

    wx.showToast({
      title: '网络异常，显示模拟数据',
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 格式化房源列表数据
   */
  formatHouseListData(rawData) {
    return rawData.map(item => ({
      id: item.id || item.houseId,
      title: item.title || item.houseName || item.name || '房源信息',
      startingPrice: this.formatPrice(item.startingPrice || item.price || item.startPrice),
      area: item.area || item.buildingArea || item.houseArea || '未知',
      location: item.location || item.address || item.region || '位置未知',
      status: this.formatStatus(item.status || item.auctionStatus),
      auctionTime: this.formatAuctionTime(item.startTime || item.auctionTime),
      imageUrl: item.imageUrl || item.image || item.photo || '../../images/default-house.png',
      unitPrice: this.calculateUnitPrice(item.startingPrice || item.price, item.area || item.buildingArea),
      houseType: item.houseType || item.type || '住宅',
      auctionType: item.auctionType || '一拍',
      endTime: item.endTime,
      court: item.court || item.courtName,
      evaluationPrice: item.evaluationPrice || item.evalPrice
    }));
  },

  /**
   * 格式化价格
   */
  formatPrice(price) {
    if (!price) return '0';
    const numPrice = parseFloat(price);
    if (numPrice >= 10000) {
      return (numPrice / 10000).toFixed(0);
    }
    return numPrice.toFixed(0);
  },

  /**
   * 格式化状态
   */
  formatStatus(status) {
    const statusMap = {
      '0': '未起拍',
      '1': '竞拍中',
      '2': '已成交',
      '3': '已结束',
      'pending': '未起拍',
      'ongoing': '竞拍中',
      'completed': '已成交',
      'ended': '已结束'
    };
    return statusMap[status] || status || '拍卖中';
  },

  /**
   * 格式化拍卖时间
   */
  formatAuctionTime(timeStr) {
    if (!timeStr) return '时间待定';
    try {
      const date = new Date(timeStr);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hour = date.getHours().toString().padStart(2, '0');
      const minute = date.getMinutes().toString().padStart(2, '0');
      return `${month}-${day} ${hour}:${minute}`;
    } catch (error) {
      return timeStr;
    }
  },

  /**
   * 计算单价
   */
  calculateUnitPrice(totalPrice, area) {
    if (!totalPrice || !area) return 0;
    const price = parseFloat(totalPrice);
    const areaNum = parseFloat(area);
    if (areaNum > 0) {
      return Math.round((price * 10000) / areaNum);
    }
    return 0;
  },

  /**
   * 从筛选条件获取房源类型
   */
  getHouseTypeFromFilter() {
    const typeMap = {
      'all': '',
      'house': '住宅',
      'commercial': '商办',
      'special': '特殊资产',
      'premium': '精选房源'
    };
    return typeMap[this.data.filterType] || '';
  },

  /**
   * 从价格筛选获取最低价格
   */
  getMinPriceFromFilter() {
    const priceFilter = this.data.priceFilter;
    if (priceFilter === 'all') return '';

    if (priceFilter.startsWith('custom-')) {
      const parts = priceFilter.split('-');
      return parts[1] || '';
    }

    const priceMap = {
      '0-50': '0',
      '50-100': '50',
      '100-150': '100',
      '150-200': '150',
      '200-300': '200',
      '300-400': '300',
      '400-500': '400',
      '500-700': '500',
      '700-1000': '700',
      '1000+': '1000'
    };
    return priceMap[priceFilter] || '';
  },

  /**
   * 从价格筛选获取最高价格
   */
  getMaxPriceFromFilter() {
    const priceFilter = this.data.priceFilter;
    if (priceFilter === 'all') return '';

    if (priceFilter.startsWith('custom-')) {
      const parts = priceFilter.split('-');
      return parts[2] || '';
    }

    const priceMap = {
      '0-50': '50',
      '50-100': '100',
      '100-150': '150',
      '150-200': '200',
      '200-300': '300',
      '300-400': '400',
      '400-500': '500',
      '500-700': '700',
      '700-1000': '1000',
      '1000+': ''
    };
    return priceMap[priceFilter] || '';
  },

  /**
   * 从面积筛选获取最小面积
   */
  getMinAreaFromFilter() {
    const areaFilter = this.data.areaFilter;
    if (areaFilter === 'all') return '';

    const areaMap = {
      '0-50': '0',
      '50-70': '50',
      '70-90': '70',
      '90-120': '90',
      '120-150': '120',
      '150-200': '150',
      '200-300': '200',
      '300+': '300'
    };
    return areaMap[areaFilter] || '';
  },

  /**
   * 从面积筛选获取最大面积
   */
  getMaxAreaFromFilter() {
    const areaFilter = this.data.areaFilter;
    if (areaFilter === 'all') return '';

    const areaMap = {
      '0-50': '50',
      '50-70': '70',
      '70-90': '90',
      '90-120': '120',
      '120-150': '150',
      '150-200': '200',
      '200-300': '300',
      '300+': ''
    };
    return areaMap[areaFilter] || '';
  },

  /**
   * 从排序筛选获取排序字段
   */
  getSortByFromFilter() {
    const sortType = this.data.sortType;
    const sortMap = {
      'smart': '',
      'latest': 'createTime',
      'price-asc': 'startingPrice',
      'price-desc': 'startingPrice',
      'unit-price-asc': 'unitPrice',
      'unit-price-desc': 'unitPrice',
      'area-desc': 'area',
      'area-asc': 'area',
      'time-asc': 'startTime'
    };
    return sortMap[sortType] || '';
  },

  /**
   * 从排序筛选获取排序顺序
   */
  getSortOrderFromFilter() {
    const sortType = this.data.sortType;
    const orderMap = {
      'smart': '',
      'latest': 'desc',
      'price-asc': 'asc',
      'price-desc': 'desc',
      'unit-price-asc': 'asc',
      'unit-price-desc': 'desc',
      'area-desc': 'desc',
      'area-asc': 'asc',
      'time-asc': 'asc'
    };
    return orderMap[sortType] || '';
  },

  /**
   * 房源类型筛选点击事件 - 跳转到房源列表页
   */
  onHouseTypeFilter(e) {
    const type = e.currentTarget.dataset.type;
    console.log('房源类型筛选点击:', type);

    // 构建跳转参数
    const params = {
      filterType: type
    };

    // 跳转到房源列表页面
    wx.navigateTo({
      url: `/pages/house-list/house-list?filterType=${type}`,
      success: () => {
        console.log('成功跳转到房源列表页面，筛选类型:', type);
      },
      fail: (err) => {
        console.error('跳转房源列表页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 生成模拟房源数据
   */
  generateMockHouseData(params) {
    const mockHouses = [];
    const titles = [
      '万科城市花园三室两厅',
      '恒大绿洲精装修住宅',
      '保利花园景观房',
      '龙湖春森彼岸',
      '融创玖玺台',
      '华润二十四城',
      '金科天籁城',
      '中海寰宇天下'
    ];

    const locations = ['渝中区', '江北区', '南岸区', '沙坪坝区', '九龙坡区', '大渡口区'];
    const statuses = ['拍卖中', '即将开拍', '已结束'];

    for (let i = 0; i < params.pageSize; i++) {
      const id = (params.page - 1) * params.pageSize + i + 1;
      mockHouses.push({
        id: id,
        title: titles[Math.floor(Math.random() * titles.length)],
        startingPrice: (Math.random() * 200 + 50).toFixed(0),
        area: (Math.random() * 100 + 50).toFixed(0),
        location: locations[Math.floor(Math.random() * locations.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)],
        auctionTime: '2024-12-' + (Math.floor(Math.random() * 28) + 1).toString().padStart(2, '0'),
        imageUrl: '../../images/default-house.png'
      });
    }

    return {
      list: mockHouses,
      hasMore: params.page < 5 // 模拟5页数据
    };
  },

  /**
   * 更新区域选项
   */
  updateRegionOptions() {
    const selectedCity = this.data.selectedCity;
    let regionOptions = [];

    if (selectedCity === '重庆市区') {
      regionOptions = ['渝中区', '江北区', '南岸区', '沙坪坝区', '九龙坡区', '大渡口区', '渝北区', '巴南区'];
    } else if (selectedCity === '重庆郊区') {
      regionOptions = ['北碚区', '綦江区', '大足区', '璧山区', '铜梁区', '潼南区', '荣昌区', '梁平区'];
    } else {
      // 其他城市的默认区域选项
      regionOptions = ['市中心', '新区', '开发区', '高新区'];
    }

    this.setData({
      currentRegionOptions: regionOptions
    });
  },

  /**
   * 显示区域筛选面板
   */
  showRegionFilter() {
    this.setData({
      showRegionPanel: true,
      tempRegionFilter: this.data.regionFilter
    });
  },

  /**
   * 隐藏区域筛选面板
   */
  hideRegionFilter() {
    this.setData({
      showRegionPanel: false
    });
  },

  /**
   * 临时区域筛选点击事件
   */
  onTempRegionFilterTap(e) {
    const region = e.currentTarget.dataset.region;
    this.setData({
      tempRegionFilter: region
    });
  },

  /**
   * 重置区域筛选
   */
  resetRegionFilter() {
    this.setData({
      tempRegionFilter: 'all'
    });
  },

  /**
   * 确认区域筛选
   */
  confirmRegionFilter() {
    const regionText = this.data.tempRegionFilter === 'all' ? '区域' : this.data.tempRegionFilter;
    this.setData({
      regionFilter: this.data.tempRegionFilter,
      regionFilterText: regionText,
      showRegionPanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 显示面积筛选面板
   */
  showAreaFilter() {
    this.setData({
      showAreaPanel: true,
      tempAreaFilter: this.data.areaFilter
    });
  },

  /**
   * 隐藏面积筛选面板
   */
  hideAreaFilter() {
    this.setData({
      showAreaPanel: false
    });
  },

  /**
   * 临时面积筛选点击事件
   */
  onTempAreaFilterTap(e) {
    const area = e.currentTarget.dataset.area;
    this.setData({
      tempAreaFilter: area
    });
  },

  /**
   * 重置面积筛选
   */
  resetAreaFilter() {
    this.setData({
      tempAreaFilter: 'all'
    });
  },

  /**
   * 确认面积筛选
   */
  confirmAreaFilter() {
    const areaText = this.data.tempAreaFilter === 'all' ? '面积' : this.getAreaText(this.data.tempAreaFilter);
    this.setData({
      areaFilter: this.data.tempAreaFilter,
      areaFilterText: areaText,
      showAreaPanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 获取面积文本
   */
  getAreaText(areaFilter) {
    const areaMap = {
      '0-50': '50㎡以下',
      '50-70': '50~70㎡',
      '70-90': '70~90㎡',
      '90-120': '90~120㎡',
      '120-150': '120~150㎡',
      '150-200': '150~200㎡',
      '200-300': '200~300㎡',
      '300+': '300㎡以上'
    };
    return areaMap[areaFilter] || '面积';
  },

  /**
   * 显示价格筛选面板
   */
  showPriceFilter() {
    this.setData({
      showPricePanel: true,
      tempPriceFilter: this.data.priceFilter
    });
  },

  /**
   * 隐藏价格筛选面板
   */
  hidePriceFilter() {
    this.setData({
      showPricePanel: false
    });
  },

  /**
   * 临时价格筛选点击事件
   */
  onTempPriceFilterTap(e) {
    const price = e.currentTarget.dataset.price;
    this.setData({
      tempPriceFilter: price
    });
  },

  /**
   * 最低价格输入
   */
  onMinPriceInput(e) {
    this.setData({
      customMinPrice: e.detail.value
    });
  },

  /**
   * 最高价格输入
   */
  onMaxPriceInput(e) {
    this.setData({
      customMaxPrice: e.detail.value
    });
  },

  /**
   * 重置价格筛选
   */
  resetPriceFilter() {
    this.setData({
      tempPriceFilter: 'all',
      customMinPrice: '',
      customMaxPrice: ''
    });
  },

  /**
   * 确认价格筛选
   */
  confirmPriceFilter() {
    let priceText = '价格';
    let priceFilter = this.data.tempPriceFilter;

    // 如果有自定义价格，优先使用自定义价格
    if (this.data.customMinPrice || this.data.customMaxPrice) {
      const minPrice = this.data.customMinPrice || '0';
      const maxPrice = this.data.customMaxPrice || '不限';
      priceText = `${minPrice}-${maxPrice}万`;
      priceFilter = `custom-${minPrice}-${maxPrice}`;
    } else if (priceFilter !== 'all') {
      priceText = this.getPriceText(priceFilter);
    }

    this.setData({
      priceFilter: priceFilter,
      priceFilterText: priceText,
      showPricePanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 获取价格文本
   */
  getPriceText(priceFilter) {
    const priceMap = {
      '0-50': '50万以下',
      '50-100': '50~100万',
      '100-150': '100~150万',
      '150-200': '150~200万',
      '200-300': '200~300万',
      '300-400': '300~400万',
      '400-500': '400~500万',
      '500-700': '500~700万',
      '700-1000': '700~1000万',
      '1000+': '1000万以上'
    };
    return priceMap[priceFilter] || '价格';
  },

  /**
   * 显示更多筛选面板
   */
  showMoreFilter() {
    this.setData({
      showMorePanel: true,
      tempAuctionType: this.data.auctionType,
      tempAuctionStatus: this.data.auctionStatus
    });
  },

  /**
   * 隐藏更多筛选面板
   */
  hideMoreFilter() {
    this.setData({
      showMorePanel: false
    });
  },

  /**
   * 临时拍卖方式筛选点击事件
   */
  onTempAuctionTypeTap(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      tempAuctionType: type
    });
  },

  /**
   * 临时拍卖状态筛选点击事件
   */
  onTempAuctionStatusTap(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      tempAuctionStatus: status
    });
  },

  /**
   * 重置更多筛选
   */
  resetMoreFilter() {
    this.setData({
      tempAuctionType: 'all',
      tempAuctionStatus: 'all'
    });
  },

  /**
   * 确认更多筛选
   */
  confirmMoreFilter() {
    this.setData({
      auctionType: this.data.tempAuctionType,
      auctionStatus: this.data.tempAuctionStatus,
      showMorePanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 显示排序筛选面板
   */
  showSortFilter() {
    this.setData({
      showSortPanel: true,
      tempSortType: this.data.sortType
    });
  },

  /**
   * 隐藏排序筛选面板
   */
  hideSortFilter() {
    this.setData({
      showSortPanel: false
    });
  },

  /**
   * 临时排序类型点击事件
   */
  onTempSortTypeTap(e) {
    const sort = e.currentTarget.dataset.sort;
    this.setData({
      tempSortType: sort
    });
  },

  /**
   * 重置排序筛选
   */
  resetSortFilter() {
    this.setData({
      tempSortType: 'smart'
    });
  },

  /**
   * 确认排序筛选
   */
  confirmSortFilter() {
    const sortText = this.getSortText(this.data.tempSortType);
    this.setData({
      sortType: this.data.tempSortType,
      sortFilterText: sortText,
      showSortPanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 获取排序文本
   */
  getSortText(sortType) {
    const sortMap = {
      'smart': '智能排序',
      'latest': '最新发布',
      'price-asc': '总价从低到高',
      'price-desc': '总价从高到低',
      'unit-price-asc': '单价从低到高',
      'unit-price-desc': '单价从高到低',
      'area-desc': '面积从大到小',
      'area-asc': '面积从小到大',
      'time-asc': '起拍时间由近到远'
    };
    return sortMap[sortType] || '智能排序';
  },

  /**
   * 房源项点击事件
   */
  onHouseItemTap(e) {
    const house = e.currentTarget.dataset.house;
    wx.navigateTo({
      url: `/pages/property-detail/property-detail?id=${house.id}`,
      success: () => {
        console.log('跳转到房源详情页面');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  }
});
