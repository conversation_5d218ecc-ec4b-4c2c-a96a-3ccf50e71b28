server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: cos-storage-service
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************
    username: root
    password: xiaoma
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 配置检测连接是否有效
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    # 设置空值的时候不序列化
    default-property-inclusion: NON_NULL

# 腾讯云COS配置
tencent:
  cos:
    # 腾讯云API密钥ID
    secret-id: AKIDra7TcYQ5YptkPr7A0jFXbT8ZnnZAYkkY
    # 腾讯云API密钥Key
    secret-key: e4GG4fKsFQm85sRmwueBdswmPrDMK6g2
    # 存储桶名称
    bucket-name: house-1367288704
    # 地域信息
    region: ap-guangzhou
    # CDN加速域名
    cdn-domain: cos.cqjxzc.com.cn
    # 是否启用CDN加速，默认为true
    enable-cdn: true
    # 连接超时时间（毫秒）
    connection-timeout: 30000
    # 读取超时时间（毫秒）
    socket-timeout: 30000

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-version: true
    enable-reload-cache-parameter: true
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: true
    enable-request-cache: true
    enable-host: false
    enable-host-text: 
    enable-home-custom: true
    home-custom-path: classpath:markdown/home.md
    enable-search: true
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: Apache License 2.0 | Copyright 2019-Knife4j

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    # 是否开启自动驼峰命名规则映射
    map-underscore-to-camel-case: true
    # 如果查询结果中包含空值的列，则 MyBatis 在映射的时候，不会映射这个字段
    call-setters-on-nulls: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 搜索指定包别名
  type-aliases-package: com.example.cos.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  # 全局配置
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      # 字段策略 IGNORED:"忽略判断", NOT_NULL:"非 NULL 判断"), NOT_EMPTY:"非空判断"
      field-strategy: NOT_NULL
      # 驼峰下划线转换
      column-underline: true
      # 逻辑删除配置
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false

logging:
  level:
    com.example: DEBUG
    com.qcloud.cos: INFO
    # MyBatis SQL日志
    com.example.cos.mapper: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# 微信小程序配置
wechat:
  miniapp:
    # 小程序AppID
    app-id: wx3212d75cbb7f7e10
    # 小程序AppSecret
    app-secret: 5a903fae7b0bae12c06da175d222aa35 
    # 微信API基础URL
    api-base-url: https://api.weixin.qq.com
    # 获取access_token的URL
    token-url: /cgi-bin/token
    # 登录凭证校验URL
    jscode2session-url: /sns/jscode2session
    # 获取用户手机号URL
    get-phone-number-url: /wxa/business/getuserphonenumber
