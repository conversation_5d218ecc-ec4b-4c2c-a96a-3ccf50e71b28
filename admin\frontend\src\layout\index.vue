<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
      <div class="logo">
        <img src="/logo.png" alt="Logo" v-if="!isCollapse" />
        <span v-if="!isCollapse">锦绣资产</span>
        <img src="/logo-mini.png" alt="Logo" v-else />
      </div>
      
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        router
        background-color="#fff"
        text-color="#606266"
        active-text-color="#FFB6C1"
      >
        <el-menu-item index="/admin/dashboard" v-permission="'read'">
          <el-icon><Odometer /></el-icon>
          <template #title>仪表板</template>
        </el-menu-item>

        <el-menu-item index="/admin/houses" v-permission="'house_manage'">
          <el-icon><House /></el-icon>
          <template #title>房源管理</template>
        </el-menu-item>

        <el-menu-item index="/admin/users" v-permission="'user_manage'">
          <el-icon><User /></el-icon>
          <template #title>用户管理</template>
        </el-menu-item>

        <el-menu-item index="/admin/house-sales" v-permission="'house_sale_manage'">
          <el-icon><Sell /></el-icon>
          <template #title>卖房信息</template>
        </el-menu-item>

        <el-menu-item index="/admin/consultations" v-permission="'consultation_manage'">
          <el-icon><ChatDotRound /></el-icon>
          <template #title>合作咨询</template>
        </el-menu-item>

        <el-menu-item index="/admin/carousels" v-permission="'system_manage'">
          <el-icon><Picture /></el-icon>
          <template #title>轮播图管理</template>
        </el-menu-item>

        <el-menu-item index="/admin/admin-management">
          <el-icon><UserFilled /></el-icon>
          <template #title>管理员管理</template>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container class="main-container">
      <!-- 顶部导航栏 -->
      <el-header class="header">
        <div class="header-left">
          <el-button 
            type="text" 
            @click="toggleSidebar"
            class="collapse-btn"
          >
            <el-icon><Expand v-if="isCollapse" /><Fold v-else /></el-icon>
          </el-button>
          
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/admin/dashboard' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <div class="user-role-info">
            <el-tag :type="roleTagType" size="small">{{ currentUserRole }}</el-tag>
          </div>
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-avatar :size="32" src="/avatar.png" />
              <span class="username">{{ currentUsername }}</span>
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                <el-dropdown-item command="settings" v-if="permissionStore.isSuperAdmin">系统设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主内容 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { usePermissionStore } from '@/stores/permission'

const route = useRoute()
const router = useRouter()
const permissionStore = usePermissionStore()

const isCollapse = ref(false)
const currentUsername = ref('管理员')

const activeMenu = computed(() => route.path)

const currentPageTitle = computed(() => {
  return route.meta.title || '未知页面'
})

const currentUserRole = computed(() => {
  return permissionStore.getRoleDisplayName()
})

const roleTagType = computed(() => {
  if (permissionStore.isSuperAdmin) return 'danger'
  if (permissionStore.isAdmin) return 'warning'
  return 'info'
})

// 获取当前登录用户信息
onMounted(() => {
  const username = localStorage.getItem('username')
  if (username) {
    currentUsername.value = username
  }

  // 初始化权限信息
  if (!permissionStore.isInitialized) {
    permissionStore.initPermissions()
  }
})

const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人中心功能开发中')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中')
      break
    case 'logout':
      await handleLogout()
      break
  }
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 清除权限信息和本地存储
    permissionStore.clearPermissions()

    ElMessage.success('退出登录成功')

    // 跳转到登录页
    router.push('/login')
  } catch (error) {
    // 用户取消退出
  }
}


</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  display: flex;
}

.sidebar {
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  transition: width 0.3s;
  
  .logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #e6e6e6;
    
    img {
      height: 32px;
      margin-right: 8px;
    }
    
    span {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-color);
    }
  }
  
  .el-menu {
    border-right: none;
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 20px;
    
    .collapse-btn {
      font-size: 18px;
      color: #606266;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;

    .user-role-info {
      display: flex;
      align-items: center;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;

      .username {
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

.main-content {
  background-color: #f5f5f5;
  padding: 0;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .layout-container {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100% !important;
    height: auto;
    
    .el-menu {
      display: flex;
      overflow-x: auto;
    }
  }
  
  .header {
    padding: 0 15px;
    
    .header-left {
      gap: 10px;
    }
  }
}
</style>
