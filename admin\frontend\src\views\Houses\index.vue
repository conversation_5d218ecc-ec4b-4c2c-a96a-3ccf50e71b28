<template>
  <div class="app-container">
    <div class="page-header">
      <h2>房源管理</h2>
      <Permission permission="create">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增房源
        </el-button>
      </Permission>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
        <el-form-item label="房源标题" prop="title">
          <el-input
            v-model="queryParams.title"
            placeholder="请输入房源标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="拍卖状态" prop="auctionStatus">
          <el-select v-model="queryParams.auctionStatus" placeholder="请选择拍卖状态" clearable style="width: 150px">
            <el-option label="未开拍" :value="0" />
            <el-option label="一拍中" :value="1" />
            <el-option label="二拍中" :value="2" />
            <el-option label="变卖中" :value="3" />
            <el-option label="已结束" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="户型" prop="houseType">
          <el-input
            v-model="queryParams.houseType"
            placeholder="请输入户型"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="小区名称" prop="communityName">
          <el-input
            v-model="queryParams.communityName"
            placeholder="请输入小区名称"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="起拍价">
          <el-input
            v-model="queryParams.minStartingPrice"
            placeholder="最低起拍价"
            style="width: 120px"
            type="number"
          />
          <span style="margin: 0 10px">-</span>
          <el-input
            v-model="queryParams.maxStartingPrice"
            placeholder="最高起拍价"
            style="width: 120px"
            type="number"
          />
        </el-form-item>
        <el-form-item label="评估价">
          <el-input
            v-model="queryParams.minEvaluationPrice"
            placeholder="最低评估价"
            style="width: 120px"
            type="number"
          />
          <span style="margin: 0 10px">-</span>
          <el-input
            v-model="queryParams.maxEvaluationPrice"
            placeholder="最高评估价"
            style="width: 120px"
            type="number"
          />
        </el-form-item>
        <el-form-item label="竞价时间">
          <el-date-picker
            v-model="queryParams.startTimeFrom"
            type="datetime"
            placeholder="开始时间"
            style="width: 180px"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
          <span style="margin: 0 10px">-</span>
          <el-date-picker
            v-model="queryParams.startTimeTo"
            type="datetime"
            placeholder="结束时间"
            style="width: 180px"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="房屋类型" prop="houseCategory">
          <el-select v-model="queryParams.houseCategory" placeholder="请选择房屋类型" clearable style="width: 120px">
            <el-option label="住宅" :value="0" />
            <el-option label="商办" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="装修情况" prop="decoration">
          <el-select v-model="queryParams.decoration" placeholder="请选择装修情况" clearable style="width: 120px">
            <el-option label="毛坯" :value="0" />
            <el-option label="简装" :value="1" />
            <el-option label="精装" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否精选" prop="isSelected">
          <el-select v-model="queryParams.isSelected" placeholder="请选择" clearable style="width: 120px">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="特殊房屋" prop="isSpecial">
          <el-select v-model="queryParams.isSpecial" placeholder="请选择" clearable style="width: 120px">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <el-button type="success" @click="handleBatchExport" :disabled="!multipleSelection.length">
          <el-icon><Download /></el-icon>
          批量导出
        </el-button>
        <Permission permission="delete">
          <el-button type="danger" @click="handleBatchDelete" :disabled="!multipleSelection.length">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </Permission>
      </div>
      <div class="toolbar-right">
        <el-tooltip content="刷新" placement="top">
          <el-button circle @click="getList">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="houseList"
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="房源ID" prop="id" width="80" />
      <el-table-column label="房源标题" prop="title" min-width="200" show-overflow-tooltip />
      <el-table-column label="拍卖状态" prop="auctionStatus" width="100">
        <template #default="scope">
          <el-tag :type="getAuctionStatusType(scope.row.auctionStatus)">
            {{ getAuctionStatusText(scope.row.auctionStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="户型" prop="houseType" width="120" />
      <el-table-column label="小区名称" prop="communityName" width="150" show-overflow-tooltip />
      <el-table-column label="建筑面积" prop="buildingArea" width="100">
        <template #default="scope">
          {{ scope.row.buildingArea }}㎡
        </template>
      </el-table-column>
      <el-table-column label="起拍价" prop="startingPrice" width="120">
        <template #default="scope">
          <span class="price">¥{{ formatPrice(scope.row.startingPrice) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评估价" prop="evaluationPrice" width="120">
        <template #default="scope">
          <span class="price">¥{{ formatPrice(scope.row.evaluationPrice) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="折扣率" prop="discountRate" width="100">
        <template #default="scope">
          <span :class="{ 'discount-rate': scope.row.discountRate < 80 }">
            {{ scope.row.discountRate }}%
          </span>
        </template>
      </el-table-column>
      <el-table-column label="起拍时间" prop="startTime" width="180" />
      <el-table-column label="结束时间" prop="endTime" width="180" />
      <el-table-column label="房屋类型" prop="houseCategory" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.houseCategory === 0 ? 'success' : 'warning'">
            {{ scope.row.houseCategory === 0 ? '住宅' : '商办' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="装修情况" prop="decoration" width="100">
        <template #default="scope">
          {{ getDecorationText(scope.row.decoration) }}
        </template>
      </el-table-column>
      <el-table-column label="是否精选" prop="isSelected" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.isSelected"
            :active-value="1"
            :inactive-value="0"
            active-color="#67c23a"
            inactive-color="#dcdfe6"
            @change="handleToggleSelected(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="特殊房屋" prop="isSpecial" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.isSpecial"
            :active-value="1"
            :inactive-value="0"
            active-color="#ff4949"
            inactive-color="#dcdfe6"
            @change="handleToggleSpecial(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="240">
        <template #default="scope">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <Permission permission="update">
              <el-button type="success" size="small" @click="handleUpdate(scope.row)">
                编辑
              </el-button>
            </Permission>
            <Permission permission="delete">
              <el-button type="danger" size="small" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </Permission>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="800px"
      :before-close="handleDialogClose"
    >
      <HouseForm
        ref="houseFormRef"
        :house-data="currentHouse"
        :is-edit="isEdit"
        @submit="handleFormSubmit"
        @cancel="handleDialogClose"
      />
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      title="房源详情"
      v-model="viewDialogVisible"
      width="900px"
    >
      <HouseDetail :house-data="currentHouse" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getHouseList, createHouse, updateHouse, deleteHouse } from '@/api/house'
import HouseForm from './components/HouseForm.vue'
import HouseDetail from './components/HouseDetail.vue'

// 响应式数据
const loading = ref(false)
const houseList = ref([])
const total = ref(0)
const multipleSelection = ref([])
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const isEdit = ref(false)
const currentHouse = ref({})
const houseFormRef = ref()

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  title: '',
  auctionStatus: '',
  houseType: '',
  communityName: '',
  minStartingPrice: '',
  maxStartingPrice: '',
  minEvaluationPrice: '',
  maxEvaluationPrice: '',
  startTimeFrom: '',
  startTimeTo: '',
  stairsType: '',
  propertyType: '',
  decoration: '',
  houseCategory: '',
  isSelected: '',
  isSpecial: '',
  constructionYearFrom: '',
  constructionYearTo: '',
  minBuildingArea: '',
  maxBuildingArea: '',
  tags: ''
})

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑房源' : '新增房源'
})

// 获取房源列表
const getList = async () => {
  loading.value = true
  try {
    const response = await getHouseList(queryParams)
    houseList.value = response.data.records || []
    total.value = response.data.total || 0
  } catch (error) {
    ElMessage.error('获取房源列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    title: '',
    auctionStatus: '',
    houseType: '',
    communityName: '',
    minStartingPrice: '',
    maxStartingPrice: '',
    minEvaluationPrice: '',
    maxEvaluationPrice: '',
    startTimeFrom: '',
    startTimeTo: '',
    stairsType: '',
    propertyType: '',
    decoration: '',
    houseCategory: '',
    isSelected: '',
    isSpecial: '',
    constructionYearFrom: '',
    constructionYearTo: '',
    minBuildingArea: '',
    maxBuildingArea: '',
    tags: ''
  })
  getList()
}

// 分页处理
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 多选处理
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

// 新增
const handleAdd = () => {
  // 完全重置表单数据
  currentHouse.value = {
    id: null,
    auctionType: '',
    title: '',
    startingPrice: null,
    evaluationPrice: null,
    priceIncrement: null,
    biddingCycle: null,
    evaluationReport: '',
    executionOrder: '',
    propertyReport: '',
    mapInfo: '',
    situationSurvey: '',
    startTime: '',
    endTime: '',
    detailImageList: '',
    houseType: '',
    sourceUrl: '',
    isSpecial: 0,
    isFeatured: 0
  }
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑
const handleUpdate = (row) => {
  currentHouse.value = { ...row }
  isEdit.value = true
  dialogVisible.value = true
}

// 查看详情
const handleView = (row) => {
  currentHouse.value = { ...row }
  viewDialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除房源"${row.communityName}"吗？`,
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteHouse(row.id)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的${multipleSelection.value.length}条记录吗？`,
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量删除选中的房源
    const deletePromises = multipleSelection.value.map(item => deleteHouse(item.id))
    await Promise.all(deletePromises)

    ElMessage.success('批量删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 批量导出
const handleBatchExport = () => {
  try {
    // 准备导出数据
    const exportData = multipleSelection.value.map(item => ({
      '房源ID': item.id,
      '房源标题': item.title,
      '拍卖类型': item.auctionType,
      '房源类型': item.houseType,
      '起拍价': item.startingPrice,
      '评估价': item.evaluationPrice,
      '加价幅度': item.priceIncrement,
      '竞价周期': item.biddingCycle + '天',
      '竞价开始时间': item.startTime,
      '竞价结束时间': item.endTime,
      '特殊商品': item.isSpecial === 1 ? '是' : '否',
      '精选商品': item.isFeatured === 1 ? '是' : '否',
      '房屋来源链接': item.sourceUrl || '无'
    }))

    // 转换为CSV格式
    const headers = Object.keys(exportData[0])
    const csvContent = [
      headers.join(','),
      ...exportData.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n')

    // 创建下载链接
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `房源数据_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 表单提交
const handleFormSubmit = async (formData) => {
  try {
    if (isEdit.value) {
      await updateHouse(formData)
      ElMessage.success('更新成功')
    } else {
      await createHouse(formData)
      ElMessage.success('新增成功')
    }
    
    dialogVisible.value = false
    getList()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '新增失败')
  }
}

// 对话框关闭处理
const handleDialogClose = () => {
  dialogVisible.value = false
  currentHouse.value = {}
}

// 来源跳转
const handleSourceJump = (sourceUrl) => {
  if (sourceUrl) {
    window.open(sourceUrl, '_blank')
  } else {
    ElMessage.warning('该房源没有来源链接')
  }
}

// 切换特殊商品状态
const handleToggleSpecial = async (row) => {
  const originalStatus = row.isSpecial === 1 ? 0 : 1 // 获取切换前的状态
  try {
    const updatedData = { ...row, isSpecial: row.isSpecial }

    await updateHouse(updatedData)
    ElMessage.success(`${row.isSpecial === 1 ? '设为特殊商品' : '取消特殊商品'}成功`)
  } catch (error) {
    // 如果更新失败，恢复原状态
    row.isSpecial = originalStatus
    ElMessage.error('操作失败')
  }
}

// 切换精选商品状态
const handleToggleFeatured = async (row) => {
  const originalStatus = row.isFeatured === 1 ? 0 : 1 // 获取切换前的状态
  try {
    const updatedData = { ...row, isFeatured: row.isFeatured }

    await updateHouse(updatedData)
    ElMessage.success(`${row.isFeatured === 1 ? '设为精选商品' : '取消精选商品'}成功`)
  } catch (error) {
    // 如果更新失败，恢复原状态
    row.isFeatured = originalStatus
    ElMessage.error('操作失败')
  }
}

// 工具函数
const formatPrice = (price) => {
  return price ? Number(price).toLocaleString() : '0'
}

// 获取拍卖状态文本
const getAuctionStatusText = (status) => {
  const statusMap = {
    0: '未开拍',
    1: '一拍中',
    2: '二拍中',
    3: '变卖中',
    4: '已结束'
  }
  return statusMap[status] || '未知'
}

// 获取拍卖状态类型
const getAuctionStatusType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'warning',
    2: 'warning',
    3: 'danger',
    4: 'success'
  }
  return typeMap[status] || 'info'
}

// 获取装修情况文本
const getDecorationText = (decoration) => {
  const decorationMap = {
    0: '毛坯',
    1: '简装',
    2: '精装'
  }
  return decorationMap[decoration] || '未知'
}

// 切换精选状态
const handleToggleSelected = async (row) => {
  try {
    await updateHouse(row)
    ElMessage.success('状态更新成功')
    getList()
  } catch (error) {
    ElMessage.error('状态更新失败')
    // 恢复原状态
    row.isSelected = row.isSelected === 1 ? 0 : 1
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.price {
  color: #e6a23c;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;

  .el-button {
    margin: 0;
    flex-shrink: 0;
  }

  @media (max-width: 1200px) {
    gap: 4px;

    .el-button {
      font-size: 12px;
      padding: 4px 8px;
    }
  }
}
</style>
