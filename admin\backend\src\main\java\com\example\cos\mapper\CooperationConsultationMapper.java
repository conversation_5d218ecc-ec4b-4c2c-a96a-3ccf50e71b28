package com.example.cos.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.cos.entity.CooperationConsultation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 合作咨询数据访问层
 */
@Mapper
public interface CooperationConsultationMapper extends BaseMapper<CooperationConsultation> {

    @Select("SELECT * FROM cooperation_consultation_table WHERE user_id = #{userId}")
    List<CooperationConsultation> findByUserId(@Param("userId") Integer userId);

    @Select("SELECT * FROM cooperation_consultation_table WHERE city = #{city}")
    List<CooperationConsultation> findByCity(@Param("city") String city);

    @Select("SELECT * FROM cooperation_consultation_table WHERE name LIKE CONCAT('%', #{name}, '%')")
    List<CooperationConsultation> findByName(@Param("name") String name);
}
