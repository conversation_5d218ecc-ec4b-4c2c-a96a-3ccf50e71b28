package com.example.cos.controller;

import com.example.cos.common.Result;
import com.example.cos.common.ResultCode;
import com.example.cos.dto.HouseSaleRegistrationCreateDTO;
import com.example.cos.entity.HouseSaleRegistration;
import com.example.cos.service.HouseSaleRegistrationService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 房源出售登记管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Api(tags = "房源出售登记管理")
@RestController
@RequestMapping("/api/house-sale")
@Validated
public class HouseSaleRegistrationController {

    private static final Logger logger = LoggerFactory.getLogger(HouseSaleRegistrationController.class);

    @Autowired
    private HouseSaleRegistrationService houseSaleRegistrationService;

    /**
     * 创建房源出售登记
     */
    @ApiOperation(value = "创建房源出售登记", notes = "用户提交房源出售信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "登记成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/create")
    public Result<HouseSaleRegistration> createRegistration(@RequestBody @Validated HouseSaleRegistrationCreateDTO createDTO) {
        try {
            logger.info("创建房源出售登记: {}", createDTO);
            
            HouseSaleRegistration registration = houseSaleRegistrationService.createRegistration(createDTO);
            
            logger.info("房源出售登记创建成功: {}", registration.getId());
            return Result.success("登记成功", registration);
            
        } catch (IllegalArgumentException e) {
            logger.warn("房源出售登记参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("创建房源出售登记失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "登记失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询房源出售登记
     */
    @ApiOperation(value = "查询房源出售登记详情", notes = "根据登记ID查询详细信息")
    @ApiImplicitParam(name = "id", value = "登记ID", required = true, dataType = "int", paramType = "path")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 404, message = "登记信息不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/{id}")
    public Result<HouseSaleRegistration> getRegistrationById(@PathVariable Integer id) {
        try {
            logger.info("查询房源出售登记详情: {}", id);
            
            HouseSaleRegistration registration = houseSaleRegistrationService.getById(id);
            
            if (registration == null) {
                return Result.error(ResultCode.NOT_FOUND.getCode(), "登记信息不存在");
            }
            
            return Result.success("查询成功", registration);
            
        } catch (Exception e) {
            logger.error("查询房源出售登记失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID查询房源出售登记
     */
    @ApiOperation(value = "查询用户的房源出售登记", notes = "根据用户ID查询其所有房源出售登记")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "int", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/user")
    public Result<List<HouseSaleRegistration>> getRegistrationsByUserId(@RequestParam Integer userId) {
        try {
            logger.info("查询用户{}的房源出售登记", userId);
            
            List<HouseSaleRegistration> registrations = houseSaleRegistrationService.findByUserId(userId);
            
            return Result.success("查询成功", registrations);
            
        } catch (Exception e) {
            logger.error("查询用户房源出售登记失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据城市查询房源出售登记
     */
    @ApiOperation(value = "根据城市查询房源出售登记", notes = "查询指定城市的房源出售登记")
    @ApiImplicitParam(name = "city", value = "城市", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/city")
    public Result<List<HouseSaleRegistration>> getRegistrationsByCity(@RequestParam String city) {
        try {
            logger.info("查询城市{}的房源出售登记", city);
            
            List<HouseSaleRegistration> registrations = houseSaleRegistrationService.findByCity(city);
            
            return Result.success("查询成功", registrations);
            
        } catch (Exception e) {
            logger.error("查询城市房源出售登记失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据小区名称查询房源出售登记
     */
    @ApiOperation(value = "根据小区名称查询房源出售登记", notes = "查询指定小区的房源出售登记")
    @ApiImplicitParam(name = "communityName", value = "小区名称", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/community")
    public Result<List<HouseSaleRegistration>> getRegistrationsByCommunity(@RequestParam String communityName) {
        try {
            logger.info("查询小区{}的房源出售登记", communityName);
            
            List<HouseSaleRegistration> registrations = houseSaleRegistrationService.findByCommunityName(communityName);
            
            return Result.success("查询成功", registrations);
            
        } catch (Exception e) {
            logger.error("查询小区房源出售登记失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据房屋类型查询房源出售登记
     */
    @ApiOperation(value = "根据房屋类型查询房源出售登记", notes = "查询指定类型的房源出售登记")
    @ApiImplicitParam(name = "houseType", value = "房屋类型", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/type")
    public Result<List<HouseSaleRegistration>> getRegistrationsByType(@RequestParam String houseType) {
        try {
            logger.info("查询房屋类型{}的房源出售登记", houseType);
            
            List<HouseSaleRegistration> registrations = houseSaleRegistrationService.findByHouseType(houseType);
            
            return Result.success("查询成功", registrations);
            
        } catch (Exception e) {
            logger.error("查询房屋类型房源出售登记失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新房源出售登记
     */
    @ApiOperation(value = "更新房源出售登记", notes = "更新房源出售登记信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "更新成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PutMapping("/update")
    public Result<Boolean> updateRegistration(@RequestBody HouseSaleRegistration registration) {
        try {
            logger.info("更新房源出售登记: {}", registration);
            
            boolean updated = houseSaleRegistrationService.updateRegistration(registration);
            
            if (updated) {
                return Result.success("更新成功", true);
            } else {
                return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "更新失败");
            }
            
        } catch (Exception e) {
            logger.error("更新房源出售登记失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除房源出售登记
     */
    @ApiOperation(value = "删除房源出售登记", notes = "根据ID删除房源出售登记")
    @ApiImplicitParam(name = "id", value = "登记ID", required = true, dataType = "int", paramType = "path")
    @ApiResponses({
            @ApiResponse(code = 200, message = "删除成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteRegistration(@PathVariable Integer id) {
        try {
            logger.info("删除房源出售登记: {}", id);
            
            boolean deleted = houseSaleRegistrationService.deleteRegistration(id);
            
            if (deleted) {
                return Result.success("删除成功", true);
            } else {
                return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "删除失败");
            }
            
        } catch (Exception e) {
            logger.error("删除房源出售登记失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "删除失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有房源出售登记
     */
    @ApiOperation(value = "查询所有房源出售登记", notes = "获取所有房源出售登记列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/all")
    public Result<List<HouseSaleRegistration>> getAllRegistrations() {
        try {
            logger.info("查询所有房源出售登记");
            
            List<HouseSaleRegistration> registrations = houseSaleRegistrationService.list();
            
            return Result.success("查询成功", registrations);
            
        } catch (Exception e) {
            logger.error("查询所有房源出售登记失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }
}
