<template>
  <div class="consultation-detail">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="咨询ID">
        {{ consultationData.id }}
      </el-descriptions-item>
      <el-descriptions-item label="用户ID">
        {{ consultationData.userId }}
      </el-descriptions-item>
      <el-descriptions-item label="城市">
        {{ consultationData.city }}
      </el-descriptions-item>
      <el-descriptions-item label="姓名">
        {{ consultationData.name }}
      </el-descriptions-item>
      <el-descriptions-item label="性别">
        {{ consultationData.gender }}
      </el-descriptions-item>
      <el-descriptions-item label="联系方式">
        {{ consultationData.contactInfo }}
      </el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="getStatusType(consultationData.status)">
          {{ consultationData.status || '待处理' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ consultationData.createTime }}
      </el-descriptions-item>
      <el-descriptions-item label="咨询内容" :span="2">
        <div class="content-text">
          {{ consultationData.consultationContent }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="备注" :span="2">
        <div class="content-text">
          {{ consultationData.remarks || '无' }}
        </div>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 回复记录 -->
    <div class="reply-section" v-if="replyHistory.length">
      <h3>回复记录</h3>
      <el-timeline>
        <el-timeline-item
          v-for="reply in replyHistory"
          :key="reply.id"
          :timestamp="reply.replyTime"
          type="primary"
        >
          <el-card>
            <div class="reply-content">
              <div class="reply-header">
                <span class="replier">回复人：{{ reply.replier }}</span>
                <span class="reply-method">回复方式：{{ reply.method }}</span>
              </div>
              <div class="reply-text">{{ reply.content }}</div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps } from 'vue'

const props = defineProps({
  consultationData: {
    type: Object,
    default: () => ({})
  }
})

// 模拟回复记录
const replyHistory = ref([
  {
    id: 1,
    replier: '客服小王',
    method: '电话回复',
    content: '已通过电话与客户沟通，详细介绍了学区房投资的相关政策和注意事项。客户表示满意，后续会考虑购买。',
    replyTime: '2024-01-15 15:30:00'
  }
])

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    '待处理': 'warning',
    '处理中': 'primary',
    '已回复': 'success',
    '已关闭': 'info'
  }
  return statusMap[status] || 'warning'
}
</script>

<style lang="scss" scoped>
.consultation-detail {
  .content-text {
    line-height: 1.6;
    color: #606266;
  }

  .reply-section {
    margin-top: 30px;

    h3 {
      margin-bottom: 20px;
      color: #303133;
    }

    .reply-content {
      .reply-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        font-size: 14px;
        color: #909399;

        .replier {
          font-weight: 600;
        }
      }

      .reply-text {
        line-height: 1.6;
        color: #606266;
      }
    }
  }
}
</style>
