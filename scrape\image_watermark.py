"""
图片水印处理模块
用于在房源图片右下角添加logo水印
"""

import os
from PIL import Image, ImageEnhance
import io
import logging

class ImageWatermark:
    def __init__(self, logo_path="logo.jpg"):
        """
        初始化水印处理器
        
        Args:
            logo_path: logo图片路径，默认为当前目录下的logo.jpg
        """
        self.logo_path = logo_path
        self.logger = logging.getLogger(__name__)
        
        # 检查logo文件是否存在
        if not os.path.exists(self.logo_path):
            self.logger.error(f"Logo文件不存在: {self.logo_path}")
            raise FileNotFoundError(f"Logo文件不存在: {self.logo_path}")
    
    def add_watermark(self, image_data, logo_size_ratio=0.25, opacity=1, margin=0):
        """
        为图片添加右下角logo水印
        
        Args:
            image_data: 图片二进制数据
            logo_size_ratio: logo相对于原图的大小比例，默认0.15（15%）
            opacity: logo透明度，0-1之间，默认0.8
            margin: logo距离右下角的边距，默认20像素
            
        Returns:
            bytes: 添加水印后的图片二进制数据
        """
        try:
            # 打开原图
            original_image = Image.open(io.BytesIO(image_data))
            
            # 确保原图是RGBA模式，支持透明度
            if original_image.mode != 'RGBA':
                original_image = original_image.convert('RGBA')
            
            # 打开logo图片
            logo = Image.open(self.logo_path)
            
            # 确保logo是RGBA模式
            if logo.mode != 'RGBA':
                logo = logo.convert('RGBA')
            
            # 计算logo的目标尺寸
            original_width, original_height = original_image.size
            logo_width = int(original_width * logo_size_ratio)
            
            # 按比例缩放logo，保持宽高比
            logo_aspect_ratio = logo.size[1] / logo.size[0]
            logo_height = int(logo_width * logo_aspect_ratio)
            
            # 调整logo大小
            logo_resized = logo.resize((logo_width, logo_height), Image.Resampling.LANCZOS)
            
            # 调整logo透明度
            if opacity < 1.0:
                # 创建一个透明度遮罩
                alpha = logo_resized.split()[-1]  # 获取alpha通道
                alpha = ImageEnhance.Brightness(alpha).enhance(opacity)
                logo_resized.putalpha(alpha)
            
            # 计算logo在右下角的位置
            logo_x = original_width - logo_width - margin
            logo_y = original_height - logo_height - margin
            
            # 确保logo不会超出图片边界
            logo_x = max(0, logo_x)
            logo_y = max(0, logo_y)
            
            # 创建一个透明层用于合成
            watermark_layer = Image.new('RGBA', original_image.size, (0, 0, 0, 0))
            watermark_layer.paste(logo_resized, (logo_x, logo_y), logo_resized)
            
            # 将水印层合成到原图上
            watermarked_image = Image.alpha_composite(original_image, watermark_layer)
            
            # 如果原图不是RGBA模式，转换回原来的模式
            if Image.open(io.BytesIO(image_data)).mode != 'RGBA':
                watermarked_image = watermarked_image.convert('RGB')
            
            # 保存到内存
            output_buffer = io.BytesIO()
            
            # 根据原图格式保存
            format_map = {
                'JPEG': 'JPEG',
                'JPG': 'JPEG', 
                'PNG': 'PNG',
                'WEBP': 'WEBP'
            }
            
            # 尝试从原始数据推断格式
            original_format = 'PNG'  # 默认PNG
            try:
                temp_img = Image.open(io.BytesIO(image_data))
                if temp_img.format in format_map:
                    original_format = format_map[temp_img.format]
            except:
                pass
            
            # 保存图片
            if original_format == 'JPEG':
                watermarked_image = watermarked_image.convert('RGB')
                watermarked_image.save(output_buffer, format='JPEG', quality=95)
            else:
                watermarked_image.save(output_buffer, format=original_format)
            
            watermarked_data = output_buffer.getvalue()
            
            self.logger.info(f"成功添加水印，原图尺寸: {original_width}x{original_height}, logo尺寸: {logo_width}x{logo_height}")
            
            return watermarked_data
            
        except Exception as e:
            self.logger.error(f"添加水印失败: {str(e)}")
            # 如果添加水印失败，返回原图数据
            return image_data
    
    def add_watermark_to_file(self, input_path, output_path=None, **kwargs):
        """
        为文件添加水印
        
        Args:
            input_path: 输入图片文件路径
            output_path: 输出图片文件路径，如果为None则覆盖原文件
            **kwargs: 传递给add_watermark的其他参数
            
        Returns:
            bool: 是否成功
        """
        try:
            # 读取原图文件
            with open(input_path, 'rb') as f:
                image_data = f.read()
            
            # 添加水印
            watermarked_data = self.add_watermark(image_data, **kwargs)
            
            # 确定输出路径
            if output_path is None:
                output_path = input_path
            
            # 保存结果
            with open(output_path, 'wb') as f:
                f.write(watermarked_data)
            
            self.logger.info(f"成功为文件添加水印: {input_path} -> {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"为文件添加水印失败: {str(e)}")
            return False

# 便捷函数
def add_watermark_to_image_data(image_data, logo_path="logo.jpg", **kwargs):
    """
    便捷函数：为图片数据添加水印
    
    Args:
        image_data: 图片二进制数据
        logo_path: logo文件路径
        **kwargs: 其他水印参数
        
    Returns:
        bytes: 添加水印后的图片数据
    """
    watermarker = ImageWatermark(logo_path)
    return watermarker.add_watermark(image_data, **kwargs)

def add_watermark_to_file(input_path, output_path=None, logo_path="logo.jpg", **kwargs):
    """
    便捷函数：为图片文件添加水印
    
    Args:
        input_path: 输入图片文件路径
        output_path: 输出图片文件路径
        logo_path: logo文件路径
        **kwargs: 其他水印参数
        
    Returns:
        bool: 是否成功
    """
    watermarker = ImageWatermark(logo_path)
    return watermarker.add_watermark_to_file(input_path, output_path, **kwargs)
