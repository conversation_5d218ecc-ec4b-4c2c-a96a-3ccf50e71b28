package com.example.cos.controller;

import com.example.cos.common.Result;
import com.example.cos.common.ResultCode;
import com.example.cos.dto.AdminLoginDTO;
import com.example.cos.dto.AdminLoginResultDTO;
import com.example.cos.entity.Admin;
import com.example.cos.service.AdminService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 管理员管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Api(tags = "管理员管理")
@RestController
@RequestMapping("/api/admin")
@Validated
public class AdminController {

    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);

    @Autowired
    private AdminService adminService;

    /**
     * 管理员登录
     */
    @ApiOperation(value = "管理员登录", notes = "管理员账号密码登录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "登录成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 401, message = "用户名或密码错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/login")
    public Result<AdminLoginResultDTO> login(@RequestBody @Validated AdminLoginDTO loginDTO) {
        try {
            logger.info("管理员登录请求: {}", loginDTO);
            
            AdminLoginResultDTO result = adminService.login(loginDTO);
            
            logger.info("管理员登录成功: {}", loginDTO.getUsername());
            return Result.success("登录成功", result);
            
        } catch (IllegalArgumentException e) {
            logger.warn("管理员登录参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (RuntimeException e) {
            logger.warn("管理员登录失败: {}", e.getMessage());
            return Result.error(ResultCode.UNAUTHORIZED.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("管理员登录异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "登录失败: " + e.getMessage());
        }
    }

    /**
     * 创建管理员
     */
    @ApiOperation(value = "创建管理员", notes = "创建新的管理员账号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "username", value = "用户名", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "password", value = "密码", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "role", value = "角色", required = true, dataType = "string", paramType = "query")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "创建成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/create")
    public Result<Admin> createAdmin(
            @RequestParam String username,
            @RequestParam String password,
            @RequestParam String role) {
        try {
            logger.info("创建管理员请求: username={}, role={}", username, role);
            
            Admin admin = adminService.createAdmin(username, password, role);
            
            // 清除密码信息
            admin.setPassword(null);
            
            logger.info("管理员创建成功: {}", admin.getId());
            return Result.success("创建成功", admin);
            
        } catch (IllegalArgumentException e) {
            logger.warn("创建管理员参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (RuntimeException e) {
            logger.warn("创建管理员失败: {}", e.getMessage());
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("创建管理员异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "创建失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户名查询管理员
     */
    @ApiOperation(value = "根据用户名查询管理员", notes = "通过用户名查询管理员信息")
    @ApiImplicitParam(name = "username", value = "用户名", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 404, message = "管理员不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/username")
    public Result<Admin> getAdminByUsername(@RequestParam String username) {
        try {
            logger.info("根据用户名查询管理员: {}", username);
            
            Admin admin = adminService.findByUsername(username);
            
            if (admin == null) {
                return Result.error(ResultCode.NOT_FOUND.getCode(), "管理员不存在");
            }
            
            // 清除密码信息
            admin.setPassword(null);
            
            return Result.success("查询成功", admin);
            
        } catch (Exception e) {
            logger.error("查询管理员失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }
}
