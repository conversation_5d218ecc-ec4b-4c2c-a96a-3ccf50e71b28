package com.example.cos.config;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.region.Region;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

/**
 * 腾讯云COS配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class CosConfig {

    private static final Logger logger = LoggerFactory.getLogger(CosConfig.class);

    @Autowired
    private CosProperties cosProperties;

    /**
     * 创建COS客户端Bean
     * 
     * @return COSClient实例
     */
    @Bean
    public COSClient cosClient() {
        // 验证配置参数
        validateProperties();

        // 创建COS凭证
        COSCredentials credentials = new BasicCOSCredentials(
                cosProperties.getSecretId(), 
                cosProperties.getSecretKey()
        );

        // 创建客户端配置
        ClientConfig clientConfig = new ClientConfig(new Region(cosProperties.getRegion()));
        
        // 设置超时时间
        clientConfig.setConnectionTimeout(cosProperties.getConnectionTimeout());
        clientConfig.setSocketTimeout(cosProperties.getSocketTimeout());

        // 创建COS客户端
        COSClient cosClient = new COSClient(credentials, clientConfig);
        
        logger.info("腾讯云COS客户端初始化成功，Region: {}, Bucket: {}", 
                cosProperties.getRegion(), cosProperties.getBucketName());
        
        return cosClient;
    }

    /**
     * 验证配置参数
     */
    private void validateProperties() {
        if (!StringUtils.hasText(cosProperties.getSecretId())) {
            throw new IllegalArgumentException("腾讯云COS SecretId不能为空，请在application.yml中配置tencent.cos.secret-id");
        }
        if (!StringUtils.hasText(cosProperties.getSecretKey())) {
            throw new IllegalArgumentException("腾讯云COS SecretKey不能为空，请在application.yml中配置tencent.cos.secret-key");
        }
        if (!StringUtils.hasText(cosProperties.getBucketName())) {
            throw new IllegalArgumentException("腾讯云COS BucketName不能为空，请在application.yml中配置tencent.cos.bucket-name");
        }
        if (!StringUtils.hasText(cosProperties.getRegion())) {
            throw new IllegalArgumentException("腾讯云COS Region不能为空，请在application.yml中配置tencent.cos.region");
        }
        
        logger.debug("腾讯云COS配置验证通过: {}", cosProperties);
    }
}
