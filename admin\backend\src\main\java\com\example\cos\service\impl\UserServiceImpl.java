package com.example.cos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.cos.constant.UserRole;
import com.example.cos.dto.PageResultDTO;
import com.example.cos.dto.UserCreateDTO;
import com.example.cos.dto.UserQueryDTO;
import com.example.cos.entity.HouseResource;
import com.example.cos.entity.User;
import com.example.cos.mapper.UserMapper;
import com.example.cos.service.HouseResourceService;
import com.example.cos.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private HouseResourceService houseResourceService;

    @Override
    public User createUser(UserCreateDTO userCreateDTO) {
        logger.info("创建用户: {}", userCreateDTO);
        
        // 检查手机号是否已存在
        User existingUser = findByPhoneNumber(userCreateDTO.getPhoneNumber());
        if (existingUser != null) {
            throw new RuntimeException("手机号已存在: " + userCreateDTO.getPhoneNumber());
        }
        
        // 创建用户
        User user = new User();
        BeanUtils.copyProperties(userCreateDTO, user);
        user.setCreateTime(LocalDateTime.now());

        // 设置默认角色，如果DTO中没有指定角色
        if (user.getRole() == null) {
            user.setRole(UserRole.getDefaultRole());
        }

        boolean saved = save(user);
        if (!saved) {
            throw new RuntimeException("用户创建失败");
        }
        
        logger.info("用户创建成功: {}", user);
        return user;
    }

    @Override
    public User findByPhoneNumber(String phoneNumber) {
        return baseMapper.findByPhoneNumber(phoneNumber);
    }

    @Override
    public User findByWechatOpenid(String wechatOpenid) {
        return baseMapper.findByWechatOpenid(wechatOpenid);
    }

    @Override
    public boolean updateUser(User user) {
        logger.info("更新用户信息: {}", user);
        return updateById(user);
    }

    @Override
    public boolean updateUserRole(Integer userId, Integer role) {
        logger.info("更新用户{}角色为: {}", userId, role);

        // 验证角色值是否有效
        if (!UserRole.isValidRole(role)) {
            throw new IllegalArgumentException("无效的角色值: " + role);
        }

        // 检查用户是否存在
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }

        // 更新角色
        user.setRole(role);
        boolean updated = updateById(user);

        if (updated) {
            logger.info("用户{}角色更新成功: {}", userId, UserRole.getRoleDescription(role));
        } else {
            logger.error("用户{}角色更新失败", userId);
        }

        return updated;
    }

    @Override
    public boolean addFavoriteHouse(Integer userId, Integer houseId) {
        logger.info("用户{}添加收藏房源: {}", userId, houseId);

        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }

        // 验证房源是否存在
        try {
            HouseResource house = houseResourceService.getById(houseId.longValue());
            if (house == null) {
                throw new RuntimeException("房源不存在: " + houseId);
            }
        } catch (Exception e) {
            throw new RuntimeException("房源验证失败: " + e.getMessage());
        }

        List<Integer> favoriteHouseIds = user.getFavoriteHouseIds();
        if (favoriteHouseIds == null) {
            favoriteHouseIds = new ArrayList<>();
        }

        // 先移除所有相同的ID，确保不重复
        while (favoriteHouseIds.remove(houseId)) {
            // 移除所有重复的ID
        }

        // 添加新的ID
        favoriteHouseIds.add(houseId);
        user.setFavoriteHouseIds(favoriteHouseIds);
        boolean updated = updateById(user);

        if (updated) {
            logger.info("用户{}成功添加收藏房源: {}，当前收藏列表: {}", userId, houseId, favoriteHouseIds);
        }

        return updated;
    }

    @Override
    public boolean removeFavoriteHouse(Integer userId, Integer houseId) {
        logger.info("用户{}移除收藏房源: {}", userId, houseId);

        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }

        List<Integer> favoriteHouseIds = user.getFavoriteHouseIds();
        if (!CollectionUtils.isEmpty(favoriteHouseIds)) {
            // 确保完全移除，使用while循环移除所有匹配的ID
            while (favoriteHouseIds.remove(houseId)) {
                // 继续移除直到没有匹配的ID
            }
            user.setFavoriteHouseIds(favoriteHouseIds);
            boolean updated = updateById(user);

            if (updated) {
                logger.info("用户{}成功移除收藏房源: {}，当前收藏列表: {}", userId, houseId, favoriteHouseIds);
            }

            return updated;
        }

        logger.info("用户{}的收藏列表为空或不包含房源: {}", userId, houseId);
        return true;
    }

    @Override
    public boolean addFollowedHouse(Integer userId, Integer houseId) {
        logger.info("用户{}添加关注房源: {}", userId, houseId);

        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }

        // 验证房源是否存在
        try {
            HouseResource house = houseResourceService.getById(houseId.longValue());
            if (house == null) {
                throw new RuntimeException("房源不存在: " + houseId);
            }
        } catch (Exception e) {
            throw new RuntimeException("房源验证失败: " + e.getMessage());
        }

        List<Integer> followedHouseIds = user.getFollowedHouseIds();
        if (followedHouseIds == null) {
            followedHouseIds = new ArrayList<>();
        }

        // 先移除所有相同的ID，确保不重复
        while (followedHouseIds.remove(houseId)) {
            // 移除所有重复的ID
        }

        // 添加新的ID
        followedHouseIds.add(houseId);
        user.setFollowedHouseIds(followedHouseIds);
        boolean updated = updateById(user);

        if (updated) {
            logger.info("用户{}成功添加关注房源: {}，当前关注列表: {}", userId, houseId, followedHouseIds);
        }

        return updated;
    }

    @Override
    public boolean removeFollowedHouse(Integer userId, Integer houseId) {
        logger.info("用户{}移除关注房源: {}", userId, houseId);

        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }

        List<Integer> followedHouseIds = user.getFollowedHouseIds();
        if (!CollectionUtils.isEmpty(followedHouseIds)) {
            // 确保完全移除，使用while循环移除所有匹配的ID
            while (followedHouseIds.remove(houseId)) {
                // 继续移除直到没有匹配的ID
            }
            user.setFollowedHouseIds(followedHouseIds);
            boolean updated = updateById(user);

            if (updated) {
                logger.info("用户{}成功移除关注房源: {}，当前关注列表: {}", userId, houseId, followedHouseIds);
            }

            return updated;
        }

        logger.info("用户{}的关注列表为空或不包含房源: {}", userId, houseId);
        return true;
    }

    @Override
    public List<Integer> getFavoriteHouseIds(Integer userId) {
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }

        List<Integer> favoriteHouseIds = user.getFavoriteHouseIds() != null ? user.getFavoriteHouseIds() : new ArrayList<>();

        // 清理无效的房源ID，确保数据一致性
        List<Integer> validFavoriteIds = cleanInvalidHouseIds(favoriteHouseIds);

        // 如果清理后的列表与原列表不同，更新用户数据
        if (!favoriteHouseIds.equals(validFavoriteIds)) {
            user.setFavoriteHouseIds(validFavoriteIds);
            updateById(user);
            logger.info("用户{}的收藏列表已清理无效房源ID，原列表: {}，清理后: {}", userId, favoriteHouseIds, validFavoriteIds);
        }

        return validFavoriteIds;
    }

    @Override
    public List<Integer> getFollowedHouseIds(Integer userId) {
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }

        List<Integer> followedHouseIds = user.getFollowedHouseIds() != null ? user.getFollowedHouseIds() : new ArrayList<>();

        // 清理无效的房源ID，确保数据一致性
        List<Integer> validFollowedIds = cleanInvalidHouseIds(followedHouseIds);

        // 如果清理后的列表与原列表不同，更新用户数据
        if (!followedHouseIds.equals(validFollowedIds)) {
            user.setFollowedHouseIds(validFollowedIds);
            updateById(user);
            logger.info("用户{}的关注列表已清理无效房源ID，原列表: {}，清理后: {}", userId, followedHouseIds, validFollowedIds);
        }

        return validFollowedIds;
    }

    /**
     * 清理无效的房源ID
     * @param houseIds 房源ID列表
     * @return 有效的房源ID列表
     */
    private List<Integer> cleanInvalidHouseIds(List<Integer> houseIds) {
        if (CollectionUtils.isEmpty(houseIds)) {
            return new ArrayList<>();
        }

        List<Integer> validIds = new ArrayList<>();

        for (Integer houseId : houseIds) {
            if (houseId != null) {
                // 检查房源是否存在
                try {
                    HouseResource house = houseResourceService.getById(houseId.longValue());
                    if (house != null) {
                        validIds.add(houseId);
                    } else {
                        logger.warn("房源ID {} 不存在，已从用户列表中移除", houseId);
                    }
                } catch (Exception e) {
                    logger.warn("检查房源ID {} 时发生错误: {}，已从用户列表中移除", houseId, e.getMessage());
                }
            }
        }

        return validIds;
    }

    @Override
    public PageResultDTO<User> getUserList(UserQueryDTO queryDTO) {
        logger.info("分页查询用户列表: {}", queryDTO);

        // 创建分页对象
        Page<User> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 构建查询条件
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();

        if (StringUtils.hasText(queryDTO.getPhoneNumber())) {
            queryWrapper.like("phone_number", queryDTO.getPhoneNumber());
        }

        if (StringUtils.hasText(queryDTO.getNickname())) {
            queryWrapper.like("nickname", queryDTO.getNickname());
        }

        if (StringUtils.hasText(queryDTO.getWechatOpenid())) {
            queryWrapper.like("wechat_openid", queryDTO.getWechatOpenid());
        }

        if (StringUtils.hasText(queryDTO.getStartTime())) {
            queryWrapper.ge("create_time", queryDTO.getStartTime());
        }

        if (StringUtils.hasText(queryDTO.getEndTime())) {
            queryWrapper.le("create_time", queryDTO.getEndTime());
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc("create_time");

        // 执行分页查询
        IPage<User> pageResult = page(page, queryWrapper);

        // 构建返回结果
        return new PageResultDTO<>(
                pageResult.getRecords(),
                pageResult.getTotal(),
                pageResult.getCurrent(),
                pageResult.getSize()
        );
    }

    @Override
    public boolean deleteUser(Integer userId) {
        logger.info("删除用户: {}", userId);

        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }

        boolean deleted = removeById(userId);
        if (deleted) {
            logger.info("用户删除成功: {}", userId);
        }

        return deleted;
    }

    @Override
    public boolean deleteUsers(List<Integer> userIds) {
        logger.info("批量删除用户: {}", userIds);

        if (CollectionUtils.isEmpty(userIds)) {
            throw new IllegalArgumentException("用户ID列表不能为空");
        }

        boolean deleted = removeByIds(userIds);
        if (deleted) {
            logger.info("批量删除用户成功: {}", userIds);
        }

        return deleted;
    }

    @Override
    public Long getUserCount() {
        return count();
    }

    @Override
    public Long getUserCountByDateRange(String startTime, String endTime) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();

        if (StringUtils.hasText(startTime)) {
            queryWrapper.ge("create_time", startTime);
        }

        if (StringUtils.hasText(endTime)) {
            queryWrapper.le("create_time", endTime);
        }

        return count(queryWrapper);
    }
}
