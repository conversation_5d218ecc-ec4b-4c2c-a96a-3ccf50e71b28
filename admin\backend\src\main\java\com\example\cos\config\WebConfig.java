package com.example.cos.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private JwtInterceptor jwtInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtInterceptor)
                .addPathPatterns("/api/**") // 拦截所有API请求
                .excludePathPatterns(
                        "/api/admin/login",           // 排除登录接口
                        "/api/admin/admins/**",       // 排除管理员管理接口
                        "/api/miniprogram/**",        // 排除小程序接口
                        "/api/user/**",               // 排除用户接口
                        "/api/house/**",              // 排除房源接口
                        "/api/house-sale/**",         // 排除卖房信息接口
                        "/api/consultation/**",       // 排除咨询接口
                        "/api/cos/**",                // 排除COS接口
                        "/doc.html",                  // 排除API文档
                        "/swagger-ui/**",             // 排除Swagger UI
                        "/swagger-resources/**",      // 排除Swagger资源
                        "/v2/api-docs",               // 排除API文档
                        "/webjars/**"                 // 排除静态资源
                );
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns(
                    "http://localhost:3000",
                    "http://127.0.0.1:3000",
                    "http://localhost:*",
                    "http://127.0.0.1:*",
                    "https://cqjxzc.com.cn",
                    "http://cqjxzc.com.cn",
                    "https://*.cqjxzc.com.cn",
                    "http://*.cqjxzc.com.cn"
                ) // 允许特定域名
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS") // 允许的HTTP方法
                .allowedHeaders("*") // 允许所有请求头
                .allowCredentials(true) // 允许携带凭证
                .maxAge(3600); // 预检请求的缓存时间（秒）
    }
}
