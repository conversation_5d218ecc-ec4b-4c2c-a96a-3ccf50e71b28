package com.example.cos.service;

import com.example.cos.dto.FileUploadResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 腾讯云COS服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CosService {

    /**
     * 上传单个文件
     * 
     * @param file 要上传的文件
     * @param filePath 文件存储路径（可选，为空时自动生成）
     * @return 文件上传结果
     */
    FileUploadResult uploadFile(MultipartFile file, String filePath);

    /**
     * 上传多个文件
     * 
     * @param files 要上传的文件列表
     * @param filePathPrefix 文件路径前缀（可选）
     * @return 文件上传结果列表
     */
    List<FileUploadResult> uploadFiles(List<MultipartFile> files, String filePathPrefix);

    /**
     * 下载文件（根据文件类型自动判断是预览还是下载）
     *
     * @param filePath 文件存储路径
     * @param response HTTP响应对象
     */
    void downloadFile(String filePath, HttpServletResponse response);

    /**
     * 强制下载文件（无论什么类型都强制下载）
     *
     * @param filePath 文件存储路径
     * @param response HTTP响应对象
     */
    void forceDownloadFile(String filePath, HttpServletResponse response);

    /**
     * 删除文件
     * 
     * @param filePath 文件存储路径
     * @return 是否删除成功
     */
    boolean deleteFile(String filePath);

    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件存储路径
     * @return 文件是否存在
     */
    boolean fileExists(String filePath);

    /**
     * 获取文件访问URL
     * 
     * @param filePath 文件存储路径
     * @return 文件访问URL
     */
    String getFileUrl(String filePath);
}
