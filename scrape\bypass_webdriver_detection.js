// 绕过webdriver检测的JavaScript代码

// 1. 隐藏webdriver属性
Object.defineProperty(navigator, 'webdriver', {
    get: () => undefined,
});

// 2. 修改Chrome对象
if (window.chrome) {
    // 添加缺失的chrome属性
    window.chrome.app = {
        isInstalled: false,
    };
    window.chrome.webstore = {
        onInstallStageChanged: {},
        onDownloadProgress: {},
    };
    window.chrome.runtime = {
        PlatformOs: {
            MAC: 'mac',
            WIN: 'win',
            ANDROID: 'android',
            CROS: 'cros',
            LINUX: 'linux',
            OPENBSD: 'openbsd',
        },
        PlatformArch: {
            ARM: 'arm',
            X86_32: 'x86-32',
            X86_64: 'x86-64',
        },
        PlatformNaclArch: {
            ARM: 'arm',
            X86_32: 'x86-32',
            X86_64: 'x86-64',
        },
        RequestUpdateCheckStatus: {
            THROTTLED: 'throttled',
            NO_UPDATE: 'no_update',
            UPDATE_AVAILABLE: 'update_available',
        },
        OnInstalledReason: {
            INSTALL: 'install',
            UPDATE: 'update',
            CHROME_UPDATE: 'chrome_update',
            SHARED_MODULE_UPDATE: 'shared_module_update',
        },
        OnRestartRequiredReason: {
            APP_UPDATE: 'app_update',
            OS_UPDATE: 'os_update',
            PERIODIC: 'periodic',
        },
    };
}

// 3. 修改permissions属性
const originalQuery = window.navigator.permissions.query;
window.navigator.permissions.query = (parameters) => (
    parameters.name === 'notifications' ?
        Promise.resolve({ state: Notification.permission }) :
        originalQuery(parameters)
);

// 4. 修改plugins属性
Object.defineProperty(navigator, 'plugins', {
    get: () => [1, 2, 3, 4, 5],
});

// 5. 修改languages属性
Object.defineProperty(navigator, 'languages', {
    get: () => ['zh-CN', 'zh', 'en'],
});

// 6. 修改webgl属性
const getParameter = WebGLRenderingContext.getParameter;
WebGLRenderingContext.prototype.getParameter = function(parameter) {
    // UNMASKED_VENDOR_WEBGL
    if (parameter === 37445) {
        return 'Intel Inc.';
    }
    // UNMASKED_RENDERER_WEBGL
    if (parameter === 37446) {
        return 'Intel Iris OpenGL Engine';
    }
    return getParameter(parameter);
};

// 7. 修改canvas指纹
const toBlob = HTMLCanvasElement.prototype.toBlob;
const toDataURL = HTMLCanvasElement.prototype.toDataURL;
const getImageData = CanvasRenderingContext2D.prototype.getImageData;

HTMLCanvasElement.prototype.toBlob = function(callback, type, quality) {
    const canvas = this;
    const ctx = canvas.getContext('2d');
    // 添加随机噪点
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    for (let i = 0; i < imageData.data.length; i += 4) {
        if (Math.random() < 0.001) {
            imageData.data[i] = Math.floor(Math.random() * 256);
            imageData.data[i + 1] = Math.floor(Math.random() * 256);
            imageData.data[i + 2] = Math.floor(Math.random() * 256);
        }
    }
    ctx.putImageData(imageData, 0, 0);
    return toBlob.apply(this, arguments);
};

HTMLCanvasElement.prototype.toDataURL = function() {
    const canvas = this;
    const ctx = canvas.getContext('2d');
    // 添加随机噪点
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    for (let i = 0; i < imageData.data.length; i += 4) {
        if (Math.random() < 0.001) {
            imageData.data[i] = Math.floor(Math.random() * 256);
            imageData.data[i + 1] = Math.floor(Math.random() * 256);
            imageData.data[i + 2] = Math.floor(Math.random() * 256);
        }
    }
    ctx.putImageData(imageData, 0, 0);
    return toDataURL.apply(this, arguments);
};

// 8. 修改Date对象以避免时区检测
const originalDate = Date;
const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
Date.prototype.getTimezoneOffset = function() {
    return -480; // 中国时区 UTC+8
};

// 9. 修改screen属性
Object.defineProperty(screen, 'colorDepth', {
    get: () => 24,
});

Object.defineProperty(screen, 'pixelDepth', {
    get: () => 24,
});

// 10. 隐藏自动化相关属性
delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

// 11. 修改iframe检测
const originalCreateElement = document.createElement;
document.createElement = function(tagName) {
    const element = originalCreateElement.call(this, tagName);
    if (tagName === 'iframe') {
        try {
            element.contentWindow.navigator.webdriver = undefined;
        } catch (e) {}
    }
    return element;
};

// 12. 修改eval函数以避免检测
const originalEval = window.eval;
window.eval = function(code) {
    if (typeof code === 'string' && code.includes('webdriver')) {
        return undefined;
    }
    return originalEval.call(this, code);
};

// 13. 修改toString方法
const originalToString = Function.prototype.toString;
Function.prototype.toString = function() {
    if (this === navigator.webdriver) {
        return 'function webdriver() { [native code] }';
    }
    return originalToString.call(this);
};

// 14. 添加常见的浏览器属性
if (!window.chrome) {
    window.chrome = {
        runtime: {},
        loadTimes: function() {},
        csi: function() {},
        app: {}
    };
}

// 15. 修改navigator.userAgent（如果需要）
Object.defineProperty(navigator, 'userAgent', {
    get: () => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
});

// 16. 修改navigator.platform
Object.defineProperty(navigator, 'platform', {
    get: () => 'Win32',
});

// 17. 修改navigator.hardwareConcurrency
Object.defineProperty(navigator, 'hardwareConcurrency', {
    get: () => 8,
});

// 18. 修改navigator.deviceMemory
Object.defineProperty(navigator, 'deviceMemory', {
    get: () => 8,
});

// 19. 修改navigator.maxTouchPoints
Object.defineProperty(navigator, 'maxTouchPoints', {
    get: () => 0,
});

// 20. 隐藏Selenium相关属性
delete window.selenium;
delete window._selenium;
delete window.__selenium_unwrapped;
delete window.__selenium_evaluate;
delete window.__webdriver_evaluate;
delete window.__driver_evaluate;
delete window.__webdriver_unwrapped;
delete window.__driver_unwrapped;
delete window._Selenium_IDE_Recorder;

console.log('Webdriver detection bypass script loaded successfully');
