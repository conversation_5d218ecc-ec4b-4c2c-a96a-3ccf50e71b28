package com.example.cos.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "用户信息")
@TableName(value = "user_table", autoResultMap = true)
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", example = "1")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "手机号码", example = "13800138000", required = true)
    @TableField("phone_number")
    private String phoneNumber;

    @ApiModelProperty(value = "微信OpenID", example = "ox1234567890abcdef")
    @TableField("wechat_openid")
    private String wechatOpenid;

    @ApiModelProperty(value = "昵称", example = "张三")
    @TableField("nickname")
    private String nickname;

    @ApiModelProperty(value = "头像URL", example = "https://example.com/avatar.jpg")
    @TableField("avatar_url")
    private String avatarUrl;

    @ApiModelProperty(value = "收藏的房源ID列表", example = "[1, 2, 3]")
    @TableField(value = "favorite_house_ids", typeHandler = JacksonTypeHandler.class)
    private List<Integer> favoriteHouseIds;

    @ApiModelProperty(value = "关注的房源ID列表", example = "[4, 5, 6]")
    @TableField(value = "followed_house_ids", typeHandler = JacksonTypeHandler.class)
    private List<Integer> followedHouseIds;

    @ApiModelProperty(value = "创建时间", example = "2024-01-15 10:30:00")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "用户角色", example = "0", notes = "0=普通用户，1=有权限用户")
    @TableField("role")
    private Integer role;

    public User() {}

    public User(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getWechatOpenid() {
        return wechatOpenid;
    }

    public void setWechatOpenid(String wechatOpenid) {
        this.wechatOpenid = wechatOpenid;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public List<Integer> getFavoriteHouseIds() {
        return favoriteHouseIds;
    }

    public void setFavoriteHouseIds(List<Integer> favoriteHouseIds) {
        this.favoriteHouseIds = favoriteHouseIds;
    }

    public List<Integer> getFollowedHouseIds() {
        return followedHouseIds;
    }

    public void setFollowedHouseIds(List<Integer> followedHouseIds) {
        this.followedHouseIds = followedHouseIds;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", wechatOpenid='" + wechatOpenid + '\'' +
                ", nickname='" + nickname + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", favoriteHouseIds=" + favoriteHouseIds +
                ", followedHouseIds=" + followedHouseIds +
                ", createTime=" + createTime +
                ", role=" + role +
                '}';
    }
}
