package com.example.cos.controller;

import com.example.cos.common.Result;
import com.example.cos.common.ResultCode;
import com.example.cos.dto.CooperationConsultationCreateDTO;
import com.example.cos.entity.CooperationConsultation;
import com.example.cos.service.CooperationConsultationService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 合作咨询管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Api(tags = "合作咨询管理")
@RestController
@RequestMapping("/api/consultation")
@Validated
public class CooperationConsultationController {

    private static final Logger logger = LoggerFactory.getLogger(CooperationConsultationController.class);

    @Autowired
    private CooperationConsultationService cooperationConsultationService;

    /**
     * 创建合作咨询
     */
    @ApiOperation(value = "创建合作咨询", notes = "用户提交合作咨询信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "咨询提交成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/create")
    public Result<CooperationConsultation> createConsultation(@RequestBody @Validated CooperationConsultationCreateDTO createDTO) {
        try {
            logger.info("创建合作咨询: {}", createDTO);
            
            CooperationConsultation consultation = cooperationConsultationService.createConsultation(createDTO);
            
            logger.info("合作咨询创建成功: {}", consultation.getId());
            return Result.success("咨询提交成功", consultation);
            
        } catch (IllegalArgumentException e) {
            logger.warn("合作咨询参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("创建合作咨询失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "咨询提交失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询合作咨询
     */
    @ApiOperation(value = "查询合作咨询详情", notes = "根据咨询ID查询详细信息")
    @ApiImplicitParam(name = "id", value = "咨询ID", required = true, dataType = "int", paramType = "path")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 404, message = "咨询信息不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/{id}")
    public Result<CooperationConsultation> getConsultationById(@PathVariable Integer id) {
        try {
            logger.info("查询合作咨询详情: {}", id);
            
            CooperationConsultation consultation = cooperationConsultationService.getById(id);
            
            if (consultation == null) {
                return Result.error(ResultCode.NOT_FOUND.getCode(), "咨询信息不存在");
            }
            
            return Result.success("查询成功", consultation);
            
        } catch (Exception e) {
            logger.error("查询合作咨询失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID查询合作咨询
     */
    @ApiOperation(value = "查询用户的合作咨询", notes = "根据用户ID查询其所有合作咨询")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "int", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/user")
    public Result<List<CooperationConsultation>> getConsultationsByUserId(@RequestParam Integer userId) {
        try {
            logger.info("查询用户{}的合作咨询", userId);
            
            List<CooperationConsultation> consultations = cooperationConsultationService.findByUserId(userId);
            
            return Result.success("查询成功", consultations);
            
        } catch (Exception e) {
            logger.error("查询用户合作咨询失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据城市查询合作咨询
     */
    @ApiOperation(value = "根据城市查询合作咨询", notes = "查询指定城市的合作咨询")
    @ApiImplicitParam(name = "city", value = "城市", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/city")
    public Result<List<CooperationConsultation>> getConsultationsByCity(@RequestParam String city) {
        try {
            logger.info("查询城市{}的合作咨询", city);
            
            List<CooperationConsultation> consultations = cooperationConsultationService.findByCity(city);
            
            return Result.success("查询成功", consultations);
            
        } catch (Exception e) {
            logger.error("查询城市合作咨询失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据姓名查询合作咨询
     */
    @ApiOperation(value = "根据姓名查询合作咨询", notes = "查询指定姓名的合作咨询")
    @ApiImplicitParam(name = "name", value = "姓名", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/name")
    public Result<List<CooperationConsultation>> getConsultationsByName(@RequestParam String name) {
        try {
            logger.info("查询姓名{}的合作咨询", name);
            
            List<CooperationConsultation> consultations = cooperationConsultationService.findByName(name);
            
            return Result.success("查询成功", consultations);
            
        } catch (Exception e) {
            logger.error("查询姓名合作咨询失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新合作咨询
     */
    @ApiOperation(value = "更新合作咨询", notes = "更新合作咨询信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "更新成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PutMapping("/update")
    public Result<Boolean> updateConsultation(@RequestBody CooperationConsultation consultation) {
        try {
            logger.info("更新合作咨询: {}", consultation);
            
            boolean updated = cooperationConsultationService.updateConsultation(consultation);
            
            if (updated) {
                return Result.success("更新成功", true);
            } else {
                return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "更新失败");
            }
            
        } catch (Exception e) {
            logger.error("更新合作咨询失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除合作咨询
     */
    @ApiOperation(value = "删除合作咨询", notes = "根据ID删除合作咨询")
    @ApiImplicitParam(name = "id", value = "咨询ID", required = true, dataType = "int", paramType = "path")
    @ApiResponses({
            @ApiResponse(code = 200, message = "删除成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteConsultation(@PathVariable Integer id) {
        try {
            logger.info("删除合作咨询: {}", id);
            
            boolean deleted = cooperationConsultationService.deleteConsultation(id);
            
            if (deleted) {
                return Result.success("删除成功", true);
            } else {
                return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "删除失败");
            }
            
        } catch (Exception e) {
            logger.error("删除合作咨询失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "删除失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有合作咨询
     */
    @ApiOperation(value = "查询所有合作咨询", notes = "获取所有合作咨询列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/all")
    public Result<List<CooperationConsultation>> getAllConsultations() {
        try {
            logger.info("查询所有合作咨询");
            
            List<CooperationConsultation> consultations = cooperationConsultationService.list();
            
            return Result.success("查询成功", consultations);
            
        } catch (Exception e) {
            logger.error("查询所有合作咨询失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }
}
