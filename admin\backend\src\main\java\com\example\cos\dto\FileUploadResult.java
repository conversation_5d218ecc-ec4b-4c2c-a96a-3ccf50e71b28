package com.example.cos.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 文件上传结果DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "文件上传结果")
public class FileUploadResult {

    @ApiModelProperty(value = "文件名", example = "example.jpg")
    private String fileName;

    @ApiModelProperty(value = "文件大小（字节）", example = "1024000")
    private Long fileSize;

    @ApiModelProperty(value = "文件类型", example = "image/jpeg")
    private String contentType;

    @ApiModelProperty(value = "文件访问URL（如启用CDN则为CDN域名）", example = "https://your-cdn-domain.com/path/example.jpg")
    private String fileUrl;

    @ApiModelProperty(value = "文件存储路径", example = "path/example.jpg")
    private String filePath;

    @ApiModelProperty(value = "上传时间戳", example = "1640995200000")
    private Long uploadTime;

    public FileUploadResult() {
        this.uploadTime = System.currentTimeMillis();
    }

    public FileUploadResult(String fileName, Long fileSize, String contentType, String fileUrl, String filePath) {
        this();
        this.fileName = fileName;
        this.fileSize = fileSize;
        this.contentType = contentType;
        this.fileUrl = fileUrl;
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Long getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Long uploadTime) {
        this.uploadTime = uploadTime;
    }

    @Override
    public String toString() {
        return "FileUploadResult{" +
                "fileName='" + fileName + '\'' +
                ", fileSize=" + fileSize +
                ", contentType='" + contentType + '\'' +
                ", fileUrl='" + fileUrl + '\'' +
                ", filePath='" + filePath + '\'' +
                ", uploadTime=" + uploadTime +
                '}';
    }
}
