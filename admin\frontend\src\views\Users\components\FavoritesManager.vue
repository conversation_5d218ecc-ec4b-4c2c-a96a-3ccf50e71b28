<template>
  <div class="favorites-manager">
    <div class="manager-header">
      <el-button type="primary" @click="handleAddFavorite">
        <el-icon><Plus /></el-icon>
        添加收藏
      </el-button>
    </div>

    <!-- 当前收藏列表 -->
    <el-table :data="favoriteHouses" style="width: 100%">
      <el-table-column label="房源ID" prop="id" width="80" />
      <el-table-column label="小区名称" prop="communityName" min-width="150" />
      <el-table-column label="房屋类型" prop="houseType" width="100" />
      <el-table-column label="面积(㎡)" prop="area" width="100" />
      <el-table-column label="价格" prop="startingPrice" width="120">
        <template #default="scope">
          <span class="price">¥{{ formatPrice(scope.row.startingPrice) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template #default="scope">
          <el-button type="danger" size="small" @click="handleRemoveFavorite(scope.row.id)">
            移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加收藏对话框 -->
    <el-dialog
      title="添加收藏房源"
      v-model="addDialogVisible"
      width="600px"
    >
      <el-form :model="addForm" label-width="100px">
        <el-form-item label="选择房源">
          <el-select
            v-model="addForm.houseId"
            placeholder="请选择要收藏的房源"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="house in availableHouses"
              :key="house.id"
              :label="`${house.communityName} - ${house.houseType}`"
              :value="house.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAddFavorite">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, defineProps, defineEmits } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { addFavoriteHouse, removeFavoriteHouse, getFavoriteHouses } from '@/api/user'
import { getHouseList } from '@/api/house'

const props = defineProps({
  userId: {
    type: Number,
    required: true
  },
  favoriteHouseIds: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update'])

const favoriteHouses = ref([])
const availableHouses = ref([])
const addDialogVisible = ref(false)

const addForm = reactive({
  houseId: null
})

// 获取收藏房源列表
const getFavoriteList = async () => {
  try {
    // 模拟数据
    favoriteHouses.value = [
      {
        id: 1,
        communityName: '阳光花园',
        houseType: '住宅',
        area: 120,
        startingPrice: 1500000
      },
      {
        id: 2,
        communityName: '海景别墅',
        houseType: '别墅',
        area: 300,
        startingPrice: 5000000
      }
    ]
  } catch (error) {
    ElMessage.error('获取收藏列表失败')
  }
}

// 获取可选房源列表
const getAvailableHouses = async () => {
  try {
    // 模拟数据
    availableHouses.value = [
      {
        id: 3,
        communityName: '绿城花园',
        houseType: '公寓',
        area: 80,
        startingPrice: 800000
      },
      {
        id: 4,
        communityName: '万科城',
        houseType: '住宅',
        area: 150,
        startingPrice: 2000000
      }
    ]
  } catch (error) {
    ElMessage.error('获取房源列表失败')
  }
}

// 添加收藏
const handleAddFavorite = () => {
  addForm.houseId = null
  addDialogVisible.value = true
}

// 确认添加收藏
const confirmAddFavorite = async () => {
  if (!addForm.houseId) {
    ElMessage.warning('请选择要收藏的房源')
    return
  }

  try {
    await addFavoriteHouse(props.userId, addForm.houseId)
    ElMessage.success('添加收藏成功')
    addDialogVisible.value = false
    getFavoriteList()
    emit('update')
  } catch (error) {
    ElMessage.error('添加收藏失败')
  }
}

// 移除收藏
const handleRemoveFavorite = async (houseId) => {
  try {
    await ElMessageBox.confirm(
      '确定要移除这个收藏吗？',
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await removeFavoriteHouse(props.userId, houseId)
    ElMessage.success('移除收藏成功')
    getFavoriteList()
    emit('update')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除收藏失败')
    }
  }
}

// 格式化价格
const formatPrice = (price) => {
  return price ? Number(price).toLocaleString() : '0'
}

onMounted(() => {
  getFavoriteList()
  getAvailableHouses()
})
</script>

<style lang="scss" scoped>
.favorites-manager {
  .manager-header {
    margin-bottom: 20px;
  }

  .price {
    color: #e6a23c;
    font-weight: 600;
  }
}
</style>
