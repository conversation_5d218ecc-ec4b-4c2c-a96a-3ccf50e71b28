package com.example.cos.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.cos.dto.HouseResourceQueryDTO;
import com.example.cos.dto.HouseResourceStatisticsDTO;
import com.example.cos.entity.HouseResource;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 房源资源服务接口
 */
public interface HouseResourceService extends IService<HouseResource> {

    /**
     * 分页查询房源
     */
    IPage<HouseResource> pageQuery(HouseResourceQueryDTO queryDTO);

    /**
     * 根据拍卖状态查询房源
     */
    List<HouseResource> findByAuctionStatus(Integer auctionStatus);

    /**
     * 根据户型查询房源
     */
    List<HouseResource> findByHouseType(String houseType);

    /**
     * 根据小区名称查询房源
     */
    List<HouseResource> findByCommunityName(String communityName);

    /**
     * 根据起拍价区间查询房源
     */
    List<HouseResource> findByStartingPriceRange(BigDecimal minPrice, BigDecimal maxPrice);

    /**
     * 根据评估价区间查询房源
     */
    List<HouseResource> findByEvaluationPriceRange(BigDecimal minPrice, BigDecimal maxPrice);

    /**
     * 根据起拍时间范围查询房源
     */
    List<HouseResource> findByStartTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据房屋类型查询房源
     */
    List<HouseResource> findByHouseCategory(Integer houseCategory);

    /**
     * 根据是否特殊房屋查询房源
     */
    List<HouseResource> findByIsSpecial(Integer isSpecial);

    /**
     * 根据是否精选查询房源
     */
    List<HouseResource> findByIsSelected(Integer isSelected);

    /**
     * 根据装修情况查询房源
     */
    List<HouseResource> findByDecoration(Integer decoration);

    /**
     * 根据建筑面积区间查询房源
     */
    List<HouseResource> findByBuildingAreaRange(BigDecimal minArea, BigDecimal maxArea);

    /**
     * 根据建筑年份区间查询房源
     */
    List<HouseResource> findByConstructionYearRange(Integer yearFrom, Integer yearTo);

    /**
     * 根据标签查询房源
     */
    List<HouseResource> findByTag(String tag);

    /**
     * 创建房源
     */
    HouseResource createHouseResource(HouseResource houseResource);

    /**
     * 更新房源
     */
    boolean updateHouseResource(HouseResource houseResource);

    /**
     * 删除房源
     */
    boolean deleteHouseResource(Long id);

    /**
     * 批量删除房源
     */
    boolean batchDeleteHouseResources(List<Long> ids);

    /**
     * 根据ID列表批量查询房源
     */
    List<HouseResource> findByIds(List<Long> ids);

    /**
     * 批量插入房源
     */
    boolean batchInsertHouseResources(List<HouseResource> houseResources);

    /**
     * 批量更新房源状态
     */
    boolean batchUpdateStatus(List<Long> ids, Integer status);

    /**
     * 统计各拍卖状态的房源数量
     */
    List<Map<String, Object>> countByAuctionStatus();

    /**
     * 统计各房屋类型的房源数量
     */
    List<Map<String, Object>> countByHouseCategory();

    /**
     * 获取价格统计信息
     */
    Map<String, Object> getPriceStatistics();

    /**
     * 根据条件删除房源
     */
    boolean deleteByConditions(Map<String, Object> conditions);

    /**
     * 获取推荐房源（精选房源）
     */
    List<HouseResource> getRecommendedHouses(int limit);

    /**
     * 获取即将开拍的房源
     */
    List<HouseResource> getUpcomingAuctions(int limit);

    /**
     * 获取热门房源（根据访问量或其他指标）
     */
    List<HouseResource> getPopularHouses(int limit);

    /**
     * 搜索房源（全文搜索）
     */
    List<HouseResource> searchHouses(String keyword);

    /**
     * 获取房源详情（包含计算字段）
     */
    HouseResource getHouseResourceDetail(Long id);

    /**
     * 验证房源数据
     */
    boolean validateHouseResource(HouseResource houseResource);

    /**
     * 检查房源标题是否重复
     * @param title 房源标题
     * @return true表示标题重复，false表示标题唯一
     */
    boolean isTitleDuplicate(String title);

    /**
     * 检查房源标题是否重复（排除指定ID的记录）
     * @param title 房源标题
     * @param excludeId 要排除的房源ID（用于更新操作）
     * @return true表示标题重复，false表示标题唯一
     */
    boolean isTitleDuplicate(String title, Long excludeId);

    /**
     * 获取房源统计数据
     * @return 房源统计数据DTO
     */
    HouseResourceStatisticsDTO getHouseResourceStatistics();
}