package com.example.cos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.cos.dto.AdminCreateDTO;
import com.example.cos.dto.AdminLoginDTO;
import com.example.cos.dto.AdminLoginResultDTO;
import com.example.cos.dto.AdminUpdateDTO;
import com.example.cos.entity.Admin;
import com.example.cos.mapper.AdminMapper;
import com.example.cos.service.AdminService;
import com.example.cos.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理员服务实现类
 */
@Service
public class AdminServiceImpl extends ServiceImpl<AdminMapper, Admin> implements AdminService {

    private static final Logger logger = LoggerFactory.getLogger(AdminServiceImpl.class);

    @Autowired
    private AdminMapper adminMapper;

    @Autowired
    private JwtUtil jwtUtil;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public AdminLoginResultDTO login(AdminLoginDTO loginDTO) {
        logger.info("管理员登录请求: {}", loginDTO);

        // 查询管理员
        Admin admin = findByUsernameAndStatus(loginDTO.getUsername(), 1);
        if (admin == null) {
            throw new RuntimeException("用户名不存在或账号已被禁用");
        }

        // 验证密码
        if (!validatePassword(loginDTO.getPassword(), admin.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        // 生成JWT令牌
        String token = jwtUtil.generateToken(admin.getId(), admin.getUsername(), admin.getRole());
        Long expiresIn = jwtUtil.getExpirationTime();

        AdminLoginResultDTO result = new AdminLoginResultDTO(
                token, expiresIn, admin.getId(), admin.getUsername(), admin.getRole()
        );

        logger.info("管理员登录成功: {}", admin.getUsername());
        return result;
    }

    @Override
    public Admin findByUsername(String username) {
        return adminMapper.findByUsername(username);
    }

    @Override
    public Admin findByUsernameAndStatus(String username, Integer status) {
        return adminMapper.findByUsernameAndStatus(username, status);
    }

    @Override
    public Admin createAdmin(String username, String password, String role) {
        logger.info("创建管理员: {}", username);

        // 检查用户名是否已存在
        Admin existingAdmin = findByUsername(username);
        if (existingAdmin != null) {
            throw new RuntimeException("用户名已存在: " + username);
        }

        // 创建管理员
        Admin admin = new Admin();
        admin.setUsername(username);
        admin.setPassword(encodePassword(password));
        admin.setRole(role);
        admin.setStatus(1);
        admin.setCreatedTime(LocalDateTime.now());

        boolean saved = save(admin);
        if (!saved) {
            throw new RuntimeException("管理员创建失败");
        }

        logger.info("管理员创建成功: {}", admin);
        return admin;
    }

    @Override
    public boolean updatePassword(Integer adminId, String oldPassword, String newPassword) {
        logger.info("更新管理员密码: {}", adminId);

        Admin admin = getById(adminId);
        if (admin == null) {
            throw new RuntimeException("管理员不存在");
        }

        // 验证旧密码
        if (!validatePassword(oldPassword, admin.getPassword())) {
            throw new RuntimeException("原密码错误");
        }

        // 更新密码
        admin.setPassword(encodePassword(newPassword));
        return updateById(admin);
    }

    @Override
    public boolean updateStatus(Integer adminId, Integer status) {
        logger.info("更新管理员状态: {} -> {}", adminId, status);

        Admin admin = getById(adminId);
        if (admin == null) {
            throw new RuntimeException("管理员不存在");
        }

        admin.setStatus(status);
        return updateById(admin);
    }

    @Override
    public boolean validatePassword(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    @Override
    public String encodePassword(String rawPassword) {
        return passwordEncoder.encode(rawPassword);
    }

    @Override
    public List<Admin> getAllAdmins() {
        logger.info("获取所有管理员列表");
        QueryWrapper<Admin> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("created_time");
        return list(queryWrapper);
    }

    @Override
    @Transactional
    public Admin createAdmin(AdminCreateDTO createDTO) {
        logger.info("创建管理员: {}", createDTO);

        // 验证用户名是否已存在
        if (isUsernameExists(createDTO.getUsername())) {
            throw new RuntimeException("用户名已存在: " + createDTO.getUsername());
        }

        // 创建管理员实体
        Admin admin = new Admin();
        admin.setUsername(createDTO.getUsername());
        admin.setPassword(encodePassword(createDTO.getPassword()));
        admin.setRole(createDTO.getRole());
        admin.setStatus(createDTO.getStatus());
        admin.setCreatedTime(LocalDateTime.now());

        // 保存到数据库
        boolean saved = save(admin);
        if (!saved) {
            throw new RuntimeException("创建管理员失败");
        }

        logger.info("管理员创建成功: {}", admin.getId());
        return admin;
    }

    @Override
    @Transactional
    public Admin updateAdmin(AdminUpdateDTO updateDTO) {
        logger.info("更新管理员: {}", updateDTO);

        // 验证管理员是否存在
        Admin existingAdmin = getById(updateDTO.getId());
        if (existingAdmin == null) {
            throw new RuntimeException("管理员不存在: " + updateDTO.getId());
        }

        // 更新密码（如果提供）
        if (StringUtils.hasText(updateDTO.getPassword())) {
            existingAdmin.setPassword(encodePassword(updateDTO.getPassword()));
        }

        // 更新角色（如果提供）
        if (StringUtils.hasText(updateDTO.getRole())) {
            existingAdmin.setRole(updateDTO.getRole());
        }

        // 更新状态（如果提供）
        if (updateDTO.getStatus() != null) {
            existingAdmin.setStatus(updateDTO.getStatus());
        }

        // 保存到数据库
        boolean updated = updateById(existingAdmin);
        if (!updated) {
            throw new RuntimeException("更新管理员失败");
        }

        logger.info("管理员更新成功: {}", existingAdmin.getId());
        return existingAdmin;
    }

    @Override
    @Transactional
    public boolean deleteAdmin(Integer adminId) {
        logger.info("删除管理员: {}", adminId);

        // 验证管理员是否存在
        Admin admin = getById(adminId);
        if (admin == null) {
            throw new RuntimeException("管理员不存在: " + adminId);
        }

        // 不能删除超级管理员
        if ("super_admin".equals(admin.getRole())) {
            throw new RuntimeException("不能删除超级管理员");
        }

        boolean deleted = removeById(adminId);
        if (deleted) {
            logger.info("管理员删除成功: {}", adminId);
        }

        return deleted;
    }

    @Override
    @Transactional
    public boolean batchDeleteAdmins(List<Integer> adminIds) {
        logger.info("批量删除管理员: {}", adminIds);

        if (CollectionUtils.isEmpty(adminIds)) {
            throw new IllegalArgumentException("管理员ID列表不能为空");
        }

        // 检查是否包含超级管理员
        List<Admin> admins = listByIds(adminIds);
        for (Admin admin : admins) {
            if ("super_admin".equals(admin.getRole())) {
                throw new RuntimeException("不能删除超级管理员: " + admin.getUsername());
            }
        }

        boolean deleted = removeByIds(adminIds);
        if (deleted) {
            logger.info("批量删除管理员成功，数量: {}", adminIds.size());
        }

        return deleted;
    }

    @Override
    @Transactional
    public Admin toggleAdminStatus(Integer adminId) {
        logger.info("切换管理员状态: {}", adminId);

        // 验证管理员是否存在
        Admin admin = getById(adminId);
        if (admin == null) {
            throw new RuntimeException("管理员不存在: " + adminId);
        }

        // 不能禁用超级管理员
        if ("super_admin".equals(admin.getRole()) && admin.getStatus() == 1) {
            throw new RuntimeException("不能禁用超级管理员");
        }

        // 切换状态
        admin.setStatus(admin.getStatus() == 1 ? 0 : 1);

        // 保存到数据库
        boolean updated = updateById(admin);
        if (!updated) {
            throw new RuntimeException("切换管理员状态失败");
        }

        logger.info("管理员状态切换成功: {} -> {}", adminId, admin.getStatus());
        return admin;
    }

    @Override
    @Transactional
    public boolean resetAdminPassword(Integer adminId, String newPassword) {
        logger.info("重置管理员密码: {}", adminId);

        // 验证管理员是否存在
        Admin admin = getById(adminId);
        if (admin == null) {
            throw new RuntimeException("管理员不存在: " + adminId);
        }

        // 更新密码
        admin.setPassword(encodePassword(newPassword));

        // 保存到数据库
        boolean updated = updateById(admin);
        if (updated) {
            logger.info("管理员密码重置成功: {}", adminId);
        }

        return updated;
    }

    @Override
    public boolean isUsernameExists(String username) {
        QueryWrapper<Admin> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        return count(queryWrapper) > 0;
    }

    @Override
    public boolean isUsernameExists(String username, Integer excludeId) {
        QueryWrapper<Admin> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        return count(queryWrapper) > 0;
    }
}
