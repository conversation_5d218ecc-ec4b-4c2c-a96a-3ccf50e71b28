// 全局样式文件

// 主题色彩变量
:root {
  --primary-color: #FFB6C1;      // 淡粉色
  --primary-light: #FFC0CB;      // 粉色
  --primary-lighter: #FFE4E1;    // 浅粉色
  --primary-dark: #FF91A4;       // 深粉色
  
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #C0C4CC;
  
  --border-color: #DCDFE6;
  --border-light: #E4E7ED;
  --border-lighter: #EBEEF5;
  --border-extra-light: #F2F6FC;
  
  --background-color: #F5F5F5;
  --background-light: #FAFAFA;
}

// 重置Element Plus主题色
.el-button--primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  
  &:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-light);
  }
  
  &:active {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
  }
}

.el-menu--horizontal .el-menu-item.is-active {
  border-bottom-color: var(--primary-color);
  color: var(--primary-color);
}

.el-menu-item.is-active {
  background-color: var(--primary-lighter) !important;
  color: var(--primary-color) !important;
}

// 全局布局样式
.app-container {
  padding: 20px;
  background-color: #fff;
  margin: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-lighter);
  
  h2 {
    margin: 0;
    color: var(--text-primary);
    font-size: 20px;
    font-weight: 500;
  }
}

.search-form {
  background-color: var(--background-light);
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 20px;
  
  .el-form-item {
    margin-bottom: 15px;
  }
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  
  .toolbar-left {
    display: flex;
    gap: 10px;
  }
  
  .toolbar-right {
    display: flex;
    gap: 10px;
    align-items: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    margin: 10px;
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .table-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .search-form {
    padding: 15px;
  }
}

// 工具类
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mb-10 { margin-bottom: 10px; }
.mb-15 { margin-bottom: 15px; }
.mb-20 { margin-bottom: 20px; }

.mt-10 { margin-top: 10px; }
.mt-15 { margin-top: 15px; }
.mt-20 { margin-top: 20px; }

.flex { display: flex; }
.flex-center { 
  display: flex; 
  justify-content: center; 
  align-items: center; 
}
.flex-between { 
  display: flex; 
  justify-content: space-between; 
  align-items: center; 
}

// 状态标签样式
.status-tag {
  &.active { color: var(--success-color); }
  &.inactive { color: var(--danger-color); }
  &.pending { color: var(--warning-color); }
}
