package com.example.cos.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 管理员登录结果DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "管理员登录结果")
public class AdminLoginResultDTO {

    @ApiModelProperty(value = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @ApiModelProperty(value = "令牌类型", example = "Bearer")
    private String tokenType = "Bearer";

    @ApiModelProperty(value = "过期时间（秒）", example = "7200")
    private Long expiresIn;

    @ApiModelProperty(value = "管理员ID", example = "1")
    private Integer adminId;

    @ApiModelProperty(value = "用户名", example = "admin")
    private String username;

    @ApiModelProperty(value = "角色", example = "超级管理员")
    private String role;

    public AdminLoginResultDTO() {}

    public AdminLoginResultDTO(String token, Long expiresIn, Integer adminId, String username, String role) {
        this.token = token;
        this.expiresIn = expiresIn;
        this.adminId = adminId;
        this.username = username;
        this.role = role;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public Long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    @Override
    public String toString() {
        return "AdminLoginResultDTO{" +
                "token='[PROTECTED]'" +
                ", tokenType='" + tokenType + '\'' +
                ", expiresIn=" + expiresIn +
                ", adminId=" + adminId +
                ", username='" + username + '\'' +
                ", role='" + role + '\'' +
                '}';
    }
}
