package com.example.cos.service.impl;

import com.example.cos.config.CosProperties;
import com.example.cos.dto.FileUploadResult;
import com.example.cos.service.CosService;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 腾讯云COS服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class CosServiceImpl implements CosService {

    private static final Logger logger = LoggerFactory.getLogger(CosServiceImpl.class);

    @Autowired
    private COSClient cosClient;

    @Autowired
    private CosProperties cosProperties;

    @Override
    public FileUploadResult uploadFile(MultipartFile file, String filePath) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }

        try {
            // 生成文件路径
            String finalFilePath = generateFilePath(file, filePath);
            
            // 创建上传请求
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());

            // 设置正确的Content-Type，确保图片和视频能够在浏览器中预览
            String contentType = determineContentType(file);
            metadata.setContentType(contentType);

            // 设置缓存控制，允许浏览器缓存
            metadata.setCacheControl("public, max-age=31536000");

            // 对于图片和视频，设置为inline显示而不是attachment下载
            if (isPreviewableFile(contentType)) {
                metadata.setContentDisposition("inline");
            }
            
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    cosProperties.getBucketName(),
                    finalFilePath,
                    file.getInputStream(),
                    metadata
            );

            // 执行上传
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            
            logger.info("文件上传成功: {}, ETag: {}", finalFilePath, putObjectResult.getETag());

            // 构建返回结果
            String fileUrl = getFileUrl(finalFilePath);
            return new FileUploadResult(
                    file.getOriginalFilename(),
                    file.getSize(),
                    file.getContentType(),
                    fileUrl,
                    finalFilePath
            );

        } catch (CosServiceException e) {
            logger.error("COS服务异常，上传文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("COS服务异常: " + e.getErrorMessage(), e);
        } catch (CosClientException e) {
            logger.error("COS客户端异常，上传文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("COS客户端异常: " + e.getMessage(), e);
        } catch (IOException e) {
            logger.error("读取文件流异常，上传文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("读取文件流异常: " + e.getMessage(), e);
        }
    }

    @Override
    public List<FileUploadResult> uploadFiles(List<MultipartFile> files, String filePathPrefix) {
        if (files == null || files.isEmpty()) {
            throw new IllegalArgumentException("上传文件列表不能为空");
        }

        List<FileUploadResult> results = new ArrayList<>();
        for (MultipartFile file : files) {
            if (file != null && !file.isEmpty()) {
                String filePath = StringUtils.hasText(filePathPrefix) ?
                        filePathPrefix + "/" + generateFileName(file) : null;
                FileUploadResult result = uploadFile(file, filePath);
                results.add(result);
            }
        }
        return results;
    }

    @Override
    public void downloadFile(String filePath, HttpServletResponse response) {
        if (!StringUtils.hasText(filePath)) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        try {
            // 检查文件是否存在
            if (!fileExists(filePath)) {
                throw new RuntimeException("文件不存在: " + filePath);
            }

            // 获取文件对象
            GetObjectRequest getObjectRequest = new GetObjectRequest(cosProperties.getBucketName(), filePath);
            COSObject cosObject = cosClient.getObject(getObjectRequest);

            // 设置响应头
            ObjectMetadata metadata = cosObject.getObjectMetadata();
            String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
            String contentType = metadata.getContentType();

            response.setContentType(contentType);
            response.setContentLengthLong(metadata.getContentLength());

            // 根据文件类型决定是预览还是下载
            if (isPreviewableFile(contentType)) {
                // 图片、视频等可预览文件设置为inline，在浏览器中直接显示
                response.setHeader("Content-Disposition", "inline; filename=\"" + fileName + "\"");
            } else {
                // 其他文件类型设置为attachment，强制下载
                response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            }

            // 设置缓存控制
            response.setHeader("Cache-Control", "public, max-age=31536000");
            response.setHeader("Expires", "Thu, 31 Dec 2037 23:55:55 GMT");

            // 写入响应流
            try (InputStream inputStream = cosObject.getObjectContent();
                 OutputStream outputStream = response.getOutputStream()) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }

            logger.info("文件下载成功: {}", filePath);

        } catch (CosServiceException e) {
            logger.error("COS服务异常，下载文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("COS服务异常: " + e.getErrorMessage(), e);
        } catch (CosClientException e) {
            logger.error("COS客户端异常，下载文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("COS客户端异常: " + e.getMessage(), e);
        } catch (IOException e) {
            logger.error("文件流处理异常，下载文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件流处理异常: " + e.getMessage(), e);
        }
    }

    @Override
    public void forceDownloadFile(String filePath, HttpServletResponse response) {
        if (!StringUtils.hasText(filePath)) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        try {
            // 检查文件是否存在
            if (!fileExists(filePath)) {
                throw new RuntimeException("文件不存在: " + filePath);
            }

            // 获取文件对象
            GetObjectRequest getObjectRequest = new GetObjectRequest(cosProperties.getBucketName(), filePath);
            COSObject cosObject = cosClient.getObject(getObjectRequest);

            // 设置响应头 - 强制下载
            ObjectMetadata metadata = cosObject.getObjectMetadata();
            String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
            String contentType = metadata.getContentType();

            response.setContentType(contentType);
            response.setContentLengthLong(metadata.getContentLength());

            // 强制设置为attachment，无论什么文件类型都下载
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // 设置缓存控制
            response.setHeader("Cache-Control", "public, max-age=31536000");
            response.setHeader("Expires", "Thu, 31 Dec 2037 23:55:55 GMT");

            // 写入响应流
            try (InputStream inputStream = cosObject.getObjectContent();
                 OutputStream outputStream = response.getOutputStream()) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }

            logger.info("文件强制下载成功: {}", filePath);

        } catch (CosServiceException e) {
            logger.error("COS服务异常，强制下载文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("COS服务异常: " + e.getErrorMessage(), e);
        } catch (CosClientException e) {
            logger.error("COS客户端异常，强制下载文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("COS客户端异常: " + e.getMessage(), e);
        } catch (IOException e) {
            logger.error("文件流处理异常，强制下载文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件流处理异常: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteFile(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        try {
            // 检查文件是否存在
            if (!fileExists(filePath)) {
                logger.warn("要删除的文件不存在: {}", filePath);
                return false;
            }

            // 删除文件
            cosClient.deleteObject(cosProperties.getBucketName(), filePath);

            logger.info("文件删除成功: {}", filePath);
            return true;

        } catch (CosServiceException e) {
            logger.error("COS服务异常，删除文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("COS服务异常: " + e.getErrorMessage(), e);
        } catch (CosClientException e) {
            logger.error("COS客户端异常，删除文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("COS客户端异常: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean fileExists(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return false;
        }

        try {
            cosClient.getObjectMetadata(cosProperties.getBucketName(), filePath);
            return true;
        } catch (CosServiceException e) {
            if (e.getStatusCode() == 404) {
                return false;
            }
            logger.error("检查文件是否存在时发生COS服务异常: {}", e.getMessage(), e);
            throw new RuntimeException("COS服务异常: " + e.getErrorMessage(), e);
        } catch (CosClientException e) {
            logger.error("检查文件是否存在时发生COS客户端异常: {}", e.getMessage(), e);
            throw new RuntimeException("COS客户端异常: " + e.getMessage(), e);
        }
    }

    @Override
    public String getFileUrl(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return null;
        }

        // 如果启用了CDN且配置了CDN域名，使用CDN域名
        if (Boolean.TRUE.equals(cosProperties.getEnableCdn()) && StringUtils.hasText(cosProperties.getCdnDomain())) {
            return String.format("https://%s/%s", cosProperties.getCdnDomain(), filePath);
        }

        // 否则使用COS原始域名
        return String.format("https://%s.cos.%s.myqcloud.com/%s",
                cosProperties.getBucketName(),
                cosProperties.getRegion(),
                filePath);
    }

    /**
     * 生成文件路径
     */
    private String generateFilePath(MultipartFile file, String customPath) {
        if (StringUtils.hasText(customPath)) {
            return customPath;
        }

        // 按日期分目录存储
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        String datePath = sdf.format(new Date());

        // 生成唯一文件名
        String fileName = generateFileName(file);

        return datePath + "/" + fileName;
    }

    /**
     * 生成唯一文件名
     */
    private String generateFileName(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFilename)) {
            originalFilename = "unknown";
        }

        // 获取文件扩展名
        String extension = "";
        int lastDotIndex = originalFilename.lastIndexOf(".");
        if (lastDotIndex > 0) {
            extension = originalFilename.substring(lastDotIndex);
        }

        // 生成UUID文件名
        return UUID.randomUUID().toString().replace("-", "") + extension;
    }

    /**
     * 确定文件的Content-Type
     */
    private String determineContentType(MultipartFile file) {
        String contentType = file.getContentType();
        String originalFilename = file.getOriginalFilename();

        // 如果MultipartFile没有提供Content-Type或者不准确，根据文件扩展名判断
        if (!StringUtils.hasText(contentType) || "application/octet-stream".equals(contentType)) {
            if (StringUtils.hasText(originalFilename)) {
                String extension = originalFilename.toLowerCase();

                // 图片类型
                if (extension.endsWith(".jpg") || extension.endsWith(".jpeg")) {
                    return "image/jpeg";
                } else if (extension.endsWith(".png")) {
                    return "image/png";
                } else if (extension.endsWith(".gif")) {
                    return "image/gif";
                } else if (extension.endsWith(".bmp")) {
                    return "image/bmp";
                } else if (extension.endsWith(".webp")) {
                    return "image/webp";
                } else if (extension.endsWith(".svg")) {
                    return "image/svg+xml";
                }
                // 视频类型
                else if (extension.endsWith(".mp4")) {
                    return "video/mp4";
                } else if (extension.endsWith(".avi")) {
                    return "video/avi";
                } else if (extension.endsWith(".mov")) {
                    return "video/quicktime";
                } else if (extension.endsWith(".wmv")) {
                    return "video/x-ms-wmv";
                } else if (extension.endsWith(".flv")) {
                    return "video/x-flv";
                } else if (extension.endsWith(".webm")) {
                    return "video/webm";
                } else if (extension.endsWith(".mkv")) {
                    return "video/x-matroska";
                }
                // 音频类型
                else if (extension.endsWith(".mp3")) {
                    return "audio/mpeg";
                } else if (extension.endsWith(".wav")) {
                    return "audio/wav";
                } else if (extension.endsWith(".ogg")) {
                    return "audio/ogg";
                }
                // PDF
                else if (extension.endsWith(".pdf")) {
                    return "application/pdf";
                }
            }
        }

        return StringUtils.hasText(contentType) ? contentType : "application/octet-stream";
    }

    /**
     * 判断文件是否可以在浏览器中预览
     */
    private boolean isPreviewableFile(String contentType) {
        if (!StringUtils.hasText(contentType)) {
            return false;
        }

        String lowerContentType = contentType.toLowerCase();

        // 图片类型
        if (lowerContentType.startsWith("image/")) {
            return true;
        }

        // 视频类型
        if (lowerContentType.startsWith("video/")) {
            return true;
        }

        // 音频类型
        if (lowerContentType.startsWith("audio/")) {
            return true;
        }

        // PDF
        if ("application/pdf".equals(lowerContentType)) {
            return true;
        }

        // 文本类型
        if (lowerContentType.startsWith("text/")) {
            return true;
        }

        return false;
    }
}
