package com.example.cos.common;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ResultCode {

    /**
     * 成功
     */
    SUCCESS(200, "操作成功"),

    /**
     * 参数错误
     */
    PARAM_ERROR(400, "参数错误"),

    /**
     * 未授权
     */
    UNAUTHORIZED(401, "未授权"),

    /**
     * 禁止访问
     */
    FORBIDDEN(403, "禁止访问"),

    /**
     * 资源不存在
     */
    NOT_FOUND(404, "资源不存在"),

    /**
     * 服务器内部错误
     */
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),

    /**
     * 业务逻辑错误
     */
    BUSINESS_ERROR(600, "业务逻辑错误"),

    /**
     * 文件上传失败
     */
    FILE_UPLOAD_ERROR(1001, "文件上传失败"),

    /**
     * 文件下载失败
     */
    FILE_DOWNLOAD_ERROR(1002, "文件下载失败"),

    /**
     * 文件删除失败
     */
    FILE_DELETE_ERROR(1003, "文件删除失败"),

    /**
     * 文件不存在
     */
    FILE_NOT_EXISTS(1004, "文件不存在"),

    /**
     * 文件格式不支持
     */
    FILE_FORMAT_NOT_SUPPORTED(1005, "文件格式不支持"),

    /**
     * 文件大小超出限制
     */
    FILE_SIZE_EXCEEDED(1006, "文件大小超出限制"),

    /**
     * COS服务异常
     */
    COS_SERVICE_ERROR(1007, "COS服务异常");

    private final int code;
    private final String message;

    ResultCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
