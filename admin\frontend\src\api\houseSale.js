import request from '@/utils/request'

// 房源出售登记API

/**
 * 创建房源出售登记
 * @param {Object} data 登记数据
 */
export function createHouseSale(data) {
  return request({
    url: '/house-sale/create',
    method: 'post',
    data
  })
}

/**
 * 根据ID查询房源出售登记
 * @param {Number} id 登记ID
 */
export function getHouseSaleById(id) {
  return request({
    url: `/house-sale/${id}`,
    method: 'get'
  })
}

/**
 * 根据用户ID查询房源出售登记
 * @param {Number} userId 用户ID
 */
export function getHouseSalesByUserId(userId) {
  return request({
    url: '/house-sale/user',
    method: 'get',
    params: { userId }
  })
}

/**
 * 根据城市查询房源出售登记
 * @param {String} city 城市
 */
export function getHouseSalesByCity(city) {
  return request({
    url: '/house-sale/city',
    method: 'get',
    params: { city }
  })
}

/**
 * 根据小区名称查询房源出售登记
 * @param {String} communityName 小区名称
 */
export function getHouseSalesByCommunity(communityName) {
  return request({
    url: '/house-sale/community',
    method: 'get',
    params: { communityName }
  })
}

/**
 * 根据房屋类型查询房源出售登记
 * @param {String} houseType 房屋类型
 */
export function getHouseSalesByType(houseType) {
  return request({
    url: '/house-sale/type',
    method: 'get',
    params: { houseType }
  })
}

/**
 * 更新房源出售登记
 * @param {Object} data 登记数据
 */
export function updateHouseSale(data) {
  return request({
    url: '/house-sale/update',
    method: 'put',
    data
  })
}

/**
 * 删除房源出售登记
 * @param {Number} id 登记ID
 */
export function deleteHouseSale(id) {
  return request({
    url: `/house-sale/${id}`,
    method: 'delete'
  })
}

/**
 * 查询所有房源出售登记
 */
export function getAllHouseSales() {
  return request({
    url: '/house-sale/all',
    method: 'get'
  })
}
