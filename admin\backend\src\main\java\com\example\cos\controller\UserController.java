package com.example.cos.controller;

import com.example.cos.common.Result;
import com.example.cos.common.ResultCode;
import com.example.cos.dto.PageResultDTO;
import com.example.cos.dto.UserCreateDTO;
import com.example.cos.dto.UserQueryDTO;
import com.example.cos.dto.UserRoleUpdateDTO;
import com.example.cos.entity.HouseResource;
import com.example.cos.entity.User;
import com.example.cos.service.HouseResourceService;
import com.example.cos.service.UserService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 用户管理控制器
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/api/user")
@Validated
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private HouseResourceService houseResourceService;

    /**
     * 创建用户
     */
    @ApiOperation(value = "创建用户", notes = "创建新用户")
    @PostMapping("/create")
    public Result<User> createUser(@RequestBody @Validated UserCreateDTO userCreateDTO) {
        try {
            logger.info("创建用户请求: {}", userCreateDTO);
            
            User user = userService.createUser(userCreateDTO);
            
            logger.info("用户创建成功: {}", user.getId());
            return Result.success("用户创建成功", user);
            
        } catch (IllegalArgumentException e) {
            logger.warn("用户创建参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("用户创建失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "用户创建失败: " + e.getMessage());
        }
    }

    /**
     * 根据手机号查询用户
     */
    @ApiOperation(value = "根据手机号查询用户", notes = "通过手机号查询用户信息")
    @GetMapping("/phone")
    public Result<User> getUserByPhone(@RequestParam String phoneNumber) {
        try {
            logger.info("根据手机号查询用户: {}", phoneNumber);
            
            User user = userService.findByPhoneNumber(phoneNumber);
            
            if (user == null) {
                return Result.error(ResultCode.NOT_FOUND.getCode(), "用户不存在");
            }
            
            return Result.success("查询成功", user);
            
        } catch (Exception e) {
            logger.error("查询用户失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询用户失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID查询用户
     */
    @ApiOperation(value = "根据用户ID查询用户", notes = "通过用户ID查询用户信息")
    @GetMapping("/{id}")
    public Result<User> getUserById(@PathVariable Integer id) {
        try {
            logger.info("根据ID查询用户: {}", id);
            
            User user = userService.getById(id);
            
            if (user == null) {
                return Result.error(ResultCode.NOT_FOUND.getCode(), "用户不存在");
            }
            
            return Result.success("查询成功", user);
            
        } catch (Exception e) {
            logger.error("查询用户失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询用户失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @ApiOperation(value = "更新用户信息", notes = "更新用户基本信息")
    @PutMapping("/update")
    public Result<Boolean> updateUser(@RequestBody User user) {
        try {
            logger.info("更新用户信息: {}", user);

            boolean updated = userService.updateUser(user);

            if (updated) {
                return Result.success("更新成功", true);
            } else {
                return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "更新失败");
            }

        } catch (Exception e) {
            logger.error("更新用户失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "更新用户失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户角色
     */
    @ApiOperation(value = "更新用户角色", notes = "更新用户的角色权限")
    @PutMapping("/{userId}/role")
    public Result<Boolean> updateUserRole(@PathVariable Integer userId, @RequestBody UserRoleUpdateDTO roleUpdateDTO) {
        try {
            logger.info("更新用户{}角色: {}", userId, roleUpdateDTO);

            boolean updated = userService.updateUserRole(userId, roleUpdateDTO.getRole());

            if (updated) {
                return Result.success("角色更新成功", true);
            } else {
                return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "角色更新失败");
            }

        } catch (IllegalArgumentException e) {
            logger.warn("角色更新参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("角色更新失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "角色更新失败: " + e.getMessage());
        }
    }

    /**
     * 添加收藏房源
     */
    @ApiOperation(value = "添加收藏房源", notes = "用户收藏房源")
    @PostMapping("/{userId}/favorite")
    public Result<Boolean> addFavoriteHouse(@PathVariable Integer userId, @RequestParam Integer houseId) {
        try {
            logger.info("用户{}添加收藏房源: {}", userId, houseId);
            
            boolean added = userService.addFavoriteHouse(userId, houseId);
            
            return Result.success("收藏成功", added);
            
        } catch (Exception e) {
            logger.error("添加收藏失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "添加收藏失败: " + e.getMessage());
        }
    }

    /**
     * 移除收藏房源
     */
    @ApiOperation(value = "移除收藏房源", notes = "用户取消收藏房源")
    @DeleteMapping("/{userId}/favorite")
    public Result<Boolean> removeFavoriteHouse(@PathVariable Integer userId, @RequestParam Integer houseId) {
        try {
            logger.info("用户{}移除收藏房源: {}", userId, houseId);
            
            boolean removed = userService.removeFavoriteHouse(userId, houseId);
            
            return Result.success("取消收藏成功", removed);
            
        } catch (Exception e) {
            logger.error("取消收藏失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "取消收藏失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户收藏的房源列表
     */
    @ApiOperation(value = "获取用户收藏的房源列表", notes = "查询用户收藏的所有房源")
    @GetMapping("/{userId}/favorites")
    public Result<List<HouseResource>> getFavoriteHouses(@PathVariable Integer userId) {
        try {
            logger.info("获取用户{}收藏的房源列表", userId);

            List<Integer> favoriteHouseIds = userService.getFavoriteHouseIds(userId);

            // 如果收藏列表为空，直接返回空列表
            if (favoriteHouseIds == null || favoriteHouseIds.isEmpty()) {
                logger.info("用户{}没有收藏的房源", userId);
                return Result.success("查询成功", Collections.emptyList());
            }

            List<Long> favoriteHouseIdsLong = favoriteHouseIds.stream().map(Integer::longValue).collect(java.util.stream.Collectors.toList());
            List<HouseResource> favoriteHouses = houseResourceService.findByIds(favoriteHouseIdsLong);

            return Result.success("查询成功", favoriteHouses);

        } catch (Exception e) {
            logger.error("查询收藏房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询收藏房源失败: " + e.getMessage());
        }
    }

    /**
     * 添加关注房源
     */
    @ApiOperation(value = "添加关注房源", notes = "用户关注房源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "int", paramType = "path"),
            @ApiImplicitParam(name = "houseId", value = "房源ID", required = true, dataType = "int", paramType = "query")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "关注成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/{userId}/follow")
    public Result<Boolean> addFollowedHouse(@PathVariable Integer userId, @RequestParam Integer houseId) {
        try {
            logger.info("用户{}添加关注房源: {}", userId, houseId);

            boolean added = userService.addFollowedHouse(userId, houseId);

            return Result.success("关注成功", added);

        } catch (Exception e) {
            logger.error("添加关注失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "添加关注失败: " + e.getMessage());
        }
    }

    /**
     * 移除关注房源
     */
    @ApiOperation(value = "移除关注房源", notes = "用户取消关注房源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "int", paramType = "path"),
            @ApiImplicitParam(name = "houseId", value = "房源ID", required = true, dataType = "int", paramType = "query")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "取消关注成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @DeleteMapping("/{userId}/follow")
    public Result<Boolean> removeFollowedHouse(@PathVariable Integer userId, @RequestParam Integer houseId) {
        try {
            logger.info("用户{}移除关注房源: {}", userId, houseId);

            boolean removed = userService.removeFollowedHouse(userId, houseId);

            return Result.success("取消关注成功", removed);

        } catch (Exception e) {
            logger.error("取消关注失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "取消关注失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户关注的房源列表
     */
    @ApiOperation(value = "获取用户关注的房源列表", notes = "查询用户关注的所有房源")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "int", paramType = "path")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/{userId}/follows")
    public Result<List<HouseResource>> getFollowedHouses(@PathVariable Integer userId) {
        try {
            logger.info("获取用户{}关注的房源列表", userId);

            List<Integer> followedHouseIds = userService.getFollowedHouseIds(userId);

            // 如果关注列表为空，直接返回空列表
            if (followedHouseIds == null || followedHouseIds.isEmpty()) {
                logger.info("用户{}没有关注的房源", userId);
                return Result.success("查询成功", Collections.emptyList());
            }

            List<Long> followedHouseIdsLong = followedHouseIds.stream().map(Integer::longValue).collect(java.util.stream.Collectors.toList());
            List<HouseResource> followedHouses = houseResourceService.findByIds(followedHouseIdsLong);

            return Result.success("查询成功", followedHouses);

        } catch (Exception e) {
            logger.error("查询关注房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询关注房源失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询用户列表
     */
    @ApiOperation(value = "分页查询用户列表", notes = "支持多条件搜索的用户分页查询")
    @PostMapping("/list")
    public Result<PageResultDTO<User>> getUserList(@RequestBody UserQueryDTO queryDTO) {
        try {
            logger.info("分页查询用户列表: {}", queryDTO);

            PageResultDTO<User> pageResult = userService.getUserList(queryDTO);

            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            logger.error("查询用户列表失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @ApiOperation(value = "删除用户", notes = "根据用户ID删除用户")
    @DeleteMapping("/{userId}")
    public Result<Boolean> deleteUser(@PathVariable Integer userId) {
        try {
            logger.info("删除用户: {}", userId);

            boolean deleted = userService.deleteUser(userId);

            return Result.success("删除成功", deleted);

        } catch (Exception e) {
            logger.error("删除用户失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除用户
     */
    @ApiOperation(value = "批量删除用户", notes = "根据用户ID列表批量删除用户")
    @DeleteMapping("/batch")
    public Result<Boolean> deleteUsers(@RequestBody List<Integer> userIds) {
        try {
            logger.info("批量删除用户: {}", userIds);

            boolean deleted = userService.deleteUsers(userIds);

            return Result.success("批量删除成功", deleted);

        } catch (Exception e) {
            logger.error("批量删除用户失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "批量删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户总数
     */
    @ApiOperation(value = "获取用户总数", notes = "获取系统中的用户总数")
    @GetMapping("/count")
    public Result<Long> getUserCount() {
        try {
            logger.info("获取用户总数");

            Long count = userService.getUserCount();

            return Result.success("查询成功", count);

        } catch (Exception e) {
            logger.error("获取用户总数失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取用户总数失败: " + e.getMessage());
        }
    }

    /**
     * 根据时间段统计用户数量
     */
    @ApiOperation(value = "根据时间段统计用户数量", notes = "统计指定时间段内的用户数量")
    @GetMapping("/count/daterange")
    public Result<Long> getUserCountByDateRange(
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        try {
            logger.info("根据时间段统计用户数量: {} - {}", startTime, endTime);

            Long count = userService.getUserCountByDateRange(startTime, endTime);

            return Result.success("查询成功", count);

        } catch (Exception e) {
            logger.error("统计用户数量失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "统计用户数量失败: " + e.getMessage());
        }
    }
}
