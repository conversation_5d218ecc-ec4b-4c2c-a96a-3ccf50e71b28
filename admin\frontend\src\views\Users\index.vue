<template>
  <div class="app-container">
    <div class="page-header">
      <h2>用户管理</h2>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
        <el-form-item label="手机号" prop="phoneNumber">
          <el-input
            v-model="queryParams.phoneNumber"
            placeholder="请输入手机号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input
            v-model="queryParams.nickname"
            placeholder="请输入昵称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-right">
        <el-tooltip content="刷新" placement="top">
          <el-button circle @click="getList">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="userList"
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="用户ID" prop="id" width="80" />
      <el-table-column label="头像" width="80">
        <template #default="scope">
          <el-avatar :size="40" :src="scope.row.avatarUrl" />
        </template>
      </el-table-column>
      <el-table-column label="昵称" prop="nickname" min-width="120" />
      <el-table-column label="手机号" prop="phoneNumber" width="130" />
      <el-table-column label="微信OpenID" prop="wechatOpenid" min-width="200" show-overflow-tooltip />
      <el-table-column label="角色" width="120">
        <template #default="scope">
          <el-tag :type="scope.row.role === 1 ? 'success' : 'info'">
            {{ getRoleText(scope.row.role) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="收藏数量" width="100">
        <template #default="scope">
          <el-tag type="info">{{ getFavoriteCount(scope.row.favoriteHouseIds) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="关注数量" width="100">
        <template #default="scope">
          <el-tag type="warning">{{ getFollowCount(scope.row.followedHouseIds) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="180" />
      <el-table-column label="操作" fixed="right" width="480">
        <template #default="scope">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <Permission permission="update">
              <el-button type="success" size="small" @click="handleUpdate(scope.row)">
                编辑
              </el-button>
            </Permission>
            <el-button type="info" size="small" @click="handleManageFavorites(scope.row)">
              收藏
            </el-button>
            <el-button type="warning" size="small" @click="handleManageFollows(scope.row)">
              关注
            </el-button>
            <Permission permission="update">
              <el-button
                :type="scope.row.role === 1 ? 'warning' : 'success'"
                size="small"
                @click="handleRoleToggle(scope.row)"
              >
                {{ scope.row.role === 1 ? '降为普通' : '升为权限' }}
              </el-button>
            </Permission>
            <Permission permission="delete">
              <el-button type="danger" size="small" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </Permission>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      :before-close="handleDialogClose"
    >
      <UserForm
        ref="userFormRef"
        :user-data="currentUser"
        :is-edit="isEdit"
        @submit="handleFormSubmit"
      />
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      title="用户详情"
      v-model="viewDialogVisible"
      width="700px"
    >
      <UserDetail :user-data="currentUser" />
    </el-dialog>

    <!-- 收藏管理对话框 -->
    <el-dialog
      title="收藏管理"
      v-model="favoritesDialogVisible"
      width="800px"
    >
      <FavoritesManager
        :user-id="currentUser.id"
        :favorite-house-ids="currentUser.favoriteHouseIds"
        @update="handleFavoritesUpdate"
      />
    </el-dialog>

    <!-- 关注管理对话框 -->
    <el-dialog
      title="关注管理"
      v-model="followsDialogVisible"
      width="800px"
    >
      <FollowsManager
        :user-id="currentUser.id"
        :followed-house-ids="currentUser.followedHouseIds"
        @update="handleFollowsUpdate"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  createUser,
  updateUser,
  updateUserRole,
  getUserList,
  deleteUser,
  deleteUsers,
  getUserCount
} from '@/api/user'
import UserForm from './components/UserForm.vue'
import UserDetail from './components/UserDetail.vue'
import FavoritesManager from './components/FavoritesManager.vue'
import FollowsManager from './components/FollowsManager.vue'

// 响应式数据
const loading = ref(false)
const userList = ref([])
const total = ref(0)
const multipleSelection = ref([])
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const favoritesDialogVisible = ref(false)
const followsDialogVisible = ref(false)
const isEdit = ref(false)
const currentUser = ref({})
const userFormRef = ref()

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  phoneNumber: '',
  nickname: '',
  wechatOpenid: ''
})

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑用户' : '新增用户'
})

// 获取用户列表
const getList = async () => {
  loading.value = true
  try {
    const response = await getUserList(queryParams)

    if (response.code === 200) {
      userList.value = response.data.records || []
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    phoneNumber: '',
    nickname: '',
    wechatOpenid: ''
  })
  getList()
}

// 分页处理
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 多选处理
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

// 新增
const handleAdd = () => {
  currentUser.value = {}
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑
const handleUpdate = (row) => {
  currentUser.value = { ...row }
  isEdit.value = true
  dialogVisible.value = true
}

// 查看详情
const handleView = (row) => {
  currentUser.value = { ...row }
  viewDialogVisible.value = true
}

// 管理收藏
const handleManageFavorites = (row) => {
  currentUser.value = { ...row }
  favoritesDialogVisible.value = true
}

// 管理关注
const handleManageFollows = (row) => {
  currentUser.value = { ...row }
  followsDialogVisible.value = true
}

// 批量导出
const handleBatchExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 表单提交
const handleFormSubmit = async (formData) => {
  try {
    if (isEdit.value) {
      await updateUser(formData)
      ElMessage.success('更新成功')
    } else {
      await createUser(formData)
      ElMessage.success('新增成功')
    }
    
    dialogVisible.value = false
    getList()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '新增失败')
  }
}

// 对话框关闭处理
const handleDialogClose = () => {
  dialogVisible.value = false
  currentUser.value = {}
}

// 收藏更新处理
const handleFavoritesUpdate = () => {
  favoritesDialogVisible.value = false
  getList()
}

// 关注更新处理
const handleFollowsUpdate = () => {
  followsDialogVisible.value = false
  getList()
}

// 角色切换
const handleRoleToggle = async (row) => {
  const newRole = row.role === 1 ? 0 : 1
  const roleText = newRole === 1 ? '有权限用户' : '普通用户'

  try {
    await ElMessageBox.confirm(
      `确定要将用户"${row.nickname || row.phoneNumber}"的角色切换为"${roleText}"吗？`,
      '角色切换确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await updateUserRole(row.id, newRole)

    if (response.code === 200) {
      ElMessage.success('角色切换成功')
      getList() // 刷新列表
    } else {
      ElMessage.error(response.message || '角色切换失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('角色切换失败:', error)
      ElMessage.error('角色切换失败')
    }
  }
}

// 删除用户
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户"${row.nickname || row.phoneNumber}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteUser(row.id)

    if (response.code === 200) {
      ElMessage.success('删除成功')
      getList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除用户
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${multipleSelection.value.length} 个用户吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const userIds = multipleSelection.value.map(user => user.id)
    const response = await deleteUsers(userIds)

    if (response.code === 200) {
      ElMessage.success('批量删除成功')
      multipleSelection.value = []
      getList()
    } else {
      ElMessage.error(response.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除用户失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 工具函数
const getFavoriteCount = (favoriteIds) => {
  return Array.isArray(favoriteIds) ? favoriteIds.length : 0
}

const getFollowCount = (followIds) => {
  return Array.isArray(followIds) ? followIds.length : 0
}

const getRoleText = (role) => {
  return role === 1 ? '有权限用户' : '普通用户'
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;

  .el-button {
    margin: 0;
    flex-shrink: 0;
  }

  // 确保按钮在小屏幕上也能正常显示
  @media (max-width: 1200px) {
    gap: 4px;

    .el-button {
      font-size: 12px;
      padding: 4px 8px;
    }
  }
}
</style>
