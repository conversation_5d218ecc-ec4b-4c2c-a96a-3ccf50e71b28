# -*- coding: utf-8 -*-
"""
API客户端模块 - 处理与后端API的通信
"""
import requests
import json
import time
from typing import Dict, Any, Optional
from config import Config

class APIClient:
    """API客户端类"""
    
    def __init__(self):
        self.base_url = Config.API_BASE_URL
        self.endpoint = Config.API_ENDPOINT
        self.timeout = Config.API_TIMEOUT
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': Config.get_random_user_agent(),
            'Accept-Language': Config.ANTI_SCRAPING['accept_language'],
            'Accept-Encoding': Config.ANTI_SCRAPING['accept_encoding'],
        })
    
    def create_house(self, house_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建房源信息
        
        Args:
            house_data: 房源数据字典
            
        Returns:
            API响应结果
        """
        url = f"{self.base_url}{self.endpoint}"
        
        # 数据验证
        if not self._validate_house_data(house_data):
            return {
                'success': False,
                'error': '房源数据验证失败',
                'data': None
            }
        
        # 发送请求
        try:
            response = self.session.post(
                url,
                json=house_data,
                timeout=self.timeout
            )
            
            # 处理响应
            return self._handle_response(response)
            
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': '请求超时',
                'data': None
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': '连接错误',
                'data': None
            }
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f'请求异常: {str(e)}',
                'data': None
            }
    
    def post(self, endpoint: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        通用POST请求方法

        Args:
            endpoint: API端点路径
            data: 请求数据

        Returns:
            API响应结果，失败时返回None
        """
        url = f"{self.base_url}{endpoint}"

        try:
            response = self.session.post(
                url,
                json=data,
                timeout=self.timeout
            )

            if response.status_code == 200:
                return response.json()
            else:
                print(f"API请求失败: {response.status_code} - {response.text}")
                return None

        except requests.exceptions.Timeout:
            print("API请求超时")
            return None
        except requests.exceptions.ConnectionError:
            print("API连接错误")
            return None
        except requests.exceptions.RequestException as e:
            print(f"API请求异常: {str(e)}")
            return None
        except Exception as e:
            print(f"未知错误: {str(e)}")
            return None

    def create_house_with_retry(self, house_data: Dict[str, Any], max_retries: int = 3) -> Dict[str, Any]:
        """
        带重试机制的创建房源信息

        Args:
            house_data: 房源数据字典
            max_retries: 最大重试次数

        Returns:
            API响应结果
        """
        for attempt in range(max_retries + 1):
            result = self.create_house(house_data)

            if result['success']:
                return result

            if attempt < max_retries:
                # 等待后重试
                delay = Config.get_random_delay('retry')
                time.sleep(delay)
                print(f"API请求失败，{delay:.1f}秒后重试 (第{attempt + 1}次)")
            else:
                print(f"API请求失败，已达到最大重试次数 ({max_retries})")

        return result
    
    def _validate_house_data(self, house_data: Dict[str, Any]) -> bool:
        """
        验证房源数据格式
        
        Args:
            house_data: 房源数据字典
            
        Returns:
            验证结果
        """
        required_fields = ['auctionType', 'title', 'endTime', 'houseType']
        
        for field in required_fields:
            if field not in house_data:
                print(f"缺少必需字段: {field}")
                return False
            
            if not house_data[field] or str(house_data[field]).strip() == '':
                print(f"字段 {field} 不能为空")
                return False
        
        return True
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """
        处理API响应
        
        Args:
            response: requests响应对象
            
        Returns:
            处理后的响应结果
        """
        try:
            # 检查HTTP状态码
            if response.status_code == 200:
                try:
                    data = response.json()
                    return {
                        'success': True,
                        'error': None,
                        'data': data
                    }
                except json.JSONDecodeError:
                    return {
                        'success': True,
                        'error': None,
                        'data': {'message': response.text}
                    }
            else:
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: {response.text}',
                    'data': None
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'响应处理异常: {str(e)}',
                'data': None
            }
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试API连接
        
        Returns:
            连接测试结果
        """
        test_data = {
            'auctionType': '测试',
            'title': '连接测试',
            'endTime': '2024-01-01 00:00:00',
            'houseType': '住宅'
        }
        
        print("正在测试API连接...")
        result = self.create_house(test_data)
        
        if result['success']:
            print("API连接测试成功")
        else:
            print(f"API连接测试失败: {result['error']}")
        
        return result
    
    def close(self):
        """关闭会话"""
        if self.session:
            self.session.close()
