import { createRouter, createWebHistory } from 'vue-router'
import { usePermissionStore } from '@/stores/permission'
import { PERMISSIONS } from '@/utils/permission'

const routes = [
  {
    path: '/',
    redirect: '/admin/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/admin',
    component: () => import('@/layout/index.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          title: '仪表板',
          icon: 'Odometer',
          permissions: [PERMISSIONS.READ]
        }
      },
      {
        path: 'houses',
        name: 'Houses',
        component: () => import('@/views/Houses/index.vue'),
        meta: {
          title: '房源管理',
          icon: 'House',
          permissions: [PERMISSIONS.HOUSE_MANAGE]
        }
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/views/Users/<USER>'),
        meta: {
          title: '用户管理',
          icon: 'User',
          permissions: [PERMISSIONS.USER_MANAGE]
        }
      },
      {
        path: 'house-sales',
        name: 'HouseSales',
        component: () => import('@/views/HouseSales/index.vue'),
        meta: {
          title: '卖房信息管理',
          icon: 'Sell',
          permissions: [PERMISSIONS.HOUSE_SALE_MANAGE]
        }
      },
      {
        path: 'consultations',
        name: 'Consultations',
        component: () => import('@/views/Consultations/index.vue'),
        meta: {
          title: '合作咨询管理',
          icon: 'ChatDotRound',
          permissions: [PERMISSIONS.CONSULTATION_MANAGE]
        }
      },
      {
        path: 'carousels',
        name: 'Carousels',
        component: () => import('@/views/Carousels/index.vue'),
        meta: {
          title: '轮播图管理',
          icon: 'Picture',
          permissions: [PERMISSIONS.SYSTEM_MANAGE]
        }
      },
      {
        path: 'admin-management',
        name: 'AdminManagement',
        component: () => import('@/views/AdminManagement/index.vue'),
        meta: {
          title: '管理员管理',
          icon: 'UserFilled'
          // 移除权限要求，所有管理员都可以访问
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/404.vue'),
    meta: { title: '页面不存在' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 锦绣资产房产管理系统`
  }

  // 登录验证逻辑
  const token = localStorage.getItem('token')
  if (to.path !== '/login' && !token) {
    next('/login')
    return
  }

  if (to.path === '/login' && token) {
    // 如果已登录，访问登录页面时跳转到首页
    next('/admin/dashboard')
    return
  }

  // 权限验证逻辑 - 只对有权限要求的页面进行检查，排除仪表板
  if (token && to.meta.permissions && to.path !== '/admin/dashboard') {
    try {
      const permissionStore = usePermissionStore()

      // 确保权限信息已初始化
      if (!permissionStore.isInitialized) {
        permissionStore.initPermissions()
        // 给权限初始化一点时间
        await new Promise(resolve => setTimeout(resolve, 50))
      }

      // 检查是否有访问权限
      const hasPermission = permissionStore.hasAnyPermission(to.meta.permissions)

      if (!hasPermission) {
        console.log(`用户没有访问 ${to.path} 的权限`, {
          requiredPermissions: to.meta.permissions,
          userPermissions: permissionStore.userPermissions,
          userRole: permissionStore.userRole
        })

        // 没有权限时跳转到仪表板
        next('/admin/dashboard')

        // 延迟显示提示，避免重复提示
        setTimeout(() => {
          import('element-plus').then(({ ElMessage }) => {
            ElMessage.warning(`您没有访问"${to.meta.title}"页面的权限`)
          })
        }, 300)
        return
      }
    } catch (error) {
      console.error('权限检查失败:', error)
      // 权限检查失败时允许访问，避免阻塞
    }
  }

  next()
})

export default router
