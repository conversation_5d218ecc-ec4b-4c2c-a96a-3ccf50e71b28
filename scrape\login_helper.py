# -*- coding: utf-8 -*-
"""
登录助手脚本
用于手动登录网站并保存登录状态，供爬虫程序使用
"""
import time
from scraper import HouseScraper

def manual_login():
    """手动登录助手"""
    print("=" * 60)
    print("登录助手 - 手动登录并保存登录状态")
    print("=" * 60)
    
    scraper = None
    try:
        # 创建爬虫实例
        scraper = HouseScraper()
        
        # 初始化浏览器
        print("正在启动浏览器...")
        if not scraper.init_driver():
            print("✗ 浏览器启动失败")
            return False
        
        # 访问目标网站
        print("正在访问目标网站...")
        if not scraper.navigate_to_target():
            print("✗ 访问网站失败")
            return False
        
        print("\n" + "=" * 60)
        print("请在浏览器中手动完成登录操作")
        print("登录完成后，请回到此窗口按Enter键继续...")
        print("=" * 60)
        
        # 等待用户手动登录
        input("按Enter键继续...")
        
        # 检查登录状态
        print("检查登录状态...")
        if scraper.check_login_status():
            print("✓ 检测到登录状态")
            
            # 保存cookies
            print("保存登录状态...")
            if scraper.save_cookies():
                print("✓ 登录状态保存成功！")
                print("\n现在可以运行main.py，程序将自动使用保存的登录状态")
                return True
            else:
                print("✗ 保存登录状态失败")
                return False
        else:
            print("⚠ 未检测到登录状态，请确认是否已成功登录")
            
            # 询问是否强制保存
            save_anyway = input("是否强制保存当前状态？(y/N): ").lower().strip()
            if save_anyway == 'y':
                if scraper.save_cookies():
                    print("✓ 当前状态已保存")
                    return True
                else:
                    print("✗ 保存失败")
                    return False
            else:
                print("未保存登录状态")
                return False
        
    except Exception as e:
        print(f"✗ 登录助手运行出错: {str(e)}")
        return False
    
    finally:
        if scraper:
            print("正在关闭浏览器...")
            scraper.cleanup()

def check_saved_login():
    """检查已保存的登录状态"""
    print("=" * 60)
    print("检查已保存的登录状态")
    print("=" * 60)
    
    scraper = None
    try:
        # 创建爬虫实例
        scraper = HouseScraper()
        
        # 初始化浏览器
        print("正在启动浏览器...")
        if not scraper.init_driver():
            print("✗ 浏览器启动失败")
            return False
        
        # 访问目标网站并加载cookies
        print("正在访问目标网站并加载登录状态...")
        if not scraper.navigate_to_target():
            print("✗ 访问网站失败")
            return False
        
        # 检查登录状态
        print("检查登录状态...")
        if scraper.check_login_status():
            print("✓ 登录状态有效！")
            return True
        else:
            print("⚠ 登录状态无效或已过期")
            return False
        
    except Exception as e:
        print(f"✗ 检查登录状态出错: {str(e)}")
        return False
    
    finally:
        if scraper:
            print("正在关闭浏览器...")
            scraper.cleanup()

def main():
    """主函数"""
    while True:
        print("\n" + "=" * 60)
        print("登录助手菜单")
        print("=" * 60)
        print("1. 手动登录并保存状态")
        print("2. 检查已保存的登录状态")
        print("3. 退出")
        print("=" * 60)
        
        choice = input("请选择操作 (1-3): ").strip()
        
        if choice == '1':
            manual_login()
        elif choice == '2':
            check_saved_login()
        elif choice == '3':
            print("退出登录助手")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
