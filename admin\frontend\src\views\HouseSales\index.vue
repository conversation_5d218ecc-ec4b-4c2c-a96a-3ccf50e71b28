<template>
  <div class="app-container">
    <div class="page-header">
      <h2>卖房信息管理</h2>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
        <el-form-item label="城市" prop="city">
          <el-input
            v-model="queryParams.city"
            placeholder="请输入城市"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="小区名称" prop="communityName">
          <el-input
            v-model="queryParams.communityName"
            placeholder="请输入小区名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="房屋类型" prop="houseType">
          <el-select v-model="queryParams.houseType" placeholder="请选择类型" clearable style="width: 150px">
            <el-option label="住宅" value="住宅" />
            <el-option label="公寓" value="公寓" />
            <el-option label="别墅" value="别墅" />
            <el-option label="商铺" value="商铺" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson">
          <el-input
            v-model="queryParams.contactPerson"
            placeholder="请输入联系人"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <el-button type="success" @click="handleBatchExport" :disabled="!multipleSelection.length">
          <el-icon><Download /></el-icon>
          批量导出
        </el-button>
        <Permission permission="delete">
          <el-button type="danger" @click="handleBatchDelete" :disabled="!multipleSelection.length">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </Permission>
      </div>
      <div class="toolbar-right">
        <el-tooltip content="刷新" placement="top">
          <el-button circle @click="getList">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="houseSaleList"
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="登记ID" prop="id" width="80" />
      <el-table-column label="城市" prop="city" width="100" />
      <el-table-column label="小区名称" prop="communityName" min-width="150" />
      <el-table-column label="房屋类型" prop="houseType" width="100" />
      <el-table-column label="面积(㎡)" prop="housingArea" width="100" />
      <el-table-column label="期望价格" prop="expectedPrice" width="120">
        <template #default="scope">
          <span class="price">¥{{ formatPrice(scope.row.expectedPrice) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系人" prop="contactPerson" width="100" />
      <el-table-column label="性别" prop="gender" width="80" />
      <el-table-column label="联系方式" prop="contactInfo" width="130" />
      <el-table-column label="创建时间" prop="createTime" width="180" />
      <el-table-column label="操作" fixed="right" width="240">
        <template #default="scope">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <Permission permission="update">
              <el-button type="success" size="small" @click="handleUpdate(scope.row)">
                编辑
              </el-button>
            </Permission>
            <Permission permission="delete">
              <el-button type="danger" size="small" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </Permission>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="800px"
      :before-close="handleDialogClose"
    >
      <HouseSaleForm
        ref="houseSaleFormRef"
        :house-sale-data="currentHouseSale"
        :is-edit="isEdit"
        @submit="handleFormSubmit"
      />
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      title="卖房登记详情"
      v-model="viewDialogVisible"
      width="700px"
    >
      <HouseSaleDetail :house-sale-data="currentHouseSale" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAllHouseSales, createHouseSale, updateHouseSale, deleteHouseSale } from '@/api/houseSale'
import HouseSaleForm from './components/HouseSaleForm.vue'
import HouseSaleDetail from './components/HouseSaleDetail.vue'

// 响应式数据
const loading = ref(false)
const houseSaleList = ref([])
const total = ref(0)
const multipleSelection = ref([])
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const isEdit = ref(false)
const currentHouseSale = ref({})
const houseSaleFormRef = ref()

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  city: '',
  communityName: '',
  houseType: '',
  contactPerson: ''
})

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑卖房登记' : '新增卖房登记'
})

// 获取卖房登记列表
const getList = async () => {
  loading.value = true
  try {
    const response = await getAllHouseSales()
    houseSaleList.value = response.data || []
    total.value = houseSaleList.value.length
  } catch (error) {
    console.error('获取卖房登记列表失败:', error)
    ElMessage.error('获取卖房登记列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    city: '',
    communityName: '',
    houseType: '',
    contactPerson: ''
  })
  getList()
}

// 分页处理
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 多选处理
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

// 新增
const handleAdd = () => {
  currentHouseSale.value = {}
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑
const handleUpdate = (row) => {
  currentHouseSale.value = { ...row }
  isEdit.value = true
  dialogVisible.value = true
}

// 查看详情
const handleView = (row) => {
  currentHouseSale.value = { ...row }
  viewDialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除"${row.contactPerson}"的卖房登记吗？`,
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteHouseSale(row.id)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的${multipleSelection.value.length}条记录吗？`,
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('批量删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 批量导出
const handleBatchExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 表单提交
const handleFormSubmit = async (formData) => {
  try {
    if (isEdit.value) {
      await updateHouseSale(formData)
      ElMessage.success('更新成功')
    } else {
      await createHouseSale(formData)
      ElMessage.success('新增成功')
    }
    
    dialogVisible.value = false
    getList()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '新增失败')
  }
}

// 对话框关闭处理
const handleDialogClose = () => {
  dialogVisible.value = false
  currentHouseSale.value = {}
}

// 工具函数
const formatPrice = (price) => {
  return price ? Number(price).toLocaleString() : '0'
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.price {
  color: #e6a23c;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;

  .el-button {
    margin: 0;
    flex-shrink: 0;
  }

  @media (max-width: 1200px) {
    gap: 4px;

    .el-button {
      font-size: 12px;
      padding: 4px 8px;
    }
  }
}
</style>
