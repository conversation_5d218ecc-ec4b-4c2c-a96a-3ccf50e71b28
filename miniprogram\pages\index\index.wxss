/* pages/index/index.wxss */
.container {
  background-color: #f5f5f5;
  width: 100%;
  padding: 0;
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 160rpx;
  background-color: #ffffff;
  z-index: 1000;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.navbar-content {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 0 30rpx;
  height: 90%;
}

.location-selector {
  display: flex;
  align-items: center;
  width: 200rpx;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.location-text {
  font-size: 28rpx;
  color: #333333;
  margin-right: 8rpx;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  flex: 1;
  margin: 0 40rpx;
}

.navbar-right {
  width: 200rpx;
}

/* 头部区域 */
.one{
width: 750rpx;
height: 370rpx;
margin-top: 20rpx;
}

/* 轮播图样式 */
.carousel-section {
  width: 100%;
  margin: 170rpx 0 20rpx 0;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.carousel-swiper {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.carousel-item {
  width: 100%;
  height: 100%;
}

.carousel-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}
.okbu{
  color: #FFFFFF;
  width: 750rpx;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 25rpx;
}
/* 头部背景图片 */
.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.header-content {
  position: relative;
  z-index: 2;
  padding: 40rpx 30rpx 40rpx;
  width: 750rpx;
  box-sizing: border-box;

}

.logo-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  width: 100%;
}

.logo {
 width: 750rpx;
 height: 50rpx;
 margin-left: -260rpx;
 margin-top: 20rpx;
}



.header-title {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width:750rpx;
}

.main-title {
  color: white;
  font-size: 60rpx;
  font-weight: bold;
  text-align: center;
  width: 100%;
  margin: 0 !important;
  padding: 0 !important;
  display: block;
  box-sizing: border-box;
  position: static !important;
  left: auto !important;
  top: auto !important;
}

.sub-title {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  letter-spacing: 2rpx;
  text-align: center;
  width: 100%;
  margin: 0 !important;
  padding: 0 !important;
  display: block;
  box-sizing: border-box;
  position: static !important;
  left: auto !important;
  top: auto !important;
}

/* 搜索和菜单整体容器 */
.content-container {
  width: 100%;
  margin: 0;
  padding: 0;
}

/* 搜索区域 */
.search-section {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 750rpx;
  position: relative;
  padding: 0;
}

.search-box {
  flex: 1;
  background-color: white;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  padding: 20rpx 80rpx 0rpx -100rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-right: 40rpx;
  margin-left: 40rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}
.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  margin-left: 10rpx;
}

.location-btn {
  width: 80rpx;
  height: 80rpx;
  background-color: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  margin-right: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
  flex-shrink: 0;
}

.location-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 功能菜单网格 */
/* 横向菜单容器 - 与之前的menu-row区分 */

.big-view {
  margin: 0 0 0 0;
  width: 100%;
}

.menu-row-horizontal {
  display: flex;
  flex-direction: row; /* 强制横向排列 */
  width: 100%; /* 占满父容器宽度 */
  padding: 15rpx 0; /* 上下内边距，与其他区域分隔 */
  box-sizing: border-box;
  margin-top: 20rpx; /* 与上方菜单拉开距离 */
}

/* 横向菜单项 - 每个占1/4宽度 */
.menu-item-horizontal {
  flex: 1;
  display: flex;
  flex-direction: column; /* 图标在上，文字在下 */
  align-items: center; /* 水平居中 */
  justify-content: center;
  padding: 10rpx;
  box-sizing: border-box;
}

/* 横向菜单图标容器 */
.menu-icon-horizontal {
  width: 90rpx;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx; /* 图标与文字间距 */
}

/* 横向菜单图标 */
.menu-icon-horizontal image {
  width: 100%;
  height: 100%;
  display: block;
  box-sizing: border-box;
}

/* 横向菜单文字 */
.menu-text-horizontal {
  font-size: 26rpx; /* 可与之前菜单文字大小区分 */
  color: #444; /* 颜色微调区分 */
  text-align: center;
  white-space: nowrap; /* 防止文字换行 */
}

/* 横向菜单激活状态 */
.menu-item-horizontal.active .menu-text-horizontal {
  color: #F8877D;
  font-weight: bold;
}

.menu-item-horizontal.active {
  background-color: rgba(248, 135, 125, 0.1);
  border-radius: 16rpx;
}

.actpo {
  width: 675rpx;
  height: 280rpx;
  box-shadow: inset 0 0 10rpx rgba(0, 0, 0, 0.2);
  border-radius: 32rpx;
  margin: 0 auto;
}

/* 菜单行容器 - 采用flex横向排列 */
.menu-row {
  display: flex;
  width: 100%;
  height: 50%; /* 两行各占一半高度 */
}

/* 菜单项 - 均匀分布 */
.menu-item {
  flex: 1; /* 每个item占1/4宽度 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 图标容器 */
.menu-icon {
  width: 70rpx; /* 统一图标容器宽度 */
  height: 70rpx; /* 统一图标容器高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx; /* 图标与文字间距 */
}

/* 图标样式 */
.menu-icon image {
  width: 100%; /* 图片自适应容器宽度 */
  height: auto; /* 高度自适应 */
  display: block;
}

/* 文字样式 */
.menu-tex {
  font-size: 24rpx; /* 统一文字大小 */
  color: #333; /* 统一文字颜色 */
  height: 37rpx;
  display: block;
  text-align: center;
}

/* 数据统计卡片 */
.stats-section {
  width: 750rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  background-color: #f5f5f5;
}

.card-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  width: 100%;
}

.card-item {
  position: relative;
  border-radius: 16rpx;
  margin-bottom: -10rpx;
}

.card-image {
  width: 100%;
  height: 160rpx;
  display: block;
  box-sizing: border-box;
}

/* 我要买房半屏弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  animation: fadeIn 0.3s ease-out;
}

.modal-container {
  width: 750rpx;
  background-color: white;
  border-radius: 40rpx 40rpx 0 0;
  padding: 0;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease-out;
  max-height: 60vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
  position: relative;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.modal-close {
  position: absolute;
  right: 40rpx;
  top: 40rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
}

.modal-content {
  padding: 60rpx 40rpx 80rpx;
}

.login-options {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 60rpx;
}

.login-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 30rpx;
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  flex: 1;
  min-height: 200rpx;
  justify-content: center;
}

.login-option:active {
  transform: scale(0.95);
  border-color: #FF4444;
  background-color: rgba(255, 68, 68, 0.05);
}

.login-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.option-icon {
  width: 100rpx;
  height: 100rpx;
}

.login-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 图片展示区域 */
.image-gallery {
  width: 750rpx;
  padding: 40rpx 20rpx;
  margin: 0;
  box-sizing: border-box;
}

.image-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  gap: 20rpx;
}

.image-row:last-child {
  margin-bottom: 0;
}

/* 重新定义图片展示区域的图片样式 */
.image-gallery .one {
  width: 355rpx;
  height: 200rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  background-color: white;
  margin: 0;
  position: static;
  top: auto;
  left: auto;
}

.image-gallery .two {
  width: 355rpx;
  height: 200rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  background-color: white;
  margin: 0;
  position: static;
  top: auto;
  left: auto;
}

.image-gallery .thenr {
  width: 355rpx;
  height: 200rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  background-color: white;
  margin: 0;
  position: static;
  top: auto;
  left: auto;
}

.image-gallery .fore {
  width: 355rpx;
  height: 200rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  background-color: white;
  margin: 0;
  position: static;
  top: auto;
  left: auto;
}

/* 房源列表模块样式 */
.house-list-section {
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  margin-top: 20rpx;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #f5f5f5;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 10rpx;
  font-size: 26rpx;
  color: #333333;
  position: relative;
}

.filter-text {
  margin-right: 8rpx;
  max-width: 80rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.filter-arrow {
  width: 20rpx;
  height: 20rpx;
  transition: transform 0.3s ease;
}

/* 房源列表样式 */
.house-list {
  width: 100%;
}

.house-item {
  display: flex;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.house-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.house-image-container {
  position: relative;
  width: 200rpx;
  height: 150rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.house-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.house-status {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background-color: rgba(248, 135, 125, 0.9);
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.house-type {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.house-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.house-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
}

.price-label {
  font-size: 24rpx;
  color: #666666;
}

.price-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF4444;
  margin-left: 8rpx;
}

.unit-price {
  font-size: 20rpx;
  color: #999999;
  margin-left: 8rpx;
}

.house-details {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.detail-item {
  font-size: 24rpx;
  color: #666666;
  margin-right: 20rpx;
}

.house-time {
  display: flex;
  align-items: center;
}

.time-label {
  font-size: 22rpx;
  color: #999999;
}

.time-value {
  font-size: 22rpx;
  color: #666666;
  margin-left: 8rpx;
}

.house-court {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
}

.court-label {
  font-size: 22rpx;
  color: #999999;
}

.court-value {
  font-size: 22rpx;
  color: #666666;
  margin-left: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200rpx;
}

/* 加载更多样式 */
.load-more, .no-more {
  text-align: center;
  padding: 40rpx 0;
  font-size: 26rpx;
  color: #999999;
}

/* 筛选面板样式 */
.filter-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 2000;
  display: flex;
  flex-direction: column;
}

.filter-panel-content {
  background-color: #ffffff;
  margin-top: auto;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 70vh;
  overflow-y: auto;
  padding: 40rpx 30rpx 30rpx;
  animation: slideUp 0.3s ease-out;
}

.filter-options-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.filter-option {
  padding: 20rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666666;
  background-color: #f8f8f8;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  min-width: 120rpx;
  text-align: center;
}

.filter-option.active {
  background-color: #F8877D;
  color: #ffffff;
  border-color: #F8877D;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.custom-price-section {
  margin-bottom: 40rpx;
}

.custom-price-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.custom-price-inputs {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.price-input {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 10rpx;
  font-size: 26rpx;
  text-align: center;
}

.price-separator {
  font-size: 26rpx;
  color: #666666;
}

.price-unit {
  font-size: 26rpx;
  color: #666666;
}

.sort-options {
  margin-bottom: 40rpx;
}

.sort-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-text {
  font-size: 28rpx;
  color: #333333;
}

.sort-option.active .sort-text {
  color: #F8877D;
}

.sort-check {
  width: 32rpx;
  height: 32rpx;
}

.filter-panel-footer {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
}

.filter-reset-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 40rpx;
}

.reset-icon {
  width: 40rpx;
  height: 40rpx;
}

.filter-confirm-btn {
  flex: 1;
  padding: 24rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #ffffff;
  background-color: #F8877D;
  border-radius: 40rpx;
}
