package com.example.cos.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.cos.entity.HouseResource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 房源资源数据访问层
 */
@Mapper
public interface HouseResourceMapper extends BaseMapper<HouseResource> {

    /**
     * 根据拍卖状态查询房源
     */
    @Select("SELECT * FROM house_resource WHERE auction_status = #{auctionStatus}")
    List<HouseResource> findByAuctionStatus(@Param("auctionStatus") Integer auctionStatus);

    /**
     * 根据户型查询房源
     */
    @Select("SELECT * FROM house_resource WHERE house_type = #{houseType}")
    List<HouseResource> findByHouseType(@Param("houseType") String houseType);

    /**
     * 根据小区名称查询房源
     */
    @Select("SELECT * FROM house_resource WHERE community_name LIKE CONCAT('%', #{communityName}, '%')")
    List<HouseResource> findByCommunityName(@Param("communityName") String communityName);

    /**
     * 根据起拍价区间查询房源
     */
    @Select("SELECT * FROM house_resource WHERE starting_price BETWEEN #{minPrice} AND #{maxPrice}")
    List<HouseResource> findByStartingPriceRange(@Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice);

    /**
     * 根据评估价区间查询房源
     */
    @Select("SELECT * FROM house_resource WHERE evaluation_price BETWEEN #{minPrice} AND #{maxPrice}")
    List<HouseResource> findByEvaluationPriceRange(@Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice);

    /**
     * 根据起拍时间范围查询房源
     */
    @Select("SELECT * FROM house_resource WHERE start_time >= #{startTime} AND start_time <= #{endTime}")
    List<HouseResource> findByStartTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据房屋类型查询房源
     */
    @Select("SELECT * FROM house_resource WHERE house_category = #{houseCategory}")
    List<HouseResource> findByHouseCategory(@Param("houseCategory") Integer houseCategory);

    /**
     * 根据是否特殊房屋查询房源
     */
    @Select("SELECT * FROM house_resource WHERE is_special = #{isSpecial}")
    List<HouseResource> findByIsSpecial(@Param("isSpecial") Integer isSpecial);

    /**
     * 根据是否精选查询房源
     */
    @Select("SELECT * FROM house_resource WHERE is_selected = #{isSelected}")
    List<HouseResource> findByIsSelected(@Param("isSelected") Integer isSelected);

    /**
     * 根据装修情况查询房源
     */
    @Select("SELECT * FROM house_resource WHERE decoration = #{decoration}")
    List<HouseResource> findByDecoration(@Param("decoration") Integer decoration);

    /**
     * 根据建筑面积区间查询房源
     */
    @Select("SELECT * FROM house_resource WHERE building_area BETWEEN #{minArea} AND #{maxArea}")
    List<HouseResource> findByBuildingAreaRange(@Param("minArea") BigDecimal minArea, @Param("maxArea") BigDecimal maxArea);

    /**
     * 根据建筑年份区间查询房源
     */
    @Select("SELECT * FROM house_resource WHERE construction_year BETWEEN #{yearFrom} AND #{yearTo}")
    List<HouseResource> findByConstructionYearRange(@Param("yearFrom") Integer yearFrom, @Param("yearTo") Integer yearTo);

    /**
     * 根据标签查询房源（模糊匹配）
     */
    @Select("SELECT * FROM house_resource WHERE tags LIKE CONCAT('%', #{tag}, '%')")
    List<HouseResource> findByTag(@Param("tag") String tag);

    /**
     * 分页查询房源（支持多条件筛选）
     */
    IPage<HouseResource> selectPageWithConditions(
            Page<HouseResource> page,
            @Param("title") String title,
            @Param("auctionStatus") Integer auctionStatus,
            @Param("houseType") String houseType,
            @Param("communityName") String communityName,
            @Param("minStartingPrice") BigDecimal minStartingPrice,
            @Param("maxStartingPrice") BigDecimal maxStartingPrice,
            @Param("minEvaluationPrice") BigDecimal minEvaluationPrice,
            @Param("maxEvaluationPrice") BigDecimal maxEvaluationPrice,
            @Param("startTimeFrom") LocalDateTime startTimeFrom,
            @Param("startTimeTo") LocalDateTime startTimeTo,
            @Param("stairsType") Integer stairsType,
            @Param("propertyType") Integer propertyType,
            @Param("decoration") Integer decoration,
            @Param("houseCategory") Integer houseCategory,
            @Param("isSelected") Integer isSelected,
            @Param("isSpecial") Integer isSpecial,
            @Param("constructionYearFrom") Integer constructionYearFrom,
            @Param("constructionYearTo") Integer constructionYearTo,
            @Param("minBuildingArea") BigDecimal minBuildingArea,
            @Param("maxBuildingArea") BigDecimal maxBuildingArea,
            @Param("tags") String tags
    );

    /**
     * 统计各拍卖状态的房源数量
     */
    @Select("SELECT auction_status, COUNT(*) as count FROM house_resource GROUP BY auction_status")
    List<Object> countByAuctionStatus();

    /**
     * 统计各房屋类型的房源数量
     */
    @Select("SELECT house_category, COUNT(*) as count FROM house_resource GROUP BY house_category")
    List<Object> countByHouseCategory();

    /**
     * 获取价格统计信息
     */
    @Select("SELECT MIN(starting_price) as minPrice, MAX(starting_price) as maxPrice, AVG(starting_price) as avgPrice FROM house_resource")
    Object getPriceStatistics();

    /**
     * 根据标题查询房源数量
     */
    @Select("SELECT COUNT(*) FROM house_resource WHERE title = #{title}")
    int countByTitle(@Param("title") String title);

    /**
     * 根据标题查询房源数量（排除指定ID）
     */
    @Select("SELECT COUNT(*) FROM house_resource WHERE title = #{title} AND id != #{excludeId}")
    int countByTitleExcludeId(@Param("title") String title, @Param("excludeId") Long excludeId);

    /**
     * 统计今日新增房源数量
     */
    @Select("SELECT COUNT(*) FROM house_resource WHERE DATE(create_time) = CURDATE()")
    Long countTodayNew();

    /**
     * 统计正在拍卖的房源数量
     */
    @Select("SELECT COUNT(*) FROM house_resource WHERE start_time <= NOW() AND end_time >= NOW()")
    Long countOngoingAuction();

    /**
     * 统计即将拍卖的房源数量
     */
    @Select("SELECT COUNT(*) FROM house_resource WHERE start_time > NOW()")
    Long countUpcomingAuction();
}