"""
Chrome驱动设置辅助脚本
用于下载和配置Chrome驱动程序
"""

import os
import sys
import requests
import zipfile
import platform
import subprocess
from pathlib import Path


def get_chrome_version():
    """获取本地Chrome浏览器版本"""
    try:
        system = platform.system()
        
        if system == "Windows":
            # Windows系统
            import winreg
            try:
                # 尝试从注册表获取Chrome版本
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
                version, _ = winreg.QueryValueEx(key, "version")
                winreg.CloseKey(key)
                return version
            except:
                # 尝试通过命令行获取
                try:
                    result = subprocess.run([
                        r"C:\Program Files\Google\Chrome\Application\chrome.exe", "--version"
                    ], capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        version = result.stdout.strip().split()[-1]
                        return version
                except:
                    pass
                
                # 尝试另一个路径
                try:
                    result = subprocess.run([
                        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe", "--version"
                    ], capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        version = result.stdout.strip().split()[-1]
                        return version
                except:
                    pass
        
        elif system == "Darwin":  # macOS
            result = subprocess.run([
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "--version"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version = result.stdout.strip().split()[-1]
                return version
        
        elif system == "Linux":
            result = subprocess.run([
                "google-chrome", "--version"
            ], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version = result.stdout.strip().split()[-1]
                return version
        
        return None
        
    except Exception as e:
        print(f"获取Chrome版本失败: {str(e)}")
        return None


def get_chromedriver_download_url(chrome_version):
    """根据Chrome版本获取对应的ChromeDriver下载URL"""
    try:
        # 获取主版本号
        major_version = chrome_version.split('.')[0]
        
        # Chrome 115及以上版本使用新的API
        if int(major_version) >= 115:
            # 使用Chrome for Testing API
            api_url = f"https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
            response = requests.get(api_url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # 查找匹配的版本
            for version_info in data['versions']:
                if version_info['version'].startswith(major_version):
                    downloads = version_info.get('downloads', {})
                    chromedriver_downloads = downloads.get('chromedriver', [])
                    
                    # 根据系统选择对应的下载链接
                    system = platform.system().lower()
                    if system == "windows":
                        platform_name = "win64" if platform.machine().endswith('64') else "win32"
                    elif system == "darwin":
                        platform_name = "mac-x64"
                    else:
                        platform_name = "linux64"
                    
                    for download in chromedriver_downloads:
                        if platform_name in download['platform']:
                            return download['url']
        
        else:
            # Chrome 114及以下版本使用旧的API
            api_url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
            response = requests.get(api_url, timeout=30)
            response.raise_for_status()
            
            driver_version = response.text.strip()
            
            # 根据系统选择对应的下载链接
            system = platform.system().lower()
            if system == "windows":
                filename = "chromedriver_win32.zip"
            elif system == "darwin":
                filename = "chromedriver_mac64.zip"
            else:
                filename = "chromedriver_linux64.zip"
            
            return f"https://chromedriver.storage.googleapis.com/{driver_version}/{filename}"
        
        return None
        
    except Exception as e:
        print(f"获取ChromeDriver下载URL失败: {str(e)}")
        return None


def download_chromedriver(download_url, save_path):
    """下载ChromeDriver"""
    try:
        print(f"正在下载ChromeDriver: {download_url}")
        
        response = requests.get(download_url, timeout=60)
        response.raise_for_status()
        
        # 保存zip文件
        zip_path = save_path + ".zip"
        with open(zip_path, 'wb') as f:
            f.write(response.content)
        
        print(f"下载完成: {zip_path}")
        
        # 解压文件
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(os.path.dirname(save_path))
        
        # 查找解压后的chromedriver文件
        extracted_files = []
        for root, dirs, files in os.walk(os.path.dirname(save_path)):
            for file in files:
                if file.startswith('chromedriver'):
                    extracted_files.append(os.path.join(root, file))
        
        if extracted_files:
            # 移动到目标位置
            chromedriver_file = extracted_files[0]
            if chromedriver_file != save_path:
                os.rename(chromedriver_file, save_path)
            
            # 设置执行权限（Linux/macOS）
            if platform.system() != "Windows":
                os.chmod(save_path, 0o755)
            
            print(f"ChromeDriver已保存到: {save_path}")
            
            # 清理zip文件
            os.remove(zip_path)
            
            return True
        else:
            print("解压后未找到chromedriver文件")
            return False
            
    except Exception as e:
        print(f"下载ChromeDriver失败: {str(e)}")
        return False


def setup_chromedriver():
    """设置ChromeDriver"""
    print("=== Chrome驱动设置工具 ===")
    
    # 检查是否已存在chromedriver
    current_dir = os.getcwd()
    chromedriver_paths = [
        os.path.join(current_dir, "chromedriver.exe"),
        os.path.join(current_dir, "chromedriver"),
        os.path.join(current_dir, "chrome", "chromedriver.exe"),
        os.path.join(current_dir, "chrome", "chromedriver")
    ]
    
    existing_driver = None
    for path in chromedriver_paths:
        if os.path.exists(path):
            existing_driver = path
            break
    
    if existing_driver:
        print(f"✅ 找到现有的ChromeDriver: {existing_driver}")
        
        # 询问是否重新下载
        response = input("是否重新下载最新版本？(y/n): ").lower().strip()
        if response not in ['y', 'yes', '是']:
            print("使用现有的ChromeDriver")
            return True
    
    # 获取Chrome版本
    print("正在检测Chrome浏览器版本...")
    chrome_version = get_chrome_version()
    
    if not chrome_version:
        print("❌ 无法检测到Chrome浏览器版本")
        print("请确保Chrome浏览器已正确安装")
        return False
    
    print(f"✅ 检测到Chrome版本: {chrome_version}")
    
    # 获取下载URL
    print("正在获取ChromeDriver下载链接...")
    download_url = get_chromedriver_download_url(chrome_version)
    
    if not download_url:
        print("❌ 无法获取ChromeDriver下载链接")
        return False
    
    print(f"✅ 获取到下载链接: {download_url}")
    
    # 确定保存路径
    if platform.system() == "Windows":
        save_path = os.path.join(current_dir, "chromedriver.exe")
    else:
        save_path = os.path.join(current_dir, "chromedriver")
    
    # 下载ChromeDriver
    success = download_chromedriver(download_url, save_path)
    
    if success:
        print("✅ ChromeDriver设置完成！")
        print(f"文件位置: {save_path}")
        return True
    else:
        print("❌ ChromeDriver设置失败")
        return False


if __name__ == "__main__":
    try:
        success = setup_chromedriver()
        if success:
            print("\n🎉 设置成功！现在可以运行爬虫了。")
        else:
            print("\n❌ 设置失败，请检查网络连接或手动下载ChromeDriver。")
            print("\n手动下载步骤：")
            print("1. 访问 https://chromedriver.chromium.org/")
            print("2. 下载与您的Chrome版本匹配的ChromeDriver")
            print("3. 将chromedriver.exe放在当前目录下")
    except KeyboardInterrupt:
        print("\n用户取消操作")
    except Exception as e:
        print(f"\n程序执行出错: {str(e)}")
