package com.example.cos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.cos.dto.HouseResourceQueryDTO;
import com.example.cos.dto.HouseResourceStatisticsDTO;
import com.example.cos.entity.HouseResource;
import com.example.cos.mapper.HouseResourceMapper;
import com.example.cos.service.HouseResourceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 房源资源服务实现类
 */
@Service
public class HouseResourceServiceImpl extends ServiceImpl<HouseResourceMapper, HouseResource> implements HouseResourceService {

    private static final Logger logger = LoggerFactory.getLogger(HouseResourceServiceImpl.class);

    @Override
    public IPage<HouseResource> pageQuery(HouseResourceQueryDTO queryDTO) {
        logger.info("分页查询房源: {}", queryDTO);

        Page<HouseResource> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());

        return baseMapper.selectPageWithConditions(
                page,
                queryDTO.getTitle(),
                queryDTO.getAuctionStatus(),
                queryDTO.getHouseType(),
                queryDTO.getCommunityName(),
                queryDTO.getMinStartingPrice(),
                queryDTO.getMaxStartingPrice(),
                queryDTO.getMinEvaluationPrice(),
                queryDTO.getMaxEvaluationPrice(),
                queryDTO.getStartTimeFrom(),
                queryDTO.getStartTimeTo(),
                queryDTO.getStairsType(),
                queryDTO.getPropertyType(),
                queryDTO.getDecoration(),
                queryDTO.getHouseCategory(),
                queryDTO.getIsSelected(),
                queryDTO.getIsSpecial(),
                queryDTO.getConstructionYearFrom(),
                queryDTO.getConstructionYearTo(),
                queryDTO.getMinBuildingArea(),
                queryDTO.getMaxBuildingArea(),
                queryDTO.getTags()
        );
    }

    @Override
    public List<HouseResource> findByAuctionStatus(Integer auctionStatus) {
        return baseMapper.findByAuctionStatus(auctionStatus);
    }

    @Override
    public List<HouseResource> findByHouseType(String houseType) {
        return baseMapper.findByHouseType(houseType);
    }

    @Override
    public List<HouseResource> findByCommunityName(String communityName) {
        return baseMapper.findByCommunityName(communityName);
    }

    @Override
    public List<HouseResource> findByStartingPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        return baseMapper.findByStartingPriceRange(minPrice, maxPrice);
    }

    @Override
    public List<HouseResource> findByEvaluationPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        return baseMapper.findByEvaluationPriceRange(minPrice, maxPrice);
    }

    @Override
    public List<HouseResource> findByStartTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.findByStartTimeRange(startTime, endTime);
    }

    @Override
    public List<HouseResource> findByHouseCategory(Integer houseCategory) {
        return baseMapper.findByHouseCategory(houseCategory);
    }

    @Override
    public List<HouseResource> findByIsSpecial(Integer isSpecial) {
        return baseMapper.findByIsSpecial(isSpecial);
    }

    @Override
    public List<HouseResource> findByIsSelected(Integer isSelected) {
        return baseMapper.findByIsSelected(isSelected);
    }

    @Override
    public List<HouseResource> findByDecoration(Integer decoration) {
        return baseMapper.findByDecoration(decoration);
    }

    @Override
    public List<HouseResource> findByBuildingAreaRange(BigDecimal minArea, BigDecimal maxArea) {
        return baseMapper.findByBuildingAreaRange(minArea, maxArea);
    }

    @Override
    public List<HouseResource> findByConstructionYearRange(Integer yearFrom, Integer yearTo) {
        return baseMapper.findByConstructionYearRange(yearFrom, yearTo);
    }

    @Override
    public List<HouseResource> findByTag(String tag) {
        return baseMapper.findByTag(tag);
    }

    @Override
    @Transactional
    public HouseResource createHouseResource(HouseResource houseResource) {
        logger.info("创建房源: {}", houseResource);

        // 验证房源数据
        if (!validateHouseResource(houseResource)) {
            throw new RuntimeException("房源数据验证失败");
        }

        boolean saved = save(houseResource);
        if (!saved) {
            throw new RuntimeException("房源创建失败");
        }

        logger.info("房源创建成功: {}", houseResource.getId());
        return houseResource;
    }

    @Override
    @Transactional
    public boolean updateHouseResource(HouseResource houseResource) {
        logger.info("更新房源: {}", houseResource);

        // 验证房源数据
        if (!validateHouseResource(houseResource)) {
            throw new RuntimeException("房源数据验证失败");
        }

        boolean updated = updateById(houseResource);
        if (updated) {
            logger.info("房源更新成功: {}", houseResource.getId());
        } else {
            logger.error("房源更新失败: {}", houseResource.getId());
        }

        return updated;
    }

    @Override
    @Transactional
    public boolean deleteHouseResource(Long id) {
        logger.info("删除房源: {}", id);

        boolean deleted = removeById(id);
        if (deleted) {
            logger.info("房源删除成功: {}", id);
        } else {
            logger.error("房源删除失败: {}", id);
        }

        return deleted;
    }

    @Override
    @Transactional
    public boolean batchDeleteHouseResources(List<Long> ids) {
        logger.info("批量删除房源: {}", ids);

        boolean deleted = removeByIds(ids);
        if (deleted) {
            logger.info("批量删除房源成功，数量: {}", ids.size());
        } else {
            logger.error("批量删除房源失败");
        }

        return deleted;
    }

    @Override
    public List<HouseResource> findByIds(List<Long> ids) {
        return listByIds(ids);
    }

    @Override
    @Transactional
    public boolean batchInsertHouseResources(List<HouseResource> houseResources) {
        logger.info("批量插入房源，数量: {}", houseResources.size());

        // 验证每个房源数据
        for (HouseResource houseResource : houseResources) {
            if (!validateHouseResource(houseResource)) {
                throw new RuntimeException("房源数据验证失败: " + houseResource.getTitle());
            }
        }

        boolean inserted = saveBatch(houseResources);
        if (inserted) {
            logger.info("批量插入房源成功，数量: {}", houseResources.size());
        } else {
            logger.error("批量插入房源失败");
        }

        return inserted;
    }

    @Override
    @Transactional
    public boolean batchUpdateStatus(List<Long> ids, Integer status) {
        logger.info("批量更新房源状态: ids={}, status={}", ids, status);

        Map<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        params.put("status", status);

        // 这里需要在Mapper中添加对应的方法
        // return baseMapper.batchUpdateStatus(params) > 0;

        // 临时实现：逐个更新
        for (Long id : ids) {
            HouseResource houseResource = new HouseResource();
            houseResource.setId(id);
            houseResource.setAuctionStatus(status);
            updateById(houseResource);
        }

        return true;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> countByAuctionStatus() {
        List<Object> results = baseMapper.countByAuctionStatus();
        // 转换为Map格式
        return (List<Map<String, Object>>) (List<?>) results;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> countByHouseCategory() {
        List<Object> results = baseMapper.countByHouseCategory();
        // 转换为Map格式
        return (List<Map<String, Object>>) (List<?>) results;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> getPriceStatistics() {
        Object result = baseMapper.getPriceStatistics();
        // 转换为Map格式
        return (Map<String, Object>) result;
    }

    @Override
    @Transactional
    public boolean deleteByConditions(Map<String, Object> conditions) {
        logger.info("根据条件删除房源: {}", conditions);

        // 这里需要在Mapper中添加对应的方法
        // return baseMapper.deleteByConditions(conditions) > 0;

        // 临时实现：根据条件构建QueryWrapper
        QueryWrapper<HouseResource> queryWrapper = new QueryWrapper<>();

        if (conditions.containsKey("auctionStatus")) {
            queryWrapper.eq("auction_status", conditions.get("auctionStatus"));
        }
        if (conditions.containsKey("endTimeBefore")) {
            queryWrapper.lt("end_time", conditions.get("endTimeBefore"));
        }
        if (conditions.containsKey("isSpecial")) {
            queryWrapper.eq("is_special", conditions.get("isSpecial"));
        }

        return remove(queryWrapper);
    }

    @Override
    public List<HouseResource> getRecommendedHouses(int limit) {
        QueryWrapper<HouseResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_selected", 1)
                   .orderByDesc("id")
                   .last("LIMIT " + limit);

        return list(queryWrapper);
    }

    @Override
    public List<HouseResource> getUpcomingAuctions(int limit) {
        QueryWrapper<HouseResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("auction_status", 0) // 未开拍
                   .gt("start_time", LocalDateTime.now())
                   .orderByAsc("start_time")
                   .last("LIMIT " + limit);

        return list(queryWrapper);
    }

    @Override
    public List<HouseResource> getPopularHouses(int limit) {
        // 这里可以根据实际的热门指标来实现，暂时按照精选和起拍价排序
        QueryWrapper<HouseResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_selected", 1)
                   .orderByDesc("starting_price")
                   .last("LIMIT " + limit);

        return list(queryWrapper);
    }

    @Override
    public List<HouseResource> searchHouses(String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return new ArrayList<>();
        }

        QueryWrapper<HouseResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("title", keyword)
                   .or()
                   .like("community_name", keyword)
                   .or()
                   .like("tags", keyword);

        return list(queryWrapper);
    }

    @Override
    public HouseResource getHouseResourceDetail(Long id) {
        HouseResource houseResource = getById(id);
        if (houseResource != null) {
            // 这里可以添加额外的计算逻辑，比如计算折扣率等
            // 由于数据库中已经有计算字段，这里直接返回即可
        }
        return houseResource;
    }

    @Override
    public boolean validateHouseResource(HouseResource houseResource) {
        if (houseResource == null) {
            logger.error("房源对象为空");
            return false;
        }

        if (!StringUtils.hasText(houseResource.getTitle())) {
            logger.error("房源标题不能为空");
            return false;
        }

        // 检查标题唯一性
        if (houseResource.getId() == null) {
            // 新增操作：检查标题是否重复
            if (isTitleDuplicate(houseResource.getTitle())) {
                logger.error("房源标题已存在: {}", houseResource.getTitle());
                return false;
            }
        } else {
            // 更新操作：检查标题是否重复（排除当前记录）
            if (isTitleDuplicate(houseResource.getTitle(), houseResource.getId())) {
                logger.error("房源标题已存在: {}", houseResource.getTitle());
                return false;
            }
        }

        if (houseResource.getStartingPrice() == null || houseResource.getStartingPrice().compareTo(BigDecimal.ZERO) <= 0) {
            logger.error("起拍价必须大于0");
            return false;
        }

        if (houseResource.getEvaluationPrice() == null || houseResource.getEvaluationPrice().compareTo(BigDecimal.ZERO) <= 0) {
            logger.error("评估价必须大于0");
            return false;
        }

        if (houseResource.getStartTime() == null) {
            logger.error("起拍时间不能为空");
            return false;
        }

        if (houseResource.getEndTime() == null) {
            logger.error("结束时间不能为空");
            return false;
        }

        if (houseResource.getStartTime().isAfter(houseResource.getEndTime())) {
            logger.error("起拍时间不能晚于结束时间");
            return false;
        }

        if (houseResource.getBuildingArea() == null || houseResource.getBuildingArea().compareTo(BigDecimal.ZERO) <= 0) {
            logger.error("建筑面积必须大于0");
            return false;
        }

        return true;
    }

    @Override
    public boolean isTitleDuplicate(String title) {
        if (!StringUtils.hasText(title)) {
            logger.warn("标题为空，无法检查重复性");
            return false;
        }

        int count = baseMapper.countByTitle(title.trim());
        boolean isDuplicate = count > 0;

        logger.debug("检查标题重复性: title={}, count={}, isDuplicate={}", title, count, isDuplicate);
        return isDuplicate;
    }

    @Override
    public boolean isTitleDuplicate(String title, Long excludeId) {
        if (!StringUtils.hasText(title)) {
            logger.warn("标题为空，无法检查重复性");
            return false;
        }

        if (excludeId == null) {
            logger.warn("排除ID为空，使用普通重复性检查");
            return isTitleDuplicate(title);
        }

        int count = baseMapper.countByTitleExcludeId(title.trim(), excludeId);
        boolean isDuplicate = count > 0;

        logger.debug("检查标题重复性（排除ID）: title={}, excludeId={}, count={}, isDuplicate={}",
                    title, excludeId, count, isDuplicate);
        return isDuplicate;
    }

    @Override
    public HouseResourceStatisticsDTO getHouseResourceStatistics() {
        logger.info("获取房源统计数据");

        try {
            // 统计今日新增数量
            Long todayNewCount = baseMapper.countTodayNew();
            logger.debug("今日新增房源数量: {}", todayNewCount);

            // 统计正在拍卖数量
            Long ongoingAuctionCount = baseMapper.countOngoingAuction();
            logger.debug("正在拍卖房源数量: {}", ongoingAuctionCount);

            // 统计即将拍卖数量
            Long upcomingAuctionCount = baseMapper.countUpcomingAuction();
            logger.debug("即将拍卖房源数量: {}", upcomingAuctionCount);

            HouseResourceStatisticsDTO statistics = new HouseResourceStatisticsDTO(
                    todayNewCount != null ? todayNewCount : 0L,
                    ongoingAuctionCount != null ? ongoingAuctionCount : 0L,
                    upcomingAuctionCount != null ? upcomingAuctionCount : 0L
            );

            logger.info("房源统计数据获取成功: {}", statistics);
            return statistics;

        } catch (Exception e) {
            logger.error("获取房源统计数据失败: {}", e.getMessage(), e);
            // 返回默认值，避免前端报错
            return new HouseResourceStatisticsDTO(0L, 0L, 0L);
        }
    }
}