package com.example.cos.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.cos.dto.CarouselCreateDTO;
import com.example.cos.dto.CarouselUpdateDTO;
import com.example.cos.entity.Carousel;
import com.example.cos.mapper.CarouselMapper;
import com.example.cos.service.CarouselService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 轮播图服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class CarouselServiceImpl extends ServiceImpl<CarouselMapper, Carousel> implements CarouselService {

    private static final Logger logger = LoggerFactory.getLogger(CarouselServiceImpl.class);

    @Override
    @Transactional
    public Carousel createCarousel(CarouselCreateDTO createDTO) {
        logger.info("创建轮播图: {}", createDTO);

        // 验证数据
        validateCarouselData(createDTO.getImageUrl(), createDTO.getSortOrder());

        // 创建轮播图实体
        Carousel carousel = new Carousel();
        BeanUtils.copyProperties(createDTO, carousel);
        carousel.setCreateTime(LocalDateTime.now());
        carousel.setUpdateTime(LocalDateTime.now());

        // 处理空字符串
        if (!StringUtils.hasText(carousel.getJumpUrl())) {
            carousel.setJumpUrl("");
        }
        if (!StringUtils.hasText(carousel.getRemark())) {
            carousel.setRemark("");
        }

        // 保存到数据库
        boolean saved = save(carousel);
        if (!saved) {
            throw new RuntimeException("创建轮播图失败");
        }

        logger.info("轮播图创建成功: {}", carousel.getId());
        return carousel;
    }

    @Override
    @Transactional
    public Carousel updateCarousel(CarouselUpdateDTO updateDTO) {
        logger.info("更新轮播图: {}", updateDTO);

        // 验证轮播图是否存在
        Carousel existingCarousel = getById(updateDTO.getId());
        if (existingCarousel == null) {
            throw new RuntimeException("轮播图不存在: " + updateDTO.getId());
        }

        // 验证数据
        validateCarouselData(updateDTO.getImageUrl(), updateDTO.getSortOrder());

        // 更新轮播图数据
        BeanUtils.copyProperties(updateDTO, existingCarousel);
        existingCarousel.setUpdateTime(LocalDateTime.now());

        // 处理空字符串
        if (!StringUtils.hasText(existingCarousel.getJumpUrl())) {
            existingCarousel.setJumpUrl("");
        }
        if (!StringUtils.hasText(existingCarousel.getRemark())) {
            existingCarousel.setRemark("");
        }

        // 保存到数据库
        boolean updated = updateById(existingCarousel);
        if (!updated) {
            throw new RuntimeException("更新轮播图失败");
        }

        logger.info("轮播图更新成功: {}", existingCarousel.getId());
        return existingCarousel;
    }

    @Override
    @Transactional
    public boolean deleteCarousel(Long id) {
        logger.info("删除轮播图: {}", id);

        // 验证轮播图是否存在
        Carousel carousel = getById(id);
        if (carousel == null) {
            throw new RuntimeException("轮播图不存在: " + id);
        }

        boolean deleted = removeById(id);
        if (deleted) {
            logger.info("轮播图删除成功: {}", id);
        }

        return deleted;
    }

    @Override
    @Transactional
    public boolean batchDeleteCarousels(List<Long> ids) {
        logger.info("批量删除轮播图: {}", ids);

        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("轮播图ID列表不能为空");
        }

        boolean deleted = removeByIds(ids);
        if (deleted) {
            logger.info("批量删除轮播图成功，数量: {}", ids.size());
        }

        return deleted;
    }

    @Override
    public List<Carousel> getEnabledCarousels() {
        return baseMapper.selectEnabledCarousels();
    }

    @Override
    public List<Carousel> getAllCarouselsOrderBySort() {
        return baseMapper.selectAllCarouselsOrderBySort();
    }

    @Override
    @Transactional
    public Carousel toggleCarouselStatus(Long id) {
        logger.info("切换轮播图状态: {}", id);

        // 验证轮播图是否存在
        Carousel carousel = getById(id);
        if (carousel == null) {
            throw new RuntimeException("轮播图不存在: " + id);
        }

        // 切换状态
        carousel.setIsEnabled(carousel.getIsEnabled() == 1 ? 0 : 1);
        carousel.setUpdateTime(LocalDateTime.now());

        // 保存到数据库
        boolean updated = updateById(carousel);
        if (!updated) {
            throw new RuntimeException("切换轮播图状态失败");
        }

        logger.info("轮播图状态切换成功: {} -> {}", id, carousel.getIsEnabled());
        return carousel;
    }

    @Override
    @Transactional
    public boolean batchUpdateSort(List<Carousel> carousels) {
        logger.info("批量更新轮播图排序，数量: {}", carousels.size());

        if (CollectionUtils.isEmpty(carousels)) {
            return true;
        }

        // 逐个更新排序
        for (Carousel carousel : carousels) {
            if (carousel.getId() != null && carousel.getSortOrder() != null) {
                Carousel existingCarousel = getById(carousel.getId());
                if (existingCarousel != null) {
                    existingCarousel.setSortOrder(carousel.getSortOrder());
                    existingCarousel.setUpdateTime(LocalDateTime.now());
                    updateById(existingCarousel);
                }
            }
        }

        logger.info("批量更新轮播图排序成功");
        return true;
    }

    /**
     * 验证轮播图数据
     */
    private void validateCarouselData(String imageUrl, Integer sortOrder) {
        if (!StringUtils.hasText(imageUrl)) {
            throw new IllegalArgumentException("图片链接不能为空");
        }

        if (sortOrder == null || sortOrder < 0) {
            throw new IllegalArgumentException("排序序号不能为空且必须大于等于0");
        }

        // 验证图片URL格式（允许相对路径，因为可能是上传到本地的文件）
        if (!imageUrl.startsWith("http://") && !imageUrl.startsWith("https://") && !imageUrl.startsWith("/")) {
            throw new IllegalArgumentException("图片链接格式不正确");
        }
    }
}
