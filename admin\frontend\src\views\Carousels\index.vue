<template>
  <div class="app-container">
    <div class="page-header">
      <h2>轮播图管理</h2>
      <Permission permission="create">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增轮播图
        </el-button>
      </Permission>
    </div>

    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :model="searchForm" inline>
        <el-form-item label="启用状态">
          <el-select v-model="searchForm.isEnabled" placeholder="请选择状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadCarousels">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <Permission permission="delete">
          <el-button type="danger" @click="handleBatchDelete" :disabled="!multipleSelection.length">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </Permission>
      </div>
      <div class="toolbar-right">
        <el-button @click="loadCarousels">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 轮播图列表 -->
    <el-table
      :data="carousels"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      row-key="id"
      class="carousel-table"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column prop="id" label="ID" width="80" />
      
      <el-table-column label="轮播图" width="150">
        <template #default="scope">
          <div class="image-preview">
            <el-image
              :src="scope.row.imageUrl"
              :preview-src-list="[scope.row.imageUrl]"
              fit="cover"
              style="width: 120px; height: 60px; border-radius: 4px;"
              :preview-teleported="true"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <span>加载失败</span>
                </div>
              </template>
            </el-image>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="imageUrl" label="图片链接" min-width="200" show-overflow-tooltip />

      <el-table-column prop="jumpUrl" label="跳转链接" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.jumpUrl">{{ scope.row.jumpUrl }}</span>
          <span v-else class="text-muted">无跳转</span>
        </template>
      </el-table-column>

      <el-table-column prop="isEnabled" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.isEnabled === 1 ? 'success' : 'danger'">
            {{ scope.row.isEnabled === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="sortOrder" label="排序" width="100" />

      <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
          <span v-else class="text-muted">无备注</span>
        </template>
      </el-table-column>

      <el-table-column prop="createTime" label="创建时间" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" width="320">
        <template #default="scope">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <Permission permission="update">
              <el-button type="success" size="small" @click="handleUpdate(scope.row)">
                编辑
              </el-button>
            </Permission>
            <Permission permission="update">
              <el-button
                :type="scope.row.isEnabled === 1 ? 'warning' : 'success'"
                size="small"
                @click="handleToggleStatus(scope.row)"
              >
                {{ scope.row.isEnabled === 1 ? '禁用' : '启用' }}
              </el-button>
            </Permission>
            <Permission permission="delete">
              <el-button type="danger" size="small" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </Permission>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 轮播图表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="carouselForm"
        :rules="carouselRules"
        ref="carouselFormRef"
        label-width="100px"
      >
        <el-form-item label="轮播图片" prop="imageUrl">
          <div class="upload-container">
            <el-upload
              v-if="dialogMode !== 'view'"
              class="image-uploader"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeUpload"
              :disabled="uploading"
              accept="image/*"
            >
              <div v-if="carouselForm.imageUrl" class="uploaded-image">
                <el-image
                  :src="carouselForm.imageUrl"
                  fit="cover"
                  style="width: 200px; height: 100px; border-radius: 4px;"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                      <span>加载失败</span>
                    </div>
                  </template>
                </el-image>
                <div class="upload-overlay">
                  <el-icon><Plus /></el-icon>
                  <span>重新上传</span>
                </div>
              </div>
              <div v-else class="upload-placeholder">
                <el-icon v-if="!uploading"><Plus /></el-icon>
                <el-icon v-else class="is-loading"><Loading /></el-icon>
                <div class="upload-text">
                  {{ uploading ? '上传中...' : '点击上传图片' }}
                </div>
              </div>
            </el-upload>

            <!-- 查看模式下的图片显示 -->
            <div v-if="dialogMode === 'view' && carouselForm.imageUrl" class="view-image">
              <el-image
                :src="carouselForm.imageUrl"
                fit="cover"
                style="width: 200px; height: 100px; border-radius: 4px;"
                :preview-src-list="[carouselForm.imageUrl]"
                :preview-teleported="true"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                    <span>加载失败</span>
                  </div>
                </template>
              </el-image>
            </div>

            <!-- 手动输入链接选项 -->
            <div class="manual-input" v-if="dialogMode !== 'view'">
              <el-button
                type="text"
                size="small"
                @click="showManualInput = !showManualInput"
                style="margin-top: 8px;"
              >
                {{ showManualInput ? '隐藏' : '手动输入链接' }}
              </el-button>
              <el-input
                v-if="showManualInput"
                v-model="carouselForm.imageUrl"
                placeholder="请输入图片链接"
                style="margin-top: 8px;"
              />
            </div>
          </div>
          <div class="form-tip">支持JPG、PNG、GIF格式，建议尺寸：750x375px，文件大小不超过2MB</div>
        </el-form-item>



        <el-form-item label="跳转链接" prop="jumpUrl">
          <el-input
            v-model="carouselForm.jumpUrl"
            placeholder="请输入跳转链接（可选）"
            :disabled="dialogMode === 'view'"
          />
          <div class="form-tip">点击轮播图后跳转的链接，留空则无跳转</div>
        </el-form-item>

        <el-form-item label="启用状态" prop="isEnabled">
          <el-radio-group v-model="carouselForm.isEnabled" :disabled="dialogMode === 'view'">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="排序序号" prop="sortOrder">
          <el-input-number
            v-model="carouselForm.sortOrder"
            :min="0"
            :max="9999"
            placeholder="排序序号"
            :disabled="dialogMode === 'view'"
          />
          <div class="form-tip">数字越小越靠前显示</div>
        </el-form-item>

        <el-form-item label="备注信息" prop="remark">
          <el-input
            v-model="carouselForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
            :disabled="dialogMode === 'view'"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button 
            v-if="dialogMode !== 'view'" 
            type="primary" 
            @click="handleSubmit"
            :loading="submitting"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCarousels, createCarousel, updateCarousel, deleteCarousel, batchDeleteCarousels, toggleCarouselStatus } from '@/api/carousel'
import { getToken } from '@/utils/auth'

// 响应式数据
const loading = ref(false)
const carousels = ref([])
const multipleSelection = ref([])

// 搜索表单
const searchForm = reactive({
  isEnabled: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogMode = ref('add') // add, edit, view
const submitting = ref(false)

// 文件上传相关
const uploading = ref(false)
const showManualInput = ref(false)

// 轮播图表单
const carouselForm = reactive({
  id: null,
  imageUrl: '',
  jumpUrl: '',
  isEnabled: 1,
  sortOrder: 0,
  remark: ''
})

const carouselFormRef = ref()

// 上传相关计算属性
const uploadUrl = computed(() => {
  // 使用import.meta.env替代process.env，兼容Vite构建
  const baseAPI = import.meta.env.VITE_APP_BASE_API || '/api'
  return baseAPI + '/api/cos/upload'
})

const uploadHeaders = computed(() => {
  return {
    'Authorization': 'Bearer ' + getToken()
  }
})

// 表单验证规则
const carouselRules = {
  imageUrl: [
    { required: true, message: '请输入图片链接', trigger: 'blur' }
  ],
  isEnabled: [
    { required: true, message: '请选择启用状态', trigger: 'change' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序序号', trigger: 'blur' },
    { type: 'number', min: 0, message: '排序序号不能小于0', trigger: 'blur' }
  ]
}

// 方法
const loadCarousels = async () => {
  loading.value = true
  try {
    const response = await getCarousels()
    if (response.code === 200) {
      let data = response.data || []
      
      // 根据搜索条件过滤
      if (searchForm.isEnabled !== '') {
        data = data.filter(item => item.isEnabled === searchForm.isEnabled)
      }
      
      carousels.value = data
    } else {
      ElMessage.error(response.message || '获取轮播图列表失败')
    }
  } catch (error) {
    console.error('获取轮播图列表失败:', error)
    ElMessage.error('获取轮播图列表失败')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  searchForm.isEnabled = ''
  loadCarousels()
}

const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

const handleAdd = () => {
  dialogTitle.value = '新增轮播图'
  dialogMode.value = 'add'
  resetCarouselForm()
  dialogVisible.value = true
}

const handleView = (row) => {
  dialogTitle.value = '查看轮播图'
  dialogMode.value = 'view'
  fillCarouselForm(row)
  dialogVisible.value = true
}

const handleUpdate = (row) => {
  dialogTitle.value = '编辑轮播图'
  dialogMode.value = 'edit'
  fillCarouselForm(row)
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除轮播图"${row.remark || row.id}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteCarousel(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      loadCarousels()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除轮播图失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleBatchDelete = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请选择要删除的轮播图')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${multipleSelection.value.length} 个轮播图吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = multipleSelection.value.map(item => item.id)
    const response = await batchDeleteCarousels(ids)
    
    if (response.code === 200) {
      ElMessage.success('批量删除成功')
      loadCarousels()
    } else {
      ElMessage.error(response.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除轮播图失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const handleToggleStatus = async (row) => {
  try {
    const action = row.isEnabled === 1 ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}轮播图"${row.remark || row.id}"吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await toggleCarouselStatus(row.id)
    if (response.code === 200) {
      ElMessage.success(`${action}成功`)
      loadCarousels()
    } else {
      ElMessage.error(response.message || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const handleSubmit = async () => {
  if (!carouselFormRef.value) return

  try {
    await carouselFormRef.value.validate()
  } catch (error) {
    return
  }

  submitting.value = true
  try {
    let response
    if (dialogMode.value === 'add') {
      response = await createCarousel(carouselForm)
    } else {
      response = await updateCarousel(carouselForm)
    }

    if (response.code === 200) {
      ElMessage.success(dialogMode.value === 'add' ? '创建成功' : '更新成功')
      dialogVisible.value = false
      loadCarousels()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

const resetCarouselForm = () => {
  Object.assign(carouselForm, {
    id: null,
    imageUrl: '',
    jumpUrl: '',
    isEnabled: 1,
    sortOrder: 0,
    remark: ''
  })

  // 重置上传相关状态
  uploading.value = false
  showManualInput.value = false

  if (carouselFormRef.value) {
    carouselFormRef.value.clearValidate()
  }
}

// 文件上传相关方法
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }

  uploading.value = true
  return true
}

const handleUploadSuccess = (response) => {
  uploading.value = false

  if (response.code === 200) {
    carouselForm.imageUrl = response.data.fileUrl
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

const handleUploadError = (error) => {
  uploading.value = false
  console.error('图片上传失败:', error)
  ElMessage.error('图片上传失败，请重试')
}

const fillCarouselForm = (row) => {
  Object.assign(carouselForm, {
    id: row.id,
    imageUrl: row.imageUrl || '',
    jumpUrl: row.jumpUrl || '',
    isEnabled: row.isEnabled,
    sortOrder: row.sortOrder,
    remark: row.remark || ''
  })
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadCarousels()
})
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    color: #303133;
  }
}

.search-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.carousel-table {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-preview {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 60px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #909399;
  font-size: 12px;
  
  .el-icon {
    font-size: 20px;
    margin-bottom: 4px;
  }
}



.text-muted {
  color: #909399;
  font-style: italic;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}

// 文件上传样式
.upload-container {
  .image-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .uploaded-image {
    position: relative;
    display: inline-block;

    .upload-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      color: white;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s;
      border-radius: 4px;

      .el-icon {
        font-size: 20px;
        margin-bottom: 4px;
      }

      span {
        font-size: 12px;
      }
    }

    &:hover .upload-overlay {
      opacity: 1;
    }
  }

  .upload-placeholder {
    width: 200px;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    .el-icon {
      font-size: 28px;
      color: #8c939d;
      margin-bottom: 8px;

      &.is-loading {
        animation: rotating 2s linear infinite;
      }
    }

    .upload-text {
      font-size: 14px;
      color: #8c939d;
    }
  }

  .view-image {
    display: inline-block;
  }

  .manual-input {
    margin-top: 8px;
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;

  .el-button {
    margin: 0;
    flex-shrink: 0;
  }

  @media (max-width: 1200px) {
    gap: 4px;

    .el-button {
      font-size: 12px;
      padding: 4px 8px;
    }
  }
}
</style>
