package com.example.cos.service;

import com.example.cos.dto.WechatLoginDTO;
import com.example.cos.dto.WechatLoginResultDTO;
import com.example.cos.entity.WechatAccessToken;
import com.example.cos.entity.WechatSessionInfo;

/**
 * 微信服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface WechatService {

    /**
     * 微信小程序登录
     * 
     * @param loginDTO 登录请求参数
     * @return 登录结果
     */
    WechatLoginResultDTO miniappLogin(WechatLoginDTO loginDTO);

    /**
     * 通过code获取微信用户session信息
     * 
     * @param code 微信授权码
     * @return 会话信息
     */
    WechatSessionInfo getSessionInfo(String code);

    /**
     * 获取微信访问令牌
     * 
     * @return 访问令牌
     */
    WechatAccessToken getAccessToken();

    /**
     * 获取用户手机号
     * 
     * @param code 手机号授权码
     * @return 手机号信息
     */
    String getPhoneNumber(String code);

    /**
     * 刷新访问令牌
     * 
     * @param refreshToken 刷新令牌
     * @return 新的登录结果
     */
    WechatLoginResultDTO refreshToken(String refreshToken);

    /**
     * 验证访问令牌
     * 
     * @param accessToken 访问令牌
     * @return 是否有效
     */
    boolean validateToken(String accessToken);

    /**
     * 用户登出
     * 
     * @param accessToken 访问令牌
     * @return 是否成功
     */
    boolean logout(String accessToken);
}
