# -*- coding: utf-8 -*-
"""
完善的防检测机制模块
包含环境特征模拟、行为模拟、随机化策略、异常处理等
"""

import time
import random
import json
import os
import requests
import tempfile
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class AntiDetectionManager:
    """防检测管理器"""
    
    def __init__(self, driver: webdriver.Chrome):
        self.driver = driver
        self.action_chains = ActionChains(driver)
        self.request_count = 0
        self.last_request_time = time.time()
        self.session_start_time = time.time()
        
        # 加载防检测脚本
        self.load_anti_detection_scripts()
    
    def load_anti_detection_scripts(self):
        """加载防检测JavaScript脚本"""
        try:
            # 加载现有的防检测脚本
            script_path = os.path.join(os.getcwd(), "bypass_webdriver_detection.js")
            if os.path.exists(script_path):
                with open(script_path, 'r', encoding='utf-8') as f:
                    script_content = f.read()
                self.driver.execute_script(script_content)
                print("✅ 防检测脚本加载成功")
            
            # 执行额外的防检测脚本
            self.execute_advanced_anti_detection()
            
        except Exception as e:
            print(f"⚠️ 防检测脚本加载失败: {str(e)}")
    
    def execute_advanced_anti_detection(self):
        """执行高级防检测脚本"""
        advanced_script = """
        // 高级防检测脚本
        
        // 1. 修复webdriver属性检测
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
            configurable: true
        });
        
        // 2. 隐藏自动化扩展
        Object.defineProperty(navigator, 'plugins', {
            get: () => {
                return Object.setPrototypeOf([
                    {
                        0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format", enabledPlugin: Plugin},
                        description: "Portable Document Format",
                        filename: "internal-pdf-viewer",
                        length: 1,
                        name: "Chrome PDF Plugin"
                    },
                    {
                        0: {type: "application/pdf", suffixes: "pdf", description: "", enabledPlugin: Plugin},
                        description: "",
                        filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                        length: 1,
                        name: "Chrome PDF Viewer"
                    },
                    {
                        0: {type: "application/x-nacl", suffixes: "", description: "Native Client Executable", enabledPlugin: Plugin},
                        1: {type: "application/x-pnacl", suffixes: "", description: "Portable Native Client Executable", enabledPlugin: Plugin},
                        description: "",
                        filename: "internal-nacl-plugin",
                        length: 2,
                        name: "Native Client"
                    }
                ], PluginArray.prototype);
            }
        });
        
        // 3. 修复语言检测
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en-US', 'en']
        });
        
        // 4. 修复权限API
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
        
        // 5. 修复Chrome运行时
        if (!window.chrome || !window.chrome.runtime) {
            window.chrome = {
                runtime: {
                    onConnect: undefined,
                    onMessage: undefined,
                    sendMessage: undefined,
                    connect: undefined
                },
                app: {
                    isInstalled: false
                }
            };
        }
        
        // 6. 修复iframe检测
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
            const element = originalCreateElement.call(this, tagName);
            if (tagName === 'iframe') {
                try {
                    Object.defineProperty(element.contentWindow.navigator, 'webdriver', {
                        get: () => undefined
                    });
                } catch (e) {}
            }
            return element;
        };
        
        // 7. 修复toString检测
        const originalToString = Function.prototype.toString;
        Function.prototype.toString = function() {
            if (this === navigator.webdriver) {
                return 'function webdriver() { [native code] }';
            }
            return originalToString.call(this);
        };
        
        // 8. 隐藏Selenium变量
        ['selenium', '_selenium', '__selenium_unwrapped', '__selenium_evaluate', 
         '__webdriver_evaluate', '__driver_evaluate', '__webdriver_unwrapped', 
         '__driver_unwrapped', '_Selenium_IDE_Recorder'].forEach(prop => {
            delete window[prop];
        });
        
        // 9. 修复canvas指纹
        const toBlob = HTMLCanvasElement.prototype.toBlob;
        const toDataURL = HTMLCanvasElement.prototype.toDataURL;
        
        HTMLCanvasElement.prototype.toBlob = function(callback, type, quality) {
            const canvas = this;
            const ctx = canvas.getContext('2d');
            if (ctx) {
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                for (let i = 0; i < imageData.data.length; i += 4) {
                    if (Math.random() < 0.001) {
                        imageData.data[i] = Math.floor(Math.random() * 256);
                        imageData.data[i + 1] = Math.floor(Math.random() * 256);
                        imageData.data[i + 2] = Math.floor(Math.random() * 256);
                    }
                }
                ctx.putImageData(imageData, 0, 0);
            }
            return toBlob.apply(this, arguments);
        };
        
        // 10. 修复WebGL指纹
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            if (parameter === 37445) return 'Intel Inc.';
            if (parameter === 37446) return 'Intel Iris OpenGL Engine';
            return getParameter.call(this, parameter);
        };
        
        console.log('高级防检测脚本已加载');
        """
        
        try:
            self.driver.execute_script(advanced_script)
            print("✅ 高级防检测脚本执行成功")
        except Exception as e:
            print(f"⚠️ 高级防检测脚本执行失败: {str(e)}")
    
    def simulate_human_behavior(self):
        """模拟人类行为"""
        try:
            # 随机鼠标移动
            if random.random() < 0.3:
                self.random_mouse_movement()
            
            # 随机滚动
            if random.random() < 0.4:
                self.simulate_reading_behavior()
            
            # 随机停顿
            if random.random() < 0.2:
                self.simulate_typing_pause()
                
        except Exception as e:
            print(f"⚠️ 人类行为模拟失败: {str(e)}")
    
    def random_mouse_movement(self):
        """随机鼠标移动"""
        try:
            # 获取页面尺寸
            window_size = self.driver.get_window_size()
            width = window_size['width']
            height = window_size['height']
            
            # 生成3-6个随机移动点
            move_count = random.randint(3, 6)
            
            for _ in range(move_count):
                x = random.randint(100, width - 100)
                y = random.randint(100, height - 100)
                
                # 使用ActionChains进行平滑移动
                self.action_chains.move_by_offset(
                    x - width//2, y - height//2
                ).perform()
                
                # 短暂停顿
                time.sleep(random.uniform(0.1, 0.3))
                
        except Exception as e:
            print(f"⚠️ 鼠标移动模拟失败: {str(e)}")
    
    def simulate_reading_behavior(self):
        """模拟阅读行为（滚动）"""
        try:
            # 随机滚动方向和距离
            scroll_directions = ['up', 'down']
            direction = random.choice(scroll_directions)
            
            # 模拟分段滚动（像人类阅读一样）
            scroll_count = random.randint(2, 5)
            
            for _ in range(scroll_count):
                if direction == 'down':
                    scroll_distance = random.randint(100, 500)
                    self.driver.execute_script(f"window.scrollBy(0, {scroll_distance});")
                else:
                    scroll_distance = random.randint(100, 300)
                    self.driver.execute_script(f"window.scrollBy(0, -{scroll_distance});")
                
                # 阅读停顿
                time.sleep(random.uniform(0.5, 2.0))
                
        except Exception as e:
            print(f"⚠️ 阅读行为模拟失败: {str(e)}")
    
    def simulate_typing_pause(self):
        """模拟打字停顿"""
        try:
            # 模拟思考停顿
            pause_time = random.uniform(1.0, 3.0)
            time.sleep(pause_time)
            
        except Exception as e:
            print(f"⚠️ 打字停顿模拟失败: {str(e)}")
    
    def add_random_delays(self, base_delay: float = 1.0, variance: float = 0.5):
        """添加随机延迟"""
        delay = base_delay + random.uniform(-variance, variance)
        delay = max(0.1, delay)  # 确保延迟不为负数
        time.sleep(delay)
    
    def random_click_method(self, element):
        """随机点击方法"""
        click_methods = [
            'action_chains_click',
            'javascript_click', 
            'direct_click'
        ]
        
        method = random.choice(click_methods)
        
        try:
            if method == 'action_chains_click':
                # 使用ActionChains点击
                self.action_chains.move_to_element(element).click().perform()
            elif method == 'javascript_click':
                # 使用JavaScript点击
                self.driver.execute_script("arguments[0].click();", element)
            else:
                # 直接点击
                element.click()
                
            print(f"✅ 使用{method}方法点击成功")
            
        except Exception as e:
            print(f"⚠️ {method}点击失败: {str(e)}")
            # 尝试备用方法
            try:
                element.click()
            except:
                self.driver.execute_script("arguments[0].click();", element)
    
    def element_position_randomization(self, element):
        """元素定位随机化"""
        try:
            # 滚动到元素附近（不是正中央）
            element_location = element.location
            element_size = element.size
            
            # 计算随机偏移
            offset_x = random.randint(-50, 50)
            offset_y = random.randint(-50, 50)
            
            target_y = element_location['y'] + offset_y
            
            self.driver.execute_script(f"window.scrollTo(0, {target_y});")
            time.sleep(random.uniform(0.5, 1.5))
            
        except Exception as e:
            print(f"⚠️ 元素定位随机化失败: {str(e)}")
    
    def request_frequency_control(self):
        """请求频率控制"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        # 如果请求过于频繁，添加延迟
        min_interval = 2.0  # 最小间隔2秒
        if time_since_last_request < min_interval:
            delay = min_interval - time_since_last_request + random.uniform(0.5, 2.0)
            print(f"⏰ 请求频率控制，延迟 {delay:.1f} 秒")
            time.sleep(delay)
        
        self.request_count += 1
        self.last_request_time = time.time()
        
        # 每10个请求后增加长延迟
        if self.request_count % 10 == 0:
            burst_delay = random.uniform(10, 30)
            print(f"⏰ 突发请求控制，延迟 {burst_delay:.1f} 秒")
            time.sleep(burst_delay)
    
    def retry_operation(self, operation_func, max_retries: int = 3, *args, **kwargs):
        """重试操作机制"""
        for attempt in range(max_retries):
            try:
                result = operation_func(*args, **kwargs)
                return result
                
            except Exception as e:
                print(f"⚠️ 操作失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                
                if attempt < max_retries - 1:
                    # 指数退避延迟
                    delay = (2 ** attempt) + random.uniform(1, 3)
                    print(f"⏰ 重试延迟 {delay:.1f} 秒")
                    time.sleep(delay)
                    
                    # 重新加载防检测脚本
                    self.load_anti_detection_scripts()
                else:
                    raise e
        
        return None
    
    def verification_complete(self):
        """验证码处理完成"""
        try:
            # 切换到无头模式（如果需要）
            self.driver.switch_to.default_content()
            
            # 等待验证完成
            time.sleep(random.uniform(2, 5))
            
            print("✅ 验证码处理完成")
            
        except Exception as e:
            print(f"⚠️ 验证码处理失败: {str(e)}")
    
    def network_error_handling(self, operation_func, *args, **kwargs):
        """网络错误处理"""
        try:
            return operation_func(*args, **kwargs)
            
        except Exception as e:
            error_msg = str(e).lower()
            
            if any(keyword in error_msg for keyword in ['timeout', 'connection', 'network']):
                print(f"🌐 检测到网络错误: {str(e)}")
                
                # 网络错误重试策略
                retry_delay = random.uniform(5, 15)
                print(f"⏰ 网络错误重试延迟 {retry_delay:.1f} 秒")
                time.sleep(retry_delay)
                
                # 刷新页面
                try:
                    self.driver.refresh()
                    time.sleep(random.uniform(3, 8))
                    return operation_func(*args, **kwargs)
                except:
                    pass
            
            raise e

    def get_enhanced_chrome_options(self) -> Options:
        """获取增强的Chrome选项"""
        options = Options()

        # 用户数据目录
        user_data_dir = os.path.join(os.getcwd(), "chrome_user_data")
        if not os.path.exists(user_data_dir):
            os.makedirs(user_data_dir)
        options.add_argument(f'--user-data-dir={user_data_dir}')

        # 随机User-Agent
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        options.add_argument(f'--user-agent={random.choice(user_agents)}')

        # 核心反检测参数
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)

        # 高级反检测参数
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-sync')
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-logging')
        options.add_argument('--disable-plugins-discovery')
        options.add_argument('--disable-preconnect')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-ipc-flooding-protection')

        # 隐藏自动化特征
        options.add_experimental_option("prefs", {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1,
            "profile.default_content_setting_values.geolocation": 2
        })

        # 随机窗口大小
        window_sizes = [(1366, 768), (1920, 1080), (1440, 900), (1536, 864)]
        width, height = random.choice(window_sizes)
        options.add_argument(f'--window-size={width},{height}')

        # 网络优化
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-ssl-errors')
        options.add_argument('--ignore-certificate-errors-spki-list')

        return options

    def setup_request_interception(self):
        """设置请求拦截"""
        try:
            # 启用网络域
            self.driver.execute_cdp_cmd('Network.enable', {})

            # 设置用户代理覆盖
            user_agent = random.choice([
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ])

            self.driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": user_agent,
                "acceptLanguage": "zh-CN,zh;q=0.9,en;q=0.8",
                "platform": "Win32"
            })

            print("✅ 请求拦截设置成功")

        except Exception as e:
            print(f"⚠️ 请求拦截设置失败: {str(e)}")

    def clear_browser_traces(self):
        """清理浏览器痕迹"""
        try:
            # 清理本地存储
            self.driver.execute_script("localStorage.clear();")
            self.driver.execute_script("sessionStorage.clear();")

            # 清理cookies（保留登录相关的）
            all_cookies = self.driver.get_cookies()
            important_cookies = []

            for cookie in all_cookies:
                # 保留重要的登录cookies
                if any(keyword in cookie.get('name', '').lower()
                      for keyword in ['login', 'session', 'auth', 'token', 'user']):
                    important_cookies.append(cookie)

            # 删除所有cookies
            self.driver.delete_all_cookies()

            # 重新添加重要cookies
            for cookie in important_cookies:
                try:
                    self.driver.add_cookie(cookie)
                except:
                    pass

            print("✅ 浏览器痕迹清理完成")

        except Exception as e:
            print(f"⚠️ 浏览器痕迹清理失败: {str(e)}")

    def randomize_viewport(self):
        """随机化视口"""
        try:
            # 随机调整窗口大小
            sizes = [(1366, 768), (1920, 1080), (1440, 900), (1536, 864), (1600, 900)]
            width, height = random.choice(sizes)

            # 添加随机偏移
            width += random.randint(-50, 50)
            height += random.randint(-50, 50)

            self.driver.set_window_size(width, height)

            # 随机位置
            x = random.randint(0, 100)
            y = random.randint(0, 100)
            self.driver.set_window_position(x, y)

            print(f"✅ 视口随机化: {width}x{height} at ({x},{y})")

        except Exception as e:
            print(f"⚠️ 视口随机化失败: {str(e)}")

    def simulate_page_interaction(self):
        """模拟页面交互"""
        try:
            # 随机滚动到页面不同位置
            scroll_positions = [0, 0.25, 0.5, 0.75, 1.0]
            target_position = random.choice(scroll_positions)

            self.driver.execute_script(f"""
                window.scrollTo({{
                    top: document.body.scrollHeight * {target_position},
                    behavior: 'smooth'
                }});
            """)

            # 等待滚动完成
            time.sleep(random.uniform(1, 3))

            # 模拟鼠标悬停
            try:
                elements = self.driver.find_elements(By.TAG_NAME, "a")
                if elements:
                    element = random.choice(elements[:5])  # 选择前5个链接之一
                    self.action_chains.move_to_element(element).perform()
                    time.sleep(random.uniform(0.5, 1.5))
            except:
                pass

            print("✅ 页面交互模拟完成")

        except Exception as e:
            print(f"⚠️ 页面交互模拟失败: {str(e)}")

    def detect_and_handle_captcha(self) -> bool:
        """检测并处理验证码"""
        try:
            # 检测常见的验证码元素
            captcha_selectors = [
                "//div[contains(@class, 'captcha')]",
                "//div[contains(@class, 'verify')]",
                "//div[contains(@class, 'slider')]",
                "//iframe[contains(@src, 'captcha')]",
                "//canvas[contains(@class, 'captcha')]"
            ]

            for selector in captcha_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        print("🔍 检测到验证码，等待人工处理...")

                        # 等待用户手动处理验证码
                        input("请手动完成验证码，然后按Enter继续...")

                        # 等待验证完成
                        time.sleep(random.uniform(2, 5))
                        return True

                except:
                    continue

            return False

        except Exception as e:
            print(f"⚠️ 验证码检测失败: {str(e)}")
            return False

    def monitor_page_changes(self, timeout: int = 10) -> bool:
        """监控页面变化"""
        try:
            initial_url = self.driver.current_url
            initial_title = self.driver.title

            start_time = time.time()

            while time.time() - start_time < timeout:
                current_url = self.driver.current_url
                current_title = self.driver.title

                # 检查URL或标题是否发生变化
                if current_url != initial_url or current_title != initial_title:
                    print(f"✅ 页面发生变化: {current_url}")
                    return True

                time.sleep(0.5)

            return False

        except Exception as e:
            print(f"⚠️ 页面变化监控失败: {str(e)}")
            return False
