package com.example.cos.config;

import com.example.cos.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * JWT拦截器
 * 用于解析JWT token并设置用户信息到request attribute
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class JwtInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(JwtInterceptor.class);

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 处理CORS预检请求
        if ("OPTIONS".equals(request.getMethod())) {
            response.setStatus(HttpServletResponse.SC_OK);
            return true;
        }

        // 获取Authorization header
        String authHeader = request.getHeader("Authorization");
        
        if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
            // 提取token
            String token = authHeader.substring(7);
            
            try {
                // 解析token
                String username = jwtUtil.getUsernameFromToken(token);
                Integer adminId = jwtUtil.getAdminIdFromToken(token);
                String role = jwtUtil.getRoleFromToken(token);
                
                // 验证token
                if (jwtUtil.validateToken(token, username)) {
                    // 设置用户信息到request attribute
                    request.setAttribute("adminId", adminId);
                    request.setAttribute("username", username);
                    request.setAttribute("role", role);

                    logger.info("JWT解析成功 - 用户: {}, 角色: {}, 管理员ID: {}", username, role, adminId);
                } else {
                    logger.warn("JWT验证失败 - token无效");
                }
            } catch (Exception e) {
                logger.warn("JWT解析失败: {}", e.getMessage());
            }
        }
        
        return true;
    }
}
