package com.example.cos.controller;

import com.example.cos.common.Result;
import com.example.cos.common.ResultCode;
import com.example.cos.dto.AdminCreateDTO;
import com.example.cos.dto.AdminUpdateDTO;
import com.example.cos.entity.Admin;
import com.example.cos.service.AdminService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 管理员管理控制器（超级管理员专用）
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Api(tags = "管理员管理")
@RestController
@RequestMapping("/api/admin-management")
@Validated
public class AdminManagementController {

    private static final Logger logger = LoggerFactory.getLogger(AdminManagementController.class);

    @Autowired
    private AdminService adminService;

    /**
     * 获取所有管理员列表
     */
    @ApiOperation(value = "获取管理员列表", notes = "获取所有管理员信息（超级管理员专用）")
    @GetMapping("/list")
    public Result<List<Admin>> getAllAdmins(HttpServletRequest request) {
        try {
            // 验证超级管理员权限
            if (!isSuperAdmin(request)) {
                return Result.error(ResultCode.FORBIDDEN.getCode(), "只有超级管理员可以访问此功能");
            }

            List<Admin> admins = adminService.getAllAdmins();
            
            // 隐藏密码信息
            admins.forEach(admin -> admin.setPassword("******"));
            
            return Result.success("获取管理员列表成功", admins);
        } catch (Exception e) {
            logger.error("获取管理员列表失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取管理员列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取管理员详情
     */
    @ApiOperation(value = "获取管理员详情", notes = "根据ID获取管理员详细信息")
    @GetMapping("/{id}")
    public Result<Admin> getAdminById(@PathVariable Integer id, HttpServletRequest request) {
        try {
            // 验证超级管理员权限
            if (!isSuperAdmin(request)) {
                return Result.error(ResultCode.FORBIDDEN.getCode(), "只有超级管理员可以访问此功能");
            }

            Admin admin = adminService.getById(id);
            if (admin == null) {
                return Result.error(ResultCode.NOT_FOUND.getCode(), "管理员不存在");
            }
            
            // 隐藏密码信息
            admin.setPassword("******");
            
            return Result.success("获取管理员详情成功", admin);
        } catch (Exception e) {
            logger.error("获取管理员详情失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取管理员详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建管理员
     */
    @ApiOperation(value = "创建管理员", notes = "创建新的管理员账号")
    @PostMapping
    public Result<Admin> createAdmin(@RequestBody @Validated AdminCreateDTO createDTO, HttpServletRequest request) {
        try {
            // 验证超级管理员权限
            if (!isSuperAdmin(request)) {
                return Result.error(ResultCode.FORBIDDEN.getCode(), "只有超级管理员可以创建管理员");
            }

            logger.info("创建管理员请求: {}", createDTO);
            
            Admin admin = adminService.createAdmin(createDTO);
            
            // 隐藏密码信息
            admin.setPassword("******");
            
            return Result.success("创建管理员成功", admin);
        } catch (IllegalArgumentException e) {
            logger.warn("创建管理员参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (RuntimeException e) {
            logger.warn("创建管理员失败: {}", e.getMessage());
            return Result.error(ResultCode.BUSINESS_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("创建管理员异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "创建管理员失败: " + e.getMessage());
        }
    }

    /**
     * 更新管理员
     */
    @ApiOperation(value = "更新管理员", notes = "更新管理员信息")
    @PutMapping
    public Result<Admin> updateAdmin(@RequestBody @Validated AdminUpdateDTO updateDTO, HttpServletRequest request) {
        try {
            // 验证超级管理员权限
            if (!isSuperAdmin(request)) {
                return Result.error(ResultCode.FORBIDDEN.getCode(), "只有超级管理员可以更新管理员");
            }

            logger.info("更新管理员请求: {}", updateDTO);
            
            Admin admin = adminService.updateAdmin(updateDTO);
            
            // 隐藏密码信息
            admin.setPassword("******");
            
            return Result.success("更新管理员成功", admin);
        } catch (IllegalArgumentException e) {
            logger.warn("更新管理员参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (RuntimeException e) {
            logger.warn("更新管理员失败: {}", e.getMessage());
            return Result.error(ResultCode.BUSINESS_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("更新管理员异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "更新管理员失败: " + e.getMessage());
        }
    }

    /**
     * 删除管理员
     */
    @ApiOperation(value = "删除管理员", notes = "根据ID删除管理员")
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteAdmin(@PathVariable Integer id, HttpServletRequest request) {
        try {
            // 验证超级管理员权限
            if (!isSuperAdmin(request)) {
                return Result.error(ResultCode.FORBIDDEN.getCode(), "只有超级管理员可以删除管理员");
            }

            logger.info("删除管理员: {}", id);
            
            boolean deleted = adminService.deleteAdmin(id);
            
            return Result.success("删除管理员成功", deleted);
        } catch (RuntimeException e) {
            logger.warn("删除管理员失败: {}", e.getMessage());
            return Result.error(ResultCode.BUSINESS_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("删除管理员异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "删除管理员失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除管理员
     */
    @ApiOperation(value = "批量删除管理员", notes = "根据ID列表批量删除管理员")
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteAdmins(@RequestBody List<Integer> ids, HttpServletRequest request) {
        try {
            // 验证超级管理员权限
            if (!isSuperAdmin(request)) {
                return Result.error(ResultCode.FORBIDDEN.getCode(), "只有超级管理员可以批量删除管理员");
            }

            logger.info("批量删除管理员: {}", ids);
            
            boolean deleted = adminService.batchDeleteAdmins(ids);
            
            return Result.success("批量删除管理员成功", deleted);
        } catch (IllegalArgumentException e) {
            logger.warn("批量删除管理员参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (RuntimeException e) {
            logger.warn("批量删除管理员失败: {}", e.getMessage());
            return Result.error(ResultCode.BUSINESS_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("批量删除管理员异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "批量删除管理员失败: " + e.getMessage());
        }
    }

    /**
     * 切换管理员状态
     */
    @ApiOperation(value = "切换管理员状态", notes = "切换管理员的启用/禁用状态")
    @PutMapping("/{id}/toggle")
    public Result<Admin> toggleAdminStatus(@PathVariable Integer id, HttpServletRequest request) {
        try {
            // 验证超级管理员权限
            if (!isSuperAdmin(request)) {
                return Result.error(ResultCode.FORBIDDEN.getCode(), "只有超级管理员可以切换管理员状态");
            }

            logger.info("切换管理员状态: {}", id);
            
            Admin admin = adminService.toggleAdminStatus(id);
            
            // 隐藏密码信息
            admin.setPassword("******");
            
            return Result.success("切换管理员状态成功", admin);
        } catch (RuntimeException e) {
            logger.warn("切换管理员状态失败: {}", e.getMessage());
            return Result.error(ResultCode.BUSINESS_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("切换管理员状态异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "切换管理员状态失败: " + e.getMessage());
        }
    }

    /**
     * 重置管理员密码
     */
    @ApiOperation(value = "重置管理员密码", notes = "重置指定管理员的密码")
    @PutMapping("/{id}/reset-password")
    public Result<Boolean> resetAdminPassword(@PathVariable Integer id, @RequestBody Map<String, String> request, HttpServletRequest httpRequest) {
        try {
            // 验证超级管理员权限
            if (!isSuperAdmin(httpRequest)) {
                return Result.error(ResultCode.FORBIDDEN.getCode(), "只有超级管理员可以重置管理员密码");
            }

            String newPassword = request.get("newPassword");
            if (newPassword == null || newPassword.trim().isEmpty()) {
                return Result.error(ResultCode.PARAM_ERROR.getCode(), "新密码不能为空");
            }

            if (newPassword.length() < 6 || newPassword.length() > 20) {
                return Result.error(ResultCode.PARAM_ERROR.getCode(), "密码长度必须在6-20个字符之间");
            }

            logger.info("重置管理员密码: {}", id);
            
            boolean reset = adminService.resetAdminPassword(id, newPassword);
            
            return Result.success("重置管理员密码成功", reset);
        } catch (RuntimeException e) {
            logger.warn("重置管理员密码失败: {}", e.getMessage());
            return Result.error(ResultCode.BUSINESS_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("重置管理员密码异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "重置管理员密码失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户名是否存在
     */
    @ApiOperation(value = "检查用户名", notes = "检查用户名是否已存在")
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username, @RequestParam(required = false) Integer excludeId, HttpServletRequest request) {
        try {
            // 验证超级管理员权限
            if (!isSuperAdmin(request)) {
                return Result.error(ResultCode.FORBIDDEN.getCode(), "只有超级管理员可以检查用户名");
            }

            boolean exists = excludeId != null ? 
                adminService.isUsernameExists(username, excludeId) : 
                adminService.isUsernameExists(username);
            
            return Result.success("检查用户名完成", exists);
        } catch (Exception e) {
            logger.error("检查用户名异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "检查用户名失败: " + e.getMessage());
        }
    }

    /**
     * 验证是否为超级管理员
     */
    private boolean isSuperAdmin(HttpServletRequest request) {
        String role = (String) request.getAttribute("role");
        return "super_admin".equals(role);
    }
}
