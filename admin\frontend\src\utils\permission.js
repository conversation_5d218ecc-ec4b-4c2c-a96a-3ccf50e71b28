/**
 * 权限管理工具类
 */

// 用户角色常量
export const USER_ROLES = {
  SUPER_ADMIN: 'super_admin',      // 超级管理员
  ADMIN: 'admin',                  // 管理员
  NORMAL_ADMIN: 'normal_admin'     // 普通管理员
}

// 角色别名映射（兼容后端可能的不同命名）
export const ROLE_ALIASES = {
  'super_admin': 'super_admin',
  'admin': 'admin',
  'normal_admin': 'normal_admin',
  // 兼容可能的其他命名
  'superadmin': 'super_admin',
  'normaladmin': 'normal_admin',
  '0': 'normal_admin',  // 如果后端使用数字
  '1': 'admin',
  '2': 'super_admin',
  // 兼容中文角色名称 - 映射到英文角色
  '超级管理员': 'super_admin',
  '管理员': 'admin',
  '普通管理员': 'normal_admin'
}

// 权限常量
export const PERMISSIONS = {
  // 数据操作权限
  READ: 'read',       // 查看权限
  CREATE: 'create',   // 新增权限
  UPDATE: 'update',   // 更新权限
  DELETE: 'delete',   // 删除权限
  
  // 模块权限
  HOUSE_MANAGE: 'house_manage',           // 房源管理
  USER_MANAGE: 'user_manage',             // 用户管理
  HOUSE_SALE_MANAGE: 'house_sale_manage', // 卖房信息管理
  CONSULTATION_MANAGE: 'consultation_manage', // 合作咨询管理
  SYSTEM_MANAGE: 'system_manage'          // 系统管理
}

// 角色权限映射
export const ROLE_PERMISSIONS = {
  [USER_ROLES.SUPER_ADMIN]: [
    // 超级管理员拥有所有权限
    PERMISSIONS.READ,
    PERMISSIONS.CREATE,
    PERMISSIONS.UPDATE,
    PERMISSIONS.DELETE,
    PERMISSIONS.HOUSE_MANAGE,
    PERMISSIONS.USER_MANAGE,
    PERMISSIONS.HOUSE_SALE_MANAGE,
    PERMISSIONS.CONSULTATION_MANAGE,
    PERMISSIONS.SYSTEM_MANAGE
  ],
  [USER_ROLES.ADMIN]: [
    // 管理员拥有大部分权限，除了系统管理
    PERMISSIONS.READ,
    PERMISSIONS.CREATE,
    PERMISSIONS.UPDATE,
    PERMISSIONS.DELETE,
    PERMISSIONS.HOUSE_MANAGE,
    PERMISSIONS.USER_MANAGE,
    PERMISSIONS.HOUSE_SALE_MANAGE,
    PERMISSIONS.CONSULTATION_MANAGE
  ],
  [USER_ROLES.NORMAL_ADMIN]: [
    // 普通管理员只有查看权限
    PERMISSIONS.READ,
    PERMISSIONS.HOUSE_MANAGE,
    PERMISSIONS.USER_MANAGE,
    PERMISSIONS.HOUSE_SALE_MANAGE,
    PERMISSIONS.CONSULTATION_MANAGE
  ]
}

/**
 * 权限管理类
 */
class PermissionManager {
  constructor() {
    this.currentUser = null
    this.userRole = null
    this.userPermissions = []
  }

  /**
   * 初始化用户权限信息
   */
  init() {
    const role = localStorage.getItem('role')
    const username = localStorage.getItem('username')
    const adminId = localStorage.getItem('adminId')

    if (role && username && adminId) {
      // 标准化角色名称
      const normalizedRole = ROLE_ALIASES[role] || role

      this.currentUser = {
        id: adminId,
        username: username,
        role: normalizedRole
      }
      this.userRole = normalizedRole
      this.userPermissions = ROLE_PERMISSIONS[normalizedRole] || []

      console.log('权限初始化完成:', {
        originalRole: role,
        normalizedRole: normalizedRole,
        permissions: this.userPermissions,
        roleAliases: ROLE_ALIASES,
        rolePermissions: ROLE_PERMISSIONS
      })
    } else {
      console.log('权限初始化失败，缺少必要信息:', { role, username, adminId })
    }
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    if (!this.currentUser) {
      this.init()
    }
    return this.currentUser
  }

  /**
   * 获取当前用户角色
   */
  getUserRole() {
    if (!this.userRole) {
      this.init()
    }
    return this.userRole
  }

  /**
   * 获取当前用户权限列表
   */
  getUserPermissions() {
    if (!this.userPermissions.length) {
      this.init()
    }
    return this.userPermissions
  }

  /**
   * 检查是否有指定权限
   * @param {string} permission 权限名称
   * @returns {boolean}
   */
  hasPermission(permission) {
    const permissions = this.getUserPermissions()
    return permissions.includes(permission)
  }

  /**
   * 检查是否有多个权限中的任意一个
   * @param {string[]} permissions 权限列表
   * @returns {boolean}
   */
  hasAnyPermission(permissions) {
    const userPermissions = this.getUserPermissions()
    return permissions.some(permission => userPermissions.includes(permission))
  }

  /**
   * 检查是否有所有指定权限
   * @param {string[]} permissions 权限列表
   * @returns {boolean}
   */
  hasAllPermissions(permissions) {
    const userPermissions = this.getUserPermissions()
    return permissions.every(permission => userPermissions.includes(permission))
  }

  /**
   * 检查是否有数据操作权限
   * @param {string} operation 操作类型 (read/create/update/delete)
   * @returns {boolean}
   */
  hasDataPermission(operation) {
    return this.hasPermission(operation)
  }

  /**
   * 检查是否有模块访问权限
   * @param {string} module 模块名称
   * @returns {boolean}
   */
  hasModulePermission(module) {
    return this.hasPermission(module)
  }

  /**
   * 检查是否为超级管理员
   * @returns {boolean}
   */
  isSuperAdmin() {
    return this.getUserRole() === USER_ROLES.SUPER_ADMIN
  }

  /**
   * 检查是否为管理员
   * @returns {boolean}
   */
  isAdmin() {
    const role = this.getUserRole()
    return role === USER_ROLES.ADMIN || role === USER_ROLES.SUPER_ADMIN
  }

  /**
   * 检查是否为普通管理员
   * @returns {boolean}
   */
  isNormalAdmin() {
    return this.getUserRole() === USER_ROLES.NORMAL_ADMIN
  }

  /**
   * 获取角色显示名称
   * @param {string} role 角色代码
   * @returns {string}
   */
  getRoleDisplayName(role) {
    const roleNames = {
      [USER_ROLES.SUPER_ADMIN]: '超级管理员',
      [USER_ROLES.ADMIN]: '管理员',
      [USER_ROLES.NORMAL_ADMIN]: '普通管理员',
      // 如果已经是中文名称，直接返回
      '超级管理员': '超级管理员',
      '管理员': '管理员',
      '普通管理员': '普通管理员'
    }
    return roleNames[role] || role || '未知角色'
  }

  /**
   * 清除权限信息
   */
  clear() {
    this.currentUser = null
    this.userRole = null
    this.userPermissions = []
  }
}

// 创建全局权限管理实例
export const permissionManager = new PermissionManager()

// 权限检查函数（用于模板中）
export function hasPermission(permission) {
  return permissionManager.hasPermission(permission)
}

export function hasDataPermission(operation) {
  return permissionManager.hasDataPermission(operation)
}

export function hasModulePermission(module) {
  return permissionManager.hasModulePermission(module)
}

export function isNormalAdmin() {
  return permissionManager.isNormalAdmin()
}

export function isAdmin() {
  return permissionManager.isAdmin()
}

export function isSuperAdmin() {
  return permissionManager.isSuperAdmin()
}

// 导出默认实例
export default permissionManager
