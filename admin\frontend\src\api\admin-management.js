/**
 * 管理员管理API
 */

import request from '@/utils/request'

// API路径
const API_BASE = '/admin-management'

/**
 * 获取所有管理员列表
 */
export function getAdmins() {
  return request({
    url: `${API_BASE}/list`,
    method: 'get'
  })
}

/**
 * 根据ID获取管理员详情
 * @param {number} id 管理员ID
 */
export function getAdminById(id) {
  return request({
    url: `${API_BASE}/${id}`,
    method: 'get'
  })
}

/**
 * 创建管理员
 * @param {object} data 管理员数据
 */
export function createAdmin(data) {
  return request({
    url: API_BASE,
    method: 'post',
    data
  })
}

/**
 * 更新管理员
 * @param {object} data 管理员数据
 */
export function updateAdmin(data) {
  return request({
    url: API_BASE,
    method: 'put',
    data
  })
}

/**
 * 删除管理员
 * @param {number} id 管理员ID
 */
export function deleteAdmin(id) {
  return request({
    url: `${API_BASE}/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除管理员
 * @param {array} ids 管理员ID列表
 */
export function batchDeleteAdmins(ids) {
  return request({
    url: `${API_BASE}/batch`,
    method: 'delete',
    data: ids
  })
}

/**
 * 切换管理员状态
 * @param {number} id 管理员ID
 */
export function toggleAdminStatus(id) {
  return request({
    url: `${API_BASE}/${id}/toggle`,
    method: 'put'
  })
}

/**
 * 重置管理员密码
 * @param {number} id 管理员ID
 * @param {string} newPassword 新密码
 */
export function resetAdminPassword(id, newPassword) {
  return request({
    url: `${API_BASE}/${id}/reset-password`,
    method: 'put',
    data: { newPassword }
  })
}

/**
 * 检查用户名是否存在
 * @param {string} username 用户名
 * @param {number} excludeId 排除的ID（可选）
 */
export function checkUsername(username, excludeId = null) {
  const params = { username }
  if (excludeId) {
    params.excludeId = excludeId
  }
  
  return request({
    url: `${API_BASE}/check-username`,
    method: 'get',
    params
  })
}
