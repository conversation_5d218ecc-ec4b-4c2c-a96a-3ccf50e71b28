import request from '@/utils/request'

// 文件管理API

/**
 * 上传单个文件
 * @param {FormData} formData 文件数据
 */
export function uploadFile(formData) {
  return request({
    url: '/cos/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 上传多个文件
 * @param {FormData} formData 文件数据
 */
export function uploadMultipleFiles(formData) {
  return request({
    url: '/cos/upload/multiple',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 下载文件
 * @param {String} fileName 文件名
 */
export function downloadFile(fileName) {
  return request({
    url: '/cos/download',
    method: 'get',
    params: { fileName },
    responseType: 'blob'
  })
}

/**
 * 获取文件预览URL
 * @param {String} fileName 文件名
 */
export function getFilePreviewUrl(fileName) {
  return request({
    url: '/cos/preview',
    method: 'get',
    params: { fileName }
  })
}

/**
 * 删除文件
 * @param {String} fileName 文件名
 */
export function deleteFile(fileName) {
  return request({
    url: '/cos/delete',
    method: 'delete',
    params: { fileName }
  })
}

/**
 * 获取文件列表
 * @param {String} prefix 文件前缀
 */
export function getFileList(prefix = '') {
  return request({
    url: '/cos/list',
    method: 'get',
    params: { prefix }
  })
}
