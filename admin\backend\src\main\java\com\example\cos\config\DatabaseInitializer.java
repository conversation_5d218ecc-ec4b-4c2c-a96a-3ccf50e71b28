package com.example.cos.config;

import com.example.cos.entity.Admin;
import com.example.cos.service.AdminService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 数据库初始化器
 * 在应用启动时自动创建默认管理员账号
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class DatabaseInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseInitializer.class);

    @Autowired
    private AdminService adminService;

    @Override
    public void run(String... args) throws Exception {
        initDefaultAdmin();
    }

    /**
     * 初始化默认管理员账号
     */
    private void initDefaultAdmin() {
        try {
            // 检查是否已存在默认管理员
            Admin existingAdmin = adminService.findByUsername("admin");
            
            if (existingAdmin == null) {
                // 创建默认管理员账号
                Admin defaultAdmin = adminService.createAdmin("admin", "admin123", "超级管理员");
                logger.info("默认管理员账号创建成功: {}", defaultAdmin.getUsername());
                logger.info("默认登录信息 - 用户名: admin, 密码: admin123");
            } else {
                logger.info("默认管理员账号已存在: {}", existingAdmin.getUsername());
            }
            
        } catch (Exception e) {
            logger.error("初始化默认管理员账号失败: {}", e.getMessage(), e);
        }
    }
}
