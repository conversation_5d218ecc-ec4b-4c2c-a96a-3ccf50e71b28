package com.example.cos.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * 轮播图实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "轮播图")
@TableName("carousel")
public class Carousel {

    @ApiModelProperty(value = "轮播图ID", example = "1")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "轮播图图片链接", example = "https://example.com/image.jpg", required = true)
    @TableField("image_url")
    private String imageUrl;

    @ApiModelProperty(value = "点击跳转链接", example = "https://example.com/page")
    @TableField("jump_url")
    private String jumpUrl;

    @ApiModelProperty(value = "是否启用", example = "1", required = true)
    @TableField("is_enabled")
    private Integer isEnabled;

    @ApiModelProperty(value = "排序序号", example = "1", required = true)
    @TableField("sort_order")
    private Integer sortOrder;

    @ApiModelProperty(value = "创建时间", example = "2024-01-15 10:30:00")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间", example = "2024-01-15 10:30:00")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注信息", example = "首页轮播图")
    @TableField("remark")
    private String remark;

    public Carousel() {}

    public Carousel(String imageUrl, String jumpUrl, Integer isEnabled, Integer sortOrder, String remark) {
        this.imageUrl = imageUrl;
        this.jumpUrl = jumpUrl;
        this.isEnabled = isEnabled;
        this.sortOrder = sortOrder;
        this.remark = remark;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public Integer getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(Integer isEnabled) {
        this.isEnabled = isEnabled;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "Carousel{" +
                "id=" + id +
                ", imageUrl='" + imageUrl + '\'' +
                ", jumpUrl='" + jumpUrl + '\'' +
                ", isEnabled=" + isEnabled +
                ", sortOrder=" + sortOrder +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", remark='" + remark + '\'' +
                '}';
    }
}
