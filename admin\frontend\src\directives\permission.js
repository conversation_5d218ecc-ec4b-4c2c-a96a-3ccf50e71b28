/**
 * 权限指令
 * 用法：
 * v-permission="'create'" - 检查单个权限
 * v-permission="['create', 'update']" - 检查多个权限（任意一个）
 * v-permission:all="['create', 'update']" - 检查多个权限（全部）
 */

import { permissionManager } from '@/utils/permission'

function checkPermission(el, binding) {
  const { value, arg } = binding
  
  if (!value) {
    throw new Error('权限指令需要指定权限值')
  }

  let hasPermission = false

  if (Array.isArray(value)) {
    // 数组形式的权限检查
    if (arg === 'all') {
      // 检查是否拥有所有权限
      hasPermission = permissionManager.hasAllPermissions(value)
    } else {
      // 检查是否拥有任意一个权限
      hasPermission = permissionManager.hasAnyPermission(value)
    }
  } else {
    // 单个权限检查
    hasPermission = permissionManager.hasPermission(value)
  }

  if (!hasPermission) {
    // 没有权限时隐藏元素
    el.style.display = 'none'
    el.setAttribute('disabled', 'disabled')
  } else {
    // 有权限时显示元素
    el.style.display = ''
    el.removeAttribute('disabled')
  }
}

export default {
  mounted(el, binding) {
    checkPermission(el, binding)
  },
  updated(el, binding) {
    checkPermission(el, binding)
  }
}
