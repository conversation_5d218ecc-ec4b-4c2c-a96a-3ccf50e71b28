<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.cos.mapper.HouseResourceMapper">

    <!-- 分页查询房源（支持多条件筛选） -->
    <select id="selectPageWithConditions" resultType="com.example.cos.entity.HouseResource">
        SELECT * FROM house_resource
        <where>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="auctionStatus != null">
                AND auction_status = #{auctionStatus}
            </if>
            <if test="houseType != null and houseType != ''">
                AND house_type = #{houseType}
            </if>
            <if test="communityName != null and communityName != ''">
                AND community_name LIKE CONCAT('%', #{communityName}, '%')
            </if>
            <if test="minStartingPrice != null">
                AND starting_price >= #{minStartingPrice}
            </if>
            <if test="maxStartingPrice != null">
                AND starting_price &lt;= #{maxStartingPrice}
            </if>
            <if test="minEvaluationPrice != null">
                AND evaluation_price >= #{minEvaluationPrice}
            </if>
            <if test="maxEvaluationPrice != null">
                AND evaluation_price &lt;= #{maxEvaluationPrice}
            </if>
            <if test="startTimeFrom != null">
                AND start_time >= #{startTimeFrom}
            </if>
            <if test="startTimeTo != null">
                AND start_time &lt;= #{startTimeTo}
            </if>
            <if test="stairsType != null">
                AND stairs_type = #{stairsType}
            </if>
            <if test="propertyType != null">
                AND property_type = #{propertyType}
            </if>
            <if test="decoration != null">
                AND decoration = #{decoration}
            </if>
            <if test="houseCategory != null">
                AND house_category = #{houseCategory}
            </if>
            <if test="isSelected != null">
                AND is_selected = #{isSelected}
            </if>
            <if test="isSpecial != null">
                AND is_special = #{isSpecial}
            </if>
            <if test="constructionYearFrom != null">
                AND construction_year >= #{constructionYearFrom}
            </if>
            <if test="constructionYearTo != null">
                AND construction_year &lt;= #{constructionYearTo}
            </if>
            <if test="minBuildingArea != null">
                AND building_area >= #{minBuildingArea}
            </if>
            <if test="maxBuildingArea != null">
                AND building_area &lt;= #{maxBuildingArea}
            </if>
            <if test="tags != null and tags != ''">
                AND tags LIKE CONCAT('%', #{tags}, '%')
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- 结果映射 -->
    <resultMap id="HouseResourceResultMap" type="com.example.cos.entity.HouseResource">
        <id column="id" property="id"/>
        <result column="auction_status" property="auctionStatus"/>
        <result column="title" property="title"/>
        <result column="starting_price" property="startingPrice"/>
        <result column="evaluation_price" property="evaluationPrice"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="auction_cycle" property="auctionCycle"/>
        <result column="house_type" property="houseType"/>
        <result column="building_area" property="buildingArea"/>
        <result column="community_name" property="communityName"/>
        <result column="stairs_type" property="stairsType"/>
        <result column="property_type" property="propertyType"/>
        <result column="deposit" property="deposit"/>
        <result column="construction_year" property="constructionYear"/>
        <result column="floor" property="floor"/>
        <result column="auction_times" property="auctionTimes"/>
        <result column="price_increment" property="priceIncrement"/>
        <result column="decoration" property="decoration"/>
        <result column="is_selected" property="isSelected"/>
        <result column="is_special" property="isSpecial"/>
        <result column="original_url" property="originalUrl"/>
        <result column="house_category" property="houseCategory"/>
        <result column="image_urls" property="imageUrls"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="tags" property="tags"/>
        <result column="discount_rate" property="discountRate"/>
        <result column="bargain_space" property="bargainSpace"/>
        <result column="market_unit_price" property="marketUnitPrice"/>
        <result column="starting_unit_price" property="startingUnitPrice"/>
    </resultMap>

    <!-- 批量插入房源 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO house_resource (
            auction_status, title, starting_price, evaluation_price, start_time, end_time,
            house_type, building_area, community_name, stairs_type, property_type, deposit,
            construction_year, floor, auction_times, price_increment, decoration,
            is_selected, is_special, original_url, house_category, image_urls,
            longitude, latitude, tags
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.auctionStatus}, #{item.title}, #{item.startingPrice}, #{item.evaluationPrice},
                #{item.startTime}, #{item.endTime}, #{item.houseType}, #{item.buildingArea},
                #{item.communityName}, #{item.stairsType}, #{item.propertyType}, #{item.deposit},
                #{item.constructionYear}, #{item.floor}, #{item.auctionTimes}, #{item.priceIncrement},
                #{item.decoration}, #{item.isSelected}, #{item.isSpecial}, #{item.originalUrl},
                #{item.houseCategory}, #{item.imageUrls}, #{item.longitude}, #{item.latitude}, #{item.tags}
            )
        </foreach>
    </insert>

    <!-- 批量更新房源状态 -->
    <update id="batchUpdateStatus" parameterType="java.util.Map">
        UPDATE house_resource SET auction_status = #{status}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据条件删除房源 -->
    <delete id="deleteByConditions" parameterType="java.util.Map">
        DELETE FROM house_resource
        <where>
            <if test="auctionStatus != null">
                AND auction_status = #{auctionStatus}
            </if>
            <if test="endTimeBefore != null">
                AND end_time &lt; #{endTimeBefore}
            </if>
            <if test="isSpecial != null">
                AND is_special = #{isSpecial}
            </if>
        </where>
    </delete>

</mapper>