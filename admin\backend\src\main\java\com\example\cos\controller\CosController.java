package com.example.cos.controller;

import com.example.cos.common.Result;
import com.example.cos.common.ResultCode;
import com.example.cos.config.CosProperties;
import com.example.cos.dto.CdnConfigDTO;
import com.example.cos.dto.FileUploadResult;
import com.example.cos.service.CosService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 腾讯云COS文件存储控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Api(tags = "腾讯云COS文件存储")
@RestController
@RequestMapping("/api/cos")
@CrossOrigin(originPatterns = {
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:*",
    "http://127.0.0.1:*",
    "https://cqjxzc.com.cn",
    "http://cqjxzc.com.cn",
    "https://*.cqjxzc.com.cn",
    "http://*.cqjxzc.com.cn"
}, allowCredentials = "true", maxAge = 3600)
public class CosController {

    private static final Logger logger = LoggerFactory.getLogger(CosController.class);

    @Autowired
    private CosService cosService;

    @Autowired
    private CosProperties cosProperties;

    /**
     * 上传单个文件
     */
    @ApiOperation(value = "上传单个文件", notes = "支持上传单个文件到腾讯云COS")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "要上传的文件", required = true, dataType = "file", paramType = "form"),
            @ApiImplicitParam(name = "filePath", value = "自定义文件存储路径（可选）", dataType = "string", paramType = "form")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "上传成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @RequestMapping(value = "/upload", method = {RequestMethod.POST, RequestMethod.OPTIONS})
    public Result<FileUploadResult> uploadFile(
            @RequestParam(value = "file", required = false) MultipartFile file,
            @RequestParam(value = "filePath", required = false) String filePath,
            HttpServletRequest request) {

        // 处理OPTIONS预检请求
        if ("OPTIONS".equals(request.getMethod())) {
            return Result.success("OK", null);
        }
        
        try {
            // 检查文件是否为空
            if (file == null || file.isEmpty()) {
                return Result.error(ResultCode.PARAM_ERROR.getCode(), "文件不能为空");
            }

            logger.info("开始上传文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());

            FileUploadResult result = cosService.uploadFile(file, filePath);
            
            logger.info("文件上传成功: {}", result.getFilePath());
            return Result.success("文件上传成功", result);
            
        } catch (IllegalArgumentException e) {
            logger.warn("文件上传参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("文件上传失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.FILE_UPLOAD_ERROR.getCode(), "文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传多个文件
     */
    @ApiOperation(value = "上传多个文件", notes = "支持批量上传多个文件到腾讯云COS")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "files", value = "要上传的文件列表", required = true, dataType = "file", paramType = "form", allowMultiple = true),
            @ApiImplicitParam(name = "filePathPrefix", value = "文件路径前缀（可选）", dataType = "string", paramType = "form")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "上传成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/upload/batch")
    public Result<List<FileUploadResult>> uploadFiles(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "filePathPrefix", required = false) String filePathPrefix) {
        
        try {
            logger.info("开始批量上传文件，文件数量: {}", files.length);
            
            List<MultipartFile> fileList = Arrays.asList(files);
            List<FileUploadResult> results = cosService.uploadFiles(fileList, filePathPrefix);
            
            logger.info("批量文件上传成功，成功数量: {}", results.size());
            return Result.success("批量文件上传成功", results);
            
        } catch (IllegalArgumentException e) {
            logger.warn("批量文件上传参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("批量文件上传失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.FILE_UPLOAD_ERROR.getCode(), "批量文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件（强制下载）
     */
    @ApiOperation(value = "下载文件", notes = "从腾讯云COS下载文件（强制下载）")
    @ApiImplicitParam(name = "filePath", value = "文件存储路径", required = true, dataType = "string", paramType = "path")
    @ApiResponses({
            @ApiResponse(code = 200, message = "下载成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 404, message = "文件不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/download/**")
    public void downloadFile(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 从请求路径中提取文件路径
            String requestURI = request.getRequestURI();
            String filePath = requestURI.substring(requestURI.indexOf("/download/") + 10);

            if (!StringUtils.hasText(filePath)) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("文件路径不能为空");
                return;
            }

            logger.info("开始强制下载文件: {}", filePath);

            cosService.forceDownloadFile(filePath, response);

            logger.info("文件强制下载成功: {}", filePath);

        } catch (IllegalArgumentException e) {
            logger.warn("文件下载参数错误: {}", e.getMessage());
            try {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("参数错误: " + e.getMessage());
            } catch (Exception ex) {
                logger.error("写入错误响应失败", ex);
            }
        } catch (Exception e) {
            logger.error("文件下载失败: {}", e.getMessage(), e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("文件下载失败: " + e.getMessage());
            } catch (Exception ex) {
                logger.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 预览文件（在浏览器中直接显示）
     */
    @ApiOperation(value = "预览文件", notes = "在浏览器中预览文件（图片、视频等）")
    @ApiImplicitParam(name = "filePath", value = "文件存储路径", required = true, dataType = "string", paramType = "path")
    @ApiResponses({
            @ApiResponse(code = 200, message = "预览成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 404, message = "文件不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/preview/**")
    public void previewFile(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 从请求路径中提取文件路径
            String requestURI = request.getRequestURI();
            String filePath = requestURI.substring(requestURI.indexOf("/preview/") + 9);

            if (!StringUtils.hasText(filePath)) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("文件路径不能为空");
                return;
            }

            logger.info("开始预览文件: {}", filePath);

            // 使用下载方法，但会根据文件类型自动设置为预览模式
            cosService.downloadFile(filePath, response);

            logger.info("文件预览成功: {}", filePath);

        } catch (IllegalArgumentException e) {
            logger.warn("文件预览参数错误: {}", e.getMessage());
            try {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("参数错误: " + e.getMessage());
            } catch (Exception ex) {
                logger.error("写入错误响应失败", ex);
            }
        } catch (Exception e) {
            logger.error("文件预览失败: {}", e.getMessage(), e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("文件预览失败: " + e.getMessage());
            } catch (Exception ex) {
                logger.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 删除文件
     */
    @ApiOperation(value = "删除文件", notes = "从腾讯云COS删除文件")
    @ApiImplicitParam(name = "filePath", value = "文件存储路径", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "删除成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 404, message = "文件不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @DeleteMapping("/delete")
    public Result<Boolean> deleteFile(@RequestParam("filePath") String filePath) {
        try {
            logger.info("开始删除文件: {}", filePath);

            boolean result = cosService.deleteFile(filePath);

            if (result) {
                logger.info("文件删除成功: {}", filePath);
                return Result.success("文件删除成功", true);
            } else {
                logger.warn("文件不存在或删除失败: {}", filePath);
                return Result.error(ResultCode.FILE_NOT_EXISTS.getCode(), "文件不存在或删除失败");
            }

        } catch (IllegalArgumentException e) {
            logger.warn("文件删除参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("文件删除失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.FILE_DELETE_ERROR.getCode(), "文件删除失败: " + e.getMessage());
        }
    }

    /**
     * 检查文件是否存在
     */
    @ApiOperation(value = "检查文件是否存在", notes = "检查腾讯云COS中文件是否存在")
    @ApiImplicitParam(name = "filePath", value = "文件存储路径", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "检查成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/exists")
    public Result<Boolean> fileExists(@RequestParam("filePath") String filePath) {
        try {
            logger.info("检查文件是否存在: {}", filePath);

            boolean exists = cosService.fileExists(filePath);

            logger.info("文件存在检查结果: {}, 存在: {}", filePath, exists);
            return Result.success("检查完成", exists);

        } catch (IllegalArgumentException e) {
            logger.warn("文件存在检查参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("文件存在检查失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.COS_SERVICE_ERROR.getCode(), "文件存在检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件访问URL
     */
    @ApiOperation(value = "获取文件访问URL", notes = "获取腾讯云COS中文件的访问URL")
    @ApiImplicitParam(name = "filePath", value = "文件存储路径", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "获取成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/url")
    public Result<String> getFileUrl(@RequestParam("filePath") String filePath) {
        try {
            logger.info("获取文件访问URL: {}", filePath);

            String fileUrl = cosService.getFileUrl(filePath);

            logger.info("文件访问URL获取成功: {}", fileUrl);
            return Result.success("获取成功", fileUrl);

        } catch (IllegalArgumentException e) {
            logger.warn("获取文件URL参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("获取文件URL失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.COS_SERVICE_ERROR.getCode(), "获取文件URL失败: " + e.getMessage());
        }
    }

    /**
     * 获取CDN配置信息
     */
    @ApiOperation(value = "获取CDN配置信息", notes = "获取当前CDN加速配置状态")
    @ApiResponses({
            @ApiResponse(code = 200, message = "获取成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/cdn-config")
    public Result<CdnConfigDTO> getCdnConfig() {
        try {
            logger.info("获取CDN配置信息");

            // 构建CDN配置信息响应
            CdnConfigDTO cdnConfig = new CdnConfigDTO(
                cosProperties.getEnableCdn(),
                cosProperties.getCdnDomain()
            );

            logger.info("CDN配置信息获取成功: {}", cdnConfig);
            return Result.success("获取成功", cdnConfig);

        } catch (Exception e) {
            logger.error("获取CDN配置信息失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.COS_SERVICE_ERROR.getCode(), "获取CDN配置信息失败: " + e.getMessage());
        }
    }
}
