/*
 Navicat Premium Data Transfer

 Source Server         : database
 Source Server Type    : MySQL
 Source Server Version : 80042
 Source Host           : localhost:3306
 Source Schema         : jinxiu_asset

 Target Server Type    : MySQL
 Target Server Version : 80042
 File Encoding         : 65001

 Date: 31/07/2025 16:08:49
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin_table
-- ----------------------------
DROP TABLE IF EXISTS `admin_table`;
CREATE TABLE `admin_table`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `status` tinyint(1) NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for carousel
-- ----------------------------
DROP TABLE IF EXISTS `carousel`;
CREATE TABLE `carousel`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '轮播图ID，自增主键',
  `image_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '轮播图图片链接（完整URL）',
  `jump_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '点击轮播图后的跳转链接（可为空，如无跳转则留空）',
  `is_enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用（1-启用，0-禁用）',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序序号（数字越小越靠前）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注信息（如轮播图用途说明）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '轮播图管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cooperation_consultation_table
-- ----------------------------
DROP TABLE IF EXISTS `cooperation_consultation_table`;
CREATE TABLE `cooperation_consultation_table`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `contact_info` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `consultation_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `cooperation_consultation_table_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user_table` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for house_resource
-- ----------------------------
DROP TABLE IF EXISTS `house_resource`;
CREATE TABLE `house_resource`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `auction_status` tinyint NULL DEFAULT NULL COMMENT '拍卖状态（0-未开拍，1-一拍中，2-二拍中，3-变卖中，4-已结束）',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '房源标题',
  `starting_price` decimal(15, 2) NULL DEFAULT NULL COMMENT '起拍价（元）',
  `evaluation_price` decimal(15, 2) NULL DEFAULT NULL COMMENT '评估价（元）',
  `start_time` datetime NULL DEFAULT NULL COMMENT '起拍时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `auction_cycle` int GENERATED ALWAYS AS (timestampdiff(DAY,`start_time`,`end_time`)) STORED COMMENT '竞价周期（天）' NULL,
  `house_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '户型（如“3室2厅1卫”）',
  `building_area` decimal(10, 2) NULL DEFAULT NULL COMMENT '建筑面积（㎡）',
  `community_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '小区名称',
  `stairs_type` tinyint NULL DEFAULT NULL COMMENT '梯部类型（0-楼梯，1-电梯）',
  `property_type` tinyint NULL DEFAULT NULL COMMENT '物业类型（0-低层，1-中层，2-高层）',
  `deposit` decimal(15, 2) NULL DEFAULT NULL COMMENT '保证金（元）',
  `construction_year` int NULL DEFAULT NULL COMMENT '建筑年份（如2010）',
  `floor` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '楼层（如“15/30”）',
  `auction_times` int NULL DEFAULT NULL COMMENT '拍卖次数',
  `price_increment` decimal(10, 2) NULL DEFAULT NULL COMMENT '加价幅度（元）',
  `decoration` tinyint NULL DEFAULT NULL COMMENT '装修情况（0-毛坯，1-简装，2-精装）',
  `is_selected` tinyint NULL DEFAULT NULL COMMENT '是否精选（0-否，1-是）',
  `is_special` tinyint NULL DEFAULT NULL COMMENT '是否特殊房屋（0-否，1-是）',
  `original_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原链接',
  `house_category` tinyint NULL DEFAULT NULL COMMENT '房屋类型（0-住宅，1-商办）',
  `image_urls` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '房屋图链接（逗号分隔多个URL）',
  `longitude` decimal(10, 6) NULL DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10, 6) NULL DEFAULT NULL COMMENT '纬度',
  `tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '房屋标签（逗号分隔）',
  `discount_rate` decimal(5, 2) GENERATED ALWAYS AS (((`starting_price` / `evaluation_price`) * 100)) STORED COMMENT '折扣率（%）' NULL,
  `bargain_space` decimal(15, 2) GENERATED ALWAYS AS ((`evaluation_price` - `starting_price`)) STORED COMMENT '捡漏空间（元）' NULL,
  `market_unit_price` decimal(10, 2) GENERATED ALWAYS AS ((`evaluation_price` / `building_area`)) STORED COMMENT '市场单价（元/㎡）' NULL,
  `starting_unit_price` decimal(10, 2) GENERATED ALWAYS AS ((`starting_price` / `building_area`)) STORED COMMENT '起拍单价（元/㎡）' NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '房屋资源表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for house_sale_registration_table
-- ----------------------------
DROP TABLE IF EXISTS `house_sale_registration_table`;
CREATE TABLE `house_sale_registration_table`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `house_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `community_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `housing_area` decimal(10, 2) NULL DEFAULT NULL,
  `expected_price` decimal(10, 2) NULL DEFAULT NULL,
  `contact_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `contact_info` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `house_sale_registration_table_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user_table` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_table
-- ----------------------------
DROP TABLE IF EXISTS `user_table`;
CREATE TABLE `user_table`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `phone_number` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `wechat_openid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `avatar_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `favorite_house_ids` json NULL,
  `followed_house_ids` json NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `role` tinyint(3) UNSIGNED ZEROFILL NULL DEFAULT 000,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
