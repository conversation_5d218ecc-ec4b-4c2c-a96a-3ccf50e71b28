<template>
  <div class="app-container">
    <div class="page-header">
      <h2>合作咨询管理</h2>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
        <el-form-item label="城市" prop="city">
          <el-input
            v-model="queryParams.city"
            placeholder="请输入城市"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入姓名"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="联系方式" prop="contactInfo">
          <el-input
            v-model="queryParams.contactInfo"
            placeholder="请输入联系方式"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <el-button type="success" @click="handleBatchExport" :disabled="!multipleSelection.length">
          <el-icon><Download /></el-icon>
          批量导出
        </el-button>
        <Permission permission="delete">
          <el-button type="danger" @click="handleBatchDelete" :disabled="!multipleSelection.length">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </Permission>
      </div>
      <div class="toolbar-right">
        <el-tooltip content="刷新" placement="top">
          <el-button circle @click="getList">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="consultationList"
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="咨询ID" prop="id" width="80" />
      <el-table-column label="城市" prop="city" width="100" />
      <el-table-column label="姓名" prop="name" width="100" />
      <el-table-column label="性别" prop="gender" width="80" />
      <el-table-column label="联系方式" prop="contactInfo" width="130" />
      <el-table-column label="咨询内容" prop="consultationContent" min-width="200" show-overflow-tooltip />
      <el-table-column label="状态" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ scope.row.status || '待处理' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="180" />
      <el-table-column label="操作" fixed="right" width="320">
        <template #default="scope">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <Permission permission="update">
              <el-button type="success" size="small" @click="handleReply(scope.row)">
                回复
              </el-button>
            </Permission>
            <Permission permission="update">
              <el-button type="warning" size="small" @click="handleUpdate(scope.row)">
                编辑
              </el-button>
            </Permission>
            <Permission permission="delete">
              <el-button type="danger" size="small" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </Permission>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="700px"
      :before-close="handleDialogClose"
    >
      <ConsultationForm
        ref="consultationFormRef"
        :consultation-data="currentConsultation"
        :is-edit="isEdit"
        @submit="handleFormSubmit"
      />
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      title="咨询详情"
      v-model="viewDialogVisible"
      width="700px"
    >
      <ConsultationDetail :consultation-data="currentConsultation" />
    </el-dialog>

    <!-- 回复对话框 -->
    <el-dialog
      title="回复咨询"
      v-model="replyDialogVisible"
      width="600px"
    >
      <ConsultationReply
        :consultation-data="currentConsultation"
        @submit="handleReplySubmit"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAllConsultations, createConsultation, updateConsultation, deleteConsultation } from '@/api/consultation'
import ConsultationForm from './components/ConsultationForm.vue'
import ConsultationDetail from './components/ConsultationDetail.vue'
import ConsultationReply from './components/ConsultationReply.vue'

// 响应式数据
const loading = ref(false)
const consultationList = ref([])
const total = ref(0)
const multipleSelection = ref([])
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const replyDialogVisible = ref(false)
const isEdit = ref(false)
const currentConsultation = ref({})
const consultationFormRef = ref()

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  city: '',
  name: '',
  contactInfo: ''
})

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑咨询' : '新增咨询'
})

// 获取咨询列表
const getList = async () => {
  loading.value = true
  try {
    const response = await getAllConsultations()
    consultationList.value = response.data || []
    total.value = consultationList.value.length
  } catch (error) {
    console.error('获取咨询列表失败:', error)
    ElMessage.error('获取咨询列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    city: '',
    name: '',
    contactInfo: ''
  })
  getList()
}

// 分页处理
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 多选处理
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

// 新增
const handleAdd = () => {
  currentConsultation.value = {}
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑
const handleUpdate = (row) => {
  currentConsultation.value = { ...row }
  isEdit.value = true
  dialogVisible.value = true
}

// 查看详情
const handleView = (row) => {
  currentConsultation.value = { ...row }
  viewDialogVisible.value = true
}

// 回复咨询
const handleReply = (row) => {
  currentConsultation.value = { ...row }
  replyDialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除"${row.name}"的咨询记录吗？`,
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteConsultation(row.id)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的${multipleSelection.value.length}条记录吗？`,
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('批量删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 批量导出
const handleBatchExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 表单提交
const handleFormSubmit = async (formData) => {
  try {
    if (isEdit.value) {
      await updateConsultation(formData)
      ElMessage.success('更新成功')
    } else {
      await createConsultation(formData)
      ElMessage.success('新增成功')
    }
    
    dialogVisible.value = false
    getList()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '新增失败')
  }
}

// 回复提交
const handleReplySubmit = async (replyData) => {
  try {
    // 这里应该调用回复API
    ElMessage.success('回复成功')
    replyDialogVisible.value = false
    getList()
  } catch (error) {
    ElMessage.error('回复失败')
  }
}

// 对话框关闭处理
const handleDialogClose = () => {
  dialogVisible.value = false
  currentConsultation.value = {}
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    '待处理': 'warning',
    '处理中': 'primary',
    '已回复': 'success',
    '已关闭': 'info'
  }
  return statusMap[status] || 'warning'
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;

  .el-button {
    margin: 0;
    flex-shrink: 0;
  }

  @media (max-width: 1200px) {
    gap: 4px;

    .el-button {
      font-size: 12px;
      padding: 4px 8px;
    }
  }
}
</style>
