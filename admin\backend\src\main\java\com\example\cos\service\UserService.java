package com.example.cos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.cos.dto.PageResultDTO;
import com.example.cos.dto.UserCreateDTO;
import com.example.cos.dto.UserQueryDTO;
import com.example.cos.entity.User;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {

    /**
     * 创建用户
     */
    User createUser(UserCreateDTO userCreateDTO);

    /**
     * 根据手机号查询用户
     */
    User findByPhoneNumber(String phoneNumber);

    /**
     * 根据微信OpenID查询用户
     */
    User findByWechatOpenid(String wechatOpenid);

    /**
     * 更新用户信息
     */
    boolean updateUser(User user);

    /**
     * 更新用户角色
     */
    boolean updateUserRole(Integer userId, Integer role);

    /**
     * 添加收藏房源
     */
    boolean addFavoriteHouse(Integer userId, Integer houseId);

    /**
     * 移除收藏房源
     */
    boolean removeFavoriteHouse(Integer userId, Integer houseId);

    /**
     * 添加关注房源
     */
    boolean addFollowedHouse(Integer userId, Integer houseId);

    /**
     * 移除关注房源
     */
    boolean removeFollowedHouse(Integer userId, Integer houseId);

    /**
     * 获取用户收藏的房源ID列表
     */
    List<Integer> getFavoriteHouseIds(Integer userId);

    /**
     * 获取用户关注的房源ID列表
     */
    List<Integer> getFollowedHouseIds(Integer userId);

    /**
     * 分页查询用户列表
     */
    PageResultDTO<User> getUserList(UserQueryDTO queryDTO);

    /**
     * 删除用户
     */
    boolean deleteUser(Integer userId);

    /**
     * 批量删除用户
     */
    boolean deleteUsers(List<Integer> userIds);

    /**
     * 获取用户总数
     */
    Long getUserCount();

    /**
     * 根据时间段统计用户数量
     */
    Long getUserCountByDateRange(String startTime, String endTime);
}
