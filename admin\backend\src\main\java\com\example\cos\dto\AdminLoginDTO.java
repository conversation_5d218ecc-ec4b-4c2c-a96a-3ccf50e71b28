package com.example.cos.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 管理员登录请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "管理员登录请求")
public class AdminLoginDTO {

    @ApiModelProperty(value = "用户名", example = "admin", required = true)
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度在3到50个字符之间")
    private String username;

    @ApiModelProperty(value = "密码", example = "admin123", required = true)
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度在6到20个字符之间")
    private String password;

    @ApiModelProperty(value = "验证码", example = "1234")
    private String captcha;

    @ApiModelProperty(value = "记住密码", example = "false")
    private Boolean rememberMe = false;

    public AdminLoginDTO() {}

    public AdminLoginDTO(String username, String password) {
        this.username = username;
        this.password = password;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCaptcha() {
        return captcha;
    }

    public void setCaptcha(String captcha) {
        this.captcha = captcha;
    }

    public Boolean getRememberMe() {
        return rememberMe;
    }

    public void setRememberMe(Boolean rememberMe) {
        this.rememberMe = rememberMe;
    }

    @Override
    public String toString() {
        return "AdminLoginDTO{" +
                "username='" + username + '\'' +
                ", password='[PROTECTED]'" +
                ", captcha='" + captcha + '\'' +
                ", rememberMe=" + rememberMe +
                '}';
    }
}
