package com.example.cos.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.cos.common.Result;
import com.example.cos.common.ResultCode;
import com.example.cos.dto.HouseResourceQueryDTO;
import com.example.cos.dto.HouseResourceStatisticsDTO;
import com.example.cos.entity.HouseResource;
import com.example.cos.service.HouseResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 房源资源管理控制器
 */
@Api(tags = "房源资源管理")
@RestController
@RequestMapping("/api/house")
@Validated
public class HouseResourceController {

    private static final Logger logger = LoggerFactory.getLogger(HouseResourceController.class);

    @Autowired
    private HouseResourceService houseResourceService;

    /**
     * 分页查询房源
     */
    @ApiOperation(value = "分页查询房源", notes = "支持多条件筛选的房源分页查询")
    @PostMapping("/page")
    public Result<IPage<HouseResource>> pageQuery(@RequestBody HouseResourceQueryDTO queryDTO) {
        try {
            logger.info("分页查询房源: {}", queryDTO);

            IPage<HouseResource> page = houseResourceService.pageQuery(queryDTO);

            logger.info("查询成功，共{}条记录", page.getTotal());
            return Result.success("查询成功", page);

        } catch (Exception e) {
            logger.error("分页查询房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询房源详情
     */
    @ApiOperation(value = "根据ID查询房源详情", notes = "获取房源的详细信息")
    @GetMapping("/{id}")
    public Result<HouseResource> getHouseById(@PathVariable Long id) {
        try {
            logger.info("根据ID查询房源详情: {}", id);

            HouseResource houseResource = houseResourceService.getHouseResourceDetail(id);

            if (houseResource != null) {
                return Result.success("查询成功", houseResource);
            } else {
                return Result.error(ResultCode.NOT_FOUND.getCode(), "房源不存在");
            }

        } catch (Exception e) {
            logger.error("查询房源详情失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建房源
     */
    @ApiOperation(value = "创建房源", notes = "添加新的房源信息")
    @PostMapping("/create")
    public Result<HouseResource> createHouse(@RequestBody @Valid HouseResource houseResource) {
        try {
            logger.info("创建房源: {}", houseResource);

            HouseResource created = houseResourceService.createHouseResource(houseResource);

            logger.info("房源创建成功: {}", created.getId());
            return Result.success("创建成功", created);

        } catch (Exception e) {
            logger.error("创建房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新房源
     */
    @ApiOperation(value = "更新房源", notes = "更新房源信息")
    @PutMapping("/update")
    public Result<Boolean> updateHouse(@RequestBody @Valid HouseResource houseResource) {
        try {
            logger.info("更新房源: {}", houseResource);

            boolean updated = houseResourceService.updateHouseResource(houseResource);

            if (updated) {
                return Result.success("更新成功", true);
            } else {
                return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "更新失败");
            }

        } catch (Exception e) {
            logger.error("更新房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除房源
     */
    @ApiOperation(value = "删除房源", notes = "根据ID删除房源")
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteHouse(@PathVariable Long id) {
        try {
            logger.info("删除房源: {}", id);

            boolean deleted = houseResourceService.deleteHouseResource(id);

            if (deleted) {
                return Result.success("删除成功", true);
            } else {
                return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "删除失败");
            }

        } catch (Exception e) {
            logger.error("删除房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除房源
     */
    @ApiOperation(value = "批量删除房源", notes = "根据ID列表批量删除房源")
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteHouses(@RequestBody List<Long> ids) {
        try {
            logger.info("批量删除房源: {}", ids);

            boolean deleted = houseResourceService.batchDeleteHouseResources(ids);

            if (deleted) {
                return Result.success("批量删除成功", true);
            } else {
                return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "批量删除失败");
            }

        } catch (Exception e) {
            logger.error("批量删除房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 根据拍卖状态查询房源
     */
    @ApiOperation(value = "根据拍卖状态查询房源", notes = "0-未开拍，1-一拍中，2-二拍中，3-变卖中，4-已结束")
    @GetMapping("/auction-status/{auctionStatus}")
    public Result<List<HouseResource>> getHousesByAuctionStatus(@PathVariable Integer auctionStatus) {
        try {
            logger.info("根据拍卖状态查询房源: {}", auctionStatus);

            List<HouseResource> houses = houseResourceService.findByAuctionStatus(auctionStatus);

            return Result.success("查询成功", houses);

        } catch (Exception e) {
            logger.error("根据拍卖状态查询房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据户型查询房源
     */
    @ApiOperation(value = "根据户型查询房源", notes = "通过户型查询房源列表")
    @GetMapping("/house-type")
    public Result<List<HouseResource>> getHousesByHouseType(@RequestParam String houseType) {
        try {
            logger.info("根据户型查询房源: {}", houseType);

            List<HouseResource> houses = houseResourceService.findByHouseType(houseType);

            return Result.success("查询成功", houses);

        } catch (Exception e) {
            logger.error("查询房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据小区名称查询房源
     */
    @ApiOperation(value = "根据小区名称查询房源", notes = "支持模糊查询")
    @GetMapping("/community")
    public Result<List<HouseResource>> getHousesByCommunityName(@RequestParam String communityName) {
        try {
            logger.info("根据小区名称查询房源: {}", communityName);

            List<HouseResource> houses = houseResourceService.findByCommunityName(communityName);

            return Result.success("查询成功", houses);

        } catch (Exception e) {
            logger.error("根据小区名称查询房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据起拍价区间查询房源
     */
    @ApiOperation(value = "根据起拍价区间查询房源", notes = "查询指定价格区间的房源")
    @GetMapping("/starting-price-range")
    public Result<List<HouseResource>> getHousesByStartingPriceRange(
            @RequestParam BigDecimal minPrice,
            @RequestParam BigDecimal maxPrice) {
        try {
            logger.info("根据起拍价区间查询房源: {} - {}", minPrice, maxPrice);

            List<HouseResource> houses = houseResourceService.findByStartingPriceRange(minPrice, maxPrice);

            return Result.success("查询成功", houses);

        } catch (Exception e) {
            logger.error("根据起拍价区间查询房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据房屋类型查询房源
     */
    @ApiOperation(value = "根据房屋类型查询房源", notes = "0-住宅，1-商办")
    @GetMapping("/house-category/{houseCategory}")
    public Result<List<HouseResource>> getHousesByHouseCategory(@PathVariable Integer houseCategory) {
        try {
            logger.info("根据房屋类型查询房源: {}", houseCategory);

            List<HouseResource> houses = houseResourceService.findByHouseCategory(houseCategory);

            return Result.success("查询成功", houses);

        } catch (Exception e) {
            logger.error("根据房屋类型查询房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据是否特殊房屋查询房源
     */
    @ApiOperation(value = "根据是否特殊房屋查询房源", notes = "0-否，1-是")
    @GetMapping("/special/{isSpecial}")
    public Result<List<HouseResource>> getHousesByIsSpecial(@PathVariable Integer isSpecial) {
        try {
            logger.info("根据是否特殊房屋查询房源: {}", isSpecial);

            List<HouseResource> houses = houseResourceService.findByIsSpecial(isSpecial);

            return Result.success("查询成功", houses);

        } catch (Exception e) {
            logger.error("根据是否特殊房屋查询房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据是否精选查询房源
     */
    @ApiOperation(value = "根据是否精选查询房源", notes = "0-否，1-是")
    @GetMapping("/selected/{isSelected}")
    public Result<List<HouseResource>> getHousesByIsSelected(@PathVariable Integer isSelected) {
        try {
            logger.info("根据是否精选查询房源: {}", isSelected);

            List<HouseResource> houses = houseResourceService.findByIsSelected(isSelected);

            return Result.success("查询成功", houses);

        } catch (Exception e) {
            logger.error("根据是否精选查询房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "查询失败: " + e.getMessage());
        }
    }

    /**
     * 搜索房源
     */
    @ApiOperation(value = "搜索房源", notes = "根据关键词搜索房源")
    @GetMapping("/search")
    public Result<List<HouseResource>> searchHouses(@RequestParam String keyword) {
        try {
            logger.info("搜索房源: {}", keyword);

            List<HouseResource> houses = houseResourceService.searchHouses(keyword);

            return Result.success("搜索成功", houses);

        } catch (Exception e) {
            logger.error("搜索房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "搜索失败: " + e.getMessage());
        }
    }

    /**
     * 获取推荐房源
     */
    @ApiOperation(value = "获取推荐房源", notes = "获取精选推荐房源")
    @GetMapping("/recommended")
    public Result<List<HouseResource>> getRecommendedHouses(@RequestParam(defaultValue = "10") int limit) {
        try {
            logger.info("获取推荐房源，数量: {}", limit);

            List<HouseResource> houses = houseResourceService.getRecommendedHouses(limit);

            return Result.success("获取成功", houses);

        } catch (Exception e) {
            logger.error("获取推荐房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取即将开拍的房源
     */
    @ApiOperation(value = "获取即将开拍的房源", notes = "获取即将开始拍卖的房源")
    @GetMapping("/upcoming")
    public Result<List<HouseResource>> getUpcomingAuctions(@RequestParam(defaultValue = "10") int limit) {
        try {
            logger.info("获取即将开拍的房源，数量: {}", limit);

            List<HouseResource> houses = houseResourceService.getUpcomingAuctions(limit);

            return Result.success("获取成功", houses);

        } catch (Exception e) {
            logger.error("获取即将开拍的房源失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取统计信息
     */
    @ApiOperation(value = "获取房源统计信息", notes = "获取各种统计数据")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getStatistics() {
        try {
            logger.info("获取房源统计信息");

            Map<String, Object> statistics = houseResourceService.getPriceStatistics();

            return Result.success("获取成功", statistics);

        } catch (Exception e) {
            logger.error("获取统计信息失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新房源状态
     */
    @ApiOperation(value = "批量更新房源状态", notes = "批量更新房源的拍卖状态")
    @PutMapping("/batch-status")
    public Result<Boolean> batchUpdateStatus(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) request.get("ids");
            Integer status = (Integer) request.get("status");

            logger.info("批量更新房源状态: ids={}, status={}", ids, status);

            boolean updated = houseResourceService.batchUpdateStatus(ids, status);

            if (updated) {
                return Result.success("批量更新成功", true);
            } else {
                return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "批量更新失败");
            }

        } catch (Exception e) {
            logger.error("批量更新房源状态失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "批量更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取房源统计数据
     */
    @ApiOperation(value = "获取房源统计数据", notes = "获取今日新增、正在拍卖、即将拍卖的房源数量统计")
    @GetMapping("/statistics/overview")
    public Result<HouseResourceStatisticsDTO> getHouseResourceStatistics() {
        try {
            logger.info("获取房源统计数据");

            HouseResourceStatisticsDTO statistics = houseResourceService.getHouseResourceStatistics();

            logger.info("房源统计数据获取成功: {}", statistics);
            return Result.success("获取成功", statistics);

        } catch (Exception e) {
            logger.error("获取房源统计数据失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取统计数据失败: " + e.getMessage());
        }
    }
}