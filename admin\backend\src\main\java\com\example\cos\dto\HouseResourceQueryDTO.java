package com.example.cos.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 房源查询DTO
 */
@ApiModel(description = "房源查询条件")
public class HouseResourceQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "房源标题", example = "阳光花园")
    private String title;

    @ApiModelProperty(value = "拍卖状态（0-未开拍，1-一拍中，2-二拍中，3-变卖中，4-已结束）", example = "1")
    private Integer auctionStatus;

    @ApiModelProperty(value = "户型", example = "3室2厅1卫")
    private String houseType;

    @ApiModelProperty(value = "小区名称", example = "阳光花园")
    private String communityName;

    @ApiModelProperty(value = "最低起拍价", example = "1000000.00")
    private BigDecimal minStartingPrice;

    @ApiModelProperty(value = "最高起拍价", example = "2000000.00")
    private BigDecimal maxStartingPrice;

    @ApiModelProperty(value = "最低评估价", example = "1200000.00")
    private BigDecimal minEvaluationPrice;

    @ApiModelProperty(value = "最高评估价", example = "2500000.00")
    private BigDecimal maxEvaluationPrice;

    @ApiModelProperty(value = "起拍时间（起）", example = "2025-01-15 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTimeFrom;

    @ApiModelProperty(value = "起拍时间（止）", example = "2025-01-30 18:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTimeTo;

    @ApiModelProperty(value = "梯部类型（0-楼梯，1-电梯）", example = "1")
    private Integer stairsType;

    @ApiModelProperty(value = "物业类型（0-低层，1-中层，2-高层）", example = "2")
    private Integer propertyType;

    @ApiModelProperty(value = "装修情况（0-毛坯，1-简装，2-精装）", example = "2")
    private Integer decoration;

    @ApiModelProperty(value = "房屋类型（0-住宅，1-商办）", example = "0")
    private Integer houseCategory;

    @ApiModelProperty(value = "是否精选（0-否，1-是）", example = "1")
    private Integer isSelected;

    @ApiModelProperty(value = "是否特殊房屋（0-否，1-是）", example = "0")
    private Integer isSpecial;

    @ApiModelProperty(value = "建筑年份（起）", example = "2000")
    private Integer constructionYearFrom;

    @ApiModelProperty(value = "建筑年份（止）", example = "2020")
    private Integer constructionYearTo;

    @ApiModelProperty(value = "最小建筑面积", example = "80.00")
    private BigDecimal minBuildingArea;

    @ApiModelProperty(value = "最大建筑面积", example = "200.00")
    private BigDecimal maxBuildingArea;

    @ApiModelProperty(value = "房屋标签", example = "学区房")
    private String tags;

    public HouseResourceQueryDTO() {}

    // Getter and Setter methods
    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getAuctionStatus() {
        return auctionStatus;
    }

    public void setAuctionStatus(Integer auctionStatus) {
        this.auctionStatus = auctionStatus;
    }

    public String getHouseType() {
        return houseType;
    }

    public void setHouseType(String houseType) {
        this.houseType = houseType;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public BigDecimal getMinStartingPrice() {
        return minStartingPrice;
    }

    public void setMinStartingPrice(BigDecimal minStartingPrice) {
        this.minStartingPrice = minStartingPrice;
    }

    public BigDecimal getMaxStartingPrice() {
        return maxStartingPrice;
    }

    public void setMaxStartingPrice(BigDecimal maxStartingPrice) {
        this.maxStartingPrice = maxStartingPrice;
    }

    public BigDecimal getMinEvaluationPrice() {
        return minEvaluationPrice;
    }

    public void setMinEvaluationPrice(BigDecimal minEvaluationPrice) {
        this.minEvaluationPrice = minEvaluationPrice;
    }

    public BigDecimal getMaxEvaluationPrice() {
        return maxEvaluationPrice;
    }

    public void setMaxEvaluationPrice(BigDecimal maxEvaluationPrice) {
        this.maxEvaluationPrice = maxEvaluationPrice;
    }

    public LocalDateTime getStartTimeFrom() {
        return startTimeFrom;
    }

    public void setStartTimeFrom(LocalDateTime startTimeFrom) {
        this.startTimeFrom = startTimeFrom;
    }

    public LocalDateTime getStartTimeTo() {
        return startTimeTo;
    }

    public void setStartTimeTo(LocalDateTime startTimeTo) {
        this.startTimeTo = startTimeTo;
    }

    public Integer getStairsType() {
        return stairsType;
    }

    public void setStairsType(Integer stairsType) {
        this.stairsType = stairsType;
    }

    public Integer getPropertyType() {
        return propertyType;
    }

    public void setPropertyType(Integer propertyType) {
        this.propertyType = propertyType;
    }

    public Integer getDecoration() {
        return decoration;
    }

    public void setDecoration(Integer decoration) {
        this.decoration = decoration;
    }

    public Integer getHouseCategory() {
        return houseCategory;
    }

    public void setHouseCategory(Integer houseCategory) {
        this.houseCategory = houseCategory;
    }

    public Integer getIsSelected() {
        return isSelected;
    }

    public void setIsSelected(Integer isSelected) {
        this.isSelected = isSelected;
    }

    public Integer getIsSpecial() {
        return isSpecial;
    }

    public void setIsSpecial(Integer isSpecial) {
        this.isSpecial = isSpecial;
    }

    public Integer getConstructionYearFrom() {
        return constructionYearFrom;
    }

    public void setConstructionYearFrom(Integer constructionYearFrom) {
        this.constructionYearFrom = constructionYearFrom;
    }

    public Integer getConstructionYearTo() {
        return constructionYearTo;
    }

    public void setConstructionYearTo(Integer constructionYearTo) {
        this.constructionYearTo = constructionYearTo;
    }

    public BigDecimal getMinBuildingArea() {
        return minBuildingArea;
    }

    public void setMinBuildingArea(BigDecimal minBuildingArea) {
        this.minBuildingArea = minBuildingArea;
    }

    public BigDecimal getMaxBuildingArea() {
        return maxBuildingArea;
    }

    public void setMaxBuildingArea(BigDecimal maxBuildingArea) {
        this.maxBuildingArea = maxBuildingArea;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    @Override
    public String toString() {
        return "HouseResourceQueryDTO{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", title='" + title + '\'' +
                ", auctionStatus=" + auctionStatus +
                ", houseType='" + houseType + '\'' +
                ", communityName='" + communityName + '\'' +
                ", minStartingPrice=" + minStartingPrice +
                ", maxStartingPrice=" + maxStartingPrice +
                ", minEvaluationPrice=" + minEvaluationPrice +
                ", maxEvaluationPrice=" + maxEvaluationPrice +
                ", startTimeFrom=" + startTimeFrom +
                ", startTimeTo=" + startTimeTo +
                ", stairsType=" + stairsType +
                ", propertyType=" + propertyType +
                ", decoration=" + decoration +
                ", houseCategory=" + houseCategory +
                ", isSelected=" + isSelected +
                ", isSpecial=" + isSpecial +
                ", constructionYearFrom=" + constructionYearFrom +
                ", constructionYearTo=" + constructionYearTo +
                ", minBuildingArea=" + minBuildingArea +
                ", maxBuildingArea=" + maxBuildingArea +
                ", tags='" + tags + '\'' +
                '}';
    }
}