package com.example.cos.constant;

/**
 * 用户角色常量类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class UserRole {

    /**
     * 普通用户
     */
    public static final Integer NORMAL_USER = 0;

    /**
     * 有权限用户
     */
    public static final Integer PRIVILEGED_USER = 1;

    /**
     * 获取角色描述
     * 
     * @param role 角色值
     * @return 角色描述
     */
    public static String getRoleDescription(Integer role) {
        if (role == null) {
            return "未知角色";
        }
        
        switch (role) {
            case 0:
                return "普通用户";
            case 1:
                return "有权限用户";
            default:
                return "未知角色";
        }
    }

    /**
     * 验证角色值是否有效
     * 
     * @param role 角色值
     * @return 是否有效
     */
    public static boolean isValidRole(Integer role) {
        return role != null && (role.equals(NORMAL_USER) || role.equals(PRIVILEGED_USER));
    }

    /**
     * 获取默认角色
     * 
     * @return 默认角色（普通用户）
     */
    public static Integer getDefaultRole() {
        return NORMAL_USER;
    }
}
