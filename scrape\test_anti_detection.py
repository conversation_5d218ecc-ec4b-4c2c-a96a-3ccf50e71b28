# -*- coding: utf-8 -*-
"""
防检测机制测试脚本
用于验证阿里资产爬虫的防检测功能
"""

import time
import sys
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from anti_detection import AntiDetectionManager
from api_client import APIClient
from ali_asset_scraper import AliAssetScraper


def test_basic_anti_detection():
    """测试基础防检测功能"""
    print("🧪 开始测试基础防检测功能...")
    
    try:
        # 创建Chrome选项
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        # 核心反检测参数
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 创建WebDriver
        driver = webdriver.Chrome(options=options)
        
        # 初始化防检测管理器
        anti_detection = AntiDetectionManager(driver)
        
        # 测试检测页面
        test_urls = [
            "https://bot.sannysoft.com/",
            "https://intoli.com/blog/not-possible-to-block-chrome-headless/chrome-headless-test.html",
            "https://arh.antoinevastel.com/bots/areyouheadless"
        ]
        
        for i, url in enumerate(test_urls, 1):
            try:
                print(f"\n📋 测试 {i}: {url}")
                driver.get(url)
                
                # 模拟人类行为
                anti_detection.simulate_human_behavior()
                
                # 等待页面加载
                time.sleep(5)
                
                # 截图保存结果
                screenshot_path = f"test_result_{i}.png"
                driver.save_screenshot(screenshot_path)
                print(f"✅ 截图已保存: {screenshot_path}")
                
                # 检查是否被检测为机器人
                page_source = driver.page_source.lower()
                bot_indicators = ['bot', 'automation', 'webdriver', 'selenium', 'headless']
                
                detected_indicators = []
                for indicator in bot_indicators:
                    if indicator in page_source:
                        detected_indicators.append(indicator)
                
                if detected_indicators:
                    print(f"⚠️ 检测到机器人特征: {detected_indicators}")
                else:
                    print("✅ 未检测到明显的机器人特征")
                
            except Exception as e:
                print(f"❌ 测试 {i} 失败: {str(e)}")
        
        # 清理资源
        driver.quit()
        print("\n✅ 基础防检测功能测试完成")
        
    except Exception as e:
        print(f"❌ 基础防检测功能测试失败: {str(e)}")


def test_ali_asset_scraper_anti_detection():
    """测试阿里资产爬虫的防检测功能"""
    print("\n🧪 开始测试阿里资产爬虫防检测功能...")
    
    try:
        # 创建API客户端
        api_client = APIClient()
        
        # 创建爬虫实例
        scraper = AliAssetScraper(api_client)
        
        # 设置浏览器驱动（不自动登录）
        if scraper.setup_driver(auto_login=False):
            print("✅ 浏览器驱动设置成功")
            
            # 测试访问阿里资产页面
            try:
                print("📋 测试访问阿里资产首页...")
                scraper.driver.get("https://zc-paimai.taobao.com")
                
                # 模拟人类行为
                if scraper.anti_detection:
                    scraper.anti_detection.simulate_human_behavior()
                
                time.sleep(5)
                
                # 检查页面标题
                page_title = scraper.driver.title
                print(f"页面标题: {page_title}")
                
                # 检查是否需要验证码
                if scraper.anti_detection:
                    captcha_detected = scraper.anti_detection.detect_and_handle_captcha()
                    if captcha_detected:
                        print("🔍 检测到验证码")
                    else:
                        print("✅ 未检测到验证码")
                
                # 截图保存结果
                scraper.driver.save_screenshot("ali_asset_test.png")
                print("✅ 阿里资产页面截图已保存: ali_asset_test.png")
                
                # 测试导航到房源列表页面
                print("📋 测试导航到房源列表页面...")
                if scraper.navigate_to_list_page(1):
                    print("✅ 成功导航到房源列表页面")
                    
                    # 截图保存结果
                    scraper.driver.save_screenshot("ali_asset_list_test.png")
                    print("✅ 房源列表页面截图已保存: ali_asset_list_test.png")
                else:
                    print("❌ 导航到房源列表页面失败")
                
            except Exception as e:
                print(f"❌ 阿里资产页面测试失败: {str(e)}")
            
            # 清理资源
            scraper.cleanup()
            print("✅ 阿里资产爬虫防检测功能测试完成")
        else:
            print("❌ 浏览器驱动设置失败")
            
    except Exception as e:
        print(f"❌ 阿里资产爬虫防检测功能测试失败: {str(e)}")


def test_javascript_anti_detection():
    """测试JavaScript防检测脚本"""
    print("\n🧪 开始测试JavaScript防检测脚本...")
    
    try:
        # 创建Chrome选项
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        # 创建WebDriver
        driver = webdriver.Chrome(options=options)
        
        # 加载增强版防检测脚本
        try:
            script_path = os.path.join(os.getcwd(), "enhanced_anti_detection.js")
            if os.path.exists(script_path):
                with open(script_path, 'r', encoding='utf-8') as f:
                    script_content = f.read()
                
                # 访问测试页面
                driver.get("data:text/html,<html><body><h1>JavaScript Test</h1></body></html>")
                
                # 执行防检测脚本
                driver.execute_script(script_content)
                print("✅ 增强版防检测脚本执行成功")
                
                # 测试webdriver属性
                webdriver_value = driver.execute_script("return navigator.webdriver;")
                print(f"navigator.webdriver: {webdriver_value}")
                
                # 测试用户代理
                user_agent = driver.execute_script("return navigator.userAgent;")
                print(f"User-Agent: {user_agent}")
                
                # 测试语言设置
                languages = driver.execute_script("return navigator.languages;")
                print(f"Languages: {languages}")
                
                # 测试插件
                plugins_length = driver.execute_script("return navigator.plugins.length;")
                print(f"Plugins count: {plugins_length}")
                
                print("✅ JavaScript防检测脚本测试完成")
            else:
                print("❌ 增强版防检测脚本文件不存在")
                
        except Exception as e:
            print(f"❌ JavaScript防检测脚本测试失败: {str(e)}")
        
        # 清理资源
        driver.quit()
        
    except Exception as e:
        print(f"❌ JavaScript防检测脚本测试失败: {str(e)}")


def main():
    """主测试函数"""
    print("🚀 开始防检测机制综合测试...")
    print("=" * 60)
    
    # 测试1: 基础防检测功能
    test_basic_anti_detection()
    
    # 测试2: JavaScript防检测脚本
    test_javascript_anti_detection()
    
    # 测试3: 阿里资产爬虫防检测功能
    test_ali_asset_scraper_anti_detection()
    
    print("\n" + "=" * 60)
    print("🎉 防检测机制综合测试完成！")
    print("\n📋 测试结果说明:")
    print("- 查看生成的截图文件了解检测结果")
    print("- 如果页面显示正常内容而非机器人警告，说明防检测有效")
    print("- 建议在实际使用前先运行此测试脚本验证环境")


if __name__ == "__main__":
    main()
