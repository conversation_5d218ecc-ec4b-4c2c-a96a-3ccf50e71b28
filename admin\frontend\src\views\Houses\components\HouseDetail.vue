<template>
  <div class="house-detail">
    <el-descriptions title="房源基本信息" :column="2" border>
      <el-descriptions-item label="房源ID">{{ houseData.id }}</el-descriptions-item>
      <el-descriptions-item label="房源标题" :span="2">{{ houseData.title }}</el-descriptions-item>
      <el-descriptions-item label="拍卖状态">
        <el-tag :type="getAuctionStatusType(houseData.auctionStatus)">
          {{ getAuctionStatusText(houseData.auctionStatus) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="户型">{{ houseData.houseType }}</el-descriptions-item>
      <el-descriptions-item label="小区名称">{{ houseData.communityName }}</el-descriptions-item>
      <el-descriptions-item label="建筑面积">{{ houseData.buildingArea }}㎡</el-descriptions-item>
      <el-descriptions-item label="楼层">{{ houseData.floor }}</el-descriptions-item>
      <el-descriptions-item label="建筑年份">{{ houseData.constructionYear }}年</el-descriptions-item>
      <el-descriptions-item label="梯部类型">
        {{ houseData.stairsType === 0 ? '楼梯' : '电梯' }}
      </el-descriptions-item>
      <el-descriptions-item label="物业类型">
        {{ getPropertyTypeText(houseData.propertyType) }}
      </el-descriptions-item>
      <el-descriptions-item label="装修情况">
        {{ getDecorationText(houseData.decoration) }}
      </el-descriptions-item>
      <el-descriptions-item label="房屋类型">
        <el-tag :type="houseData.houseCategory === 0 ? 'success' : 'warning'">
          {{ houseData.houseCategory === 0 ? '住宅' : '商办' }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>

    <el-divider />

    <el-descriptions title="价格信息" :column="2" border>
      <el-descriptions-item label="起拍价">
        <span class="price">¥{{ formatPrice(houseData.startingPrice) }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="评估价">
        <span class="price">¥{{ formatPrice(houseData.evaluationPrice) }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="保证金">
        <span class="price">¥{{ formatPrice(houseData.deposit) }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="加价幅度">
        <span class="price">¥{{ formatPrice(houseData.priceIncrement) }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="折扣率">
        <span :class="{ 'discount-rate': houseData.discountRate < 80 }">
          {{ houseData.discountRate }}%
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="捡漏空间">
        <span class="price">¥{{ formatPrice(houseData.bargainSpace) }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="市场单价">
        <span class="price">¥{{ formatPrice(houseData.marketUnitPrice) }}/㎡</span>
      </el-descriptions-item>
      <el-descriptions-item label="起拍单价">
        <span class="price">¥{{ formatPrice(houseData.startingUnitPrice) }}/㎡</span>
      </el-descriptions-item>
    </el-descriptions>

    <el-divider />

    <el-descriptions title="拍卖信息" :column="2" border>
      <el-descriptions-item label="起拍时间">{{ houseData.startTime }}</el-descriptions-item>
      <el-descriptions-item label="结束时间">{{ houseData.endTime }}</el-descriptions-item>
      <el-descriptions-item label="竞价周期">{{ houseData.auctionCycle }}天</el-descriptions-item>
      <el-descriptions-item label="拍卖次数">{{ houseData.auctionTimes }}次</el-descriptions-item>
      <el-descriptions-item label="是否精选">
        <el-tag :type="houseData.isSelected === 1 ? 'success' : 'info'">
          {{ houseData.isSelected === 1 ? '是' : '否' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="特殊房屋">
        <el-tag :type="houseData.isSpecial === 1 ? 'danger' : 'info'">
          {{ houseData.isSpecial === 1 ? '是' : '否' }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>

    <el-divider />

    <el-descriptions title="位置信息" :column="2" border>
      <el-descriptions-item label="经度">{{ houseData.longitude }}</el-descriptions-item>
      <el-descriptions-item label="纬度">{{ houseData.latitude }}</el-descriptions-item>
      <el-descriptions-item label="房屋标签" :span="2">
        <el-tag v-for="tag in tagList" :key="tag" style="margin-right: 8px;">
          {{ tag }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="原链接" :span="2">
        <el-link :href="houseData.originalUrl" target="_blank" type="primary">
          查看原始页面
        </el-link>
      </el-descriptions-item>
    </el-descriptions>

    <el-divider />

    <div v-if="houseData.imageUrls" class="image-gallery">
      <h3>房屋图片</h3>
      <el-row :gutter="10">
        <el-col v-for="(image, index) in imageList" :key="index" :span="6">
          <el-image
            :src="image"
            :preview-src-list="imageList"
            :initial-index="index"
            fit="cover"
            style="width: 100%; height: 200px; border-radius: 8px;"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { computed, defineProps } from 'vue'

const props = defineProps({
  houseData: {
    type: Object,
    default: () => ({})
  }
})

// 计算属性
const imageList = computed(() => {
  if (!props.houseData.imageUrls) return []
  return props.houseData.imageUrls.split(',').filter(url => url.trim())
})

const tagList = computed(() => {
  if (!props.houseData.tags) return []
  return props.houseData.tags.split(',').filter(tag => tag.trim())
})

// 工具函数
const formatPrice = (price) => {
  return price ? Number(price).toLocaleString() : '0'
}

const getAuctionStatusText = (status) => {
  const statusMap = {
    0: '未开拍',
    1: '一拍中',
    2: '二拍中',
    3: '变卖中',
    4: '已结束'
  }
  return statusMap[status] || '未知'
}

const getAuctionStatusType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'warning',
    2: 'warning',
    3: 'danger',
    4: 'success'
  }
  return typeMap[status] || 'info'
}

const getPropertyTypeText = (type) => {
  const typeMap = {
    0: '低层',
    1: '中层',
    2: '高层'
  }
  return typeMap[type] || '未知'
}

const getDecorationText = (decoration) => {
  const decorationMap = {
    0: '毛坯',
    1: '简装',
    2: '精装'
  }
  return decorationMap[decoration] || '未知'
}
</script>

<style scoped>
.house-detail {
  padding: 20px;
}

.price {
  color: #e6a23c;
  font-weight: bold;
}

.discount-rate {
  color: #f56c6c;
  font-weight: bold;
}

.image-gallery {
  margin-top: 20px;
}

.image-gallery h3 {
  margin-bottom: 16px;
  color: #303133;
}
</style>
