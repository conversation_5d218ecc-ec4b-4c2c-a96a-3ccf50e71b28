import request from '@/utils/request'

// 用户管理API

/**
 * 创建用户
 * @param {Object} data 用户数据
 */
export function createUser(data) {
  return request({
    url: '/user/create',
    method: 'post',
    data
  })
}

/**
 * 根据手机号查询用户
 * @param {String} phoneNumber 手机号
 */
export function getUserByPhone(phoneNumber) {
  return request({
    url: '/user/phone',
    method: 'get',
    params: { phoneNumber }
  })
}

/**
 * 根据用户ID查询用户
 * @param {Number} id 用户ID
 */
export function getUserById(id) {
  return request({
    url: `/user/${id}`,
    method: 'get'
  })
}

/**
 * 更新用户信息
 * @param {Object} data 用户数据
 */
export function updateUser(data) {
  return request({
    url: '/user/update',
    method: 'put',
    data
  })
}

/**
 * 更新用户角色
 * @param {Number} userId 用户ID
 * @param {Number} role 角色值
 */
export function updateUserRole(userId, role) {
  return request({
    url: `/user/${userId}/role`,
    method: 'put',
    data: {
      userId: userId,
      role: role
    }
  })
}

/**
 * 添加收藏房源
 * @param {Number} userId 用户ID
 * @param {Number} houseId 房源ID
 */
export function addFavoriteHouse(userId, houseId) {
  return request({
    url: `/user/${userId}/favorite`,
    method: 'post',
    params: { houseId }
  })
}

/**
 * 移除收藏房源
 * @param {Number} userId 用户ID
 * @param {Number} houseId 房源ID
 */
export function removeFavoriteHouse(userId, houseId) {
  return request({
    url: `/user/${userId}/favorite`,
    method: 'delete',
    params: { houseId }
  })
}

/**
 * 获取用户收藏的房源列表
 * @param {Number} userId 用户ID
 */
export function getFavoriteHouses(userId) {
  return request({
    url: `/user/${userId}/favorites`,
    method: 'get'
  })
}

/**
 * 添加关注房源
 * @param {Number} userId 用户ID
 * @param {Number} houseId 房源ID
 */
export function addFollowedHouse(userId, houseId) {
  return request({
    url: `/user/${userId}/follow`,
    method: 'post',
    params: { houseId }
  })
}

/**
 * 移除关注房源
 * @param {Number} userId 用户ID
 * @param {Number} houseId 房源ID
 */
export function removeFollowedHouse(userId, houseId) {
  return request({
    url: `/user/${userId}/follow`,
    method: 'delete',
    params: { houseId }
  })
}

/**
 * 获取用户关注的房源列表
 * @param {Number} userId 用户ID
 */
export function getFollowedHouses(userId) {
  return request({
    url: `/user/${userId}/follows`,
    method: 'get'
  })
}

/**
 * 分页查询用户列表
 * @param {Object} queryParams 查询参数
 */
export function getUserList(queryParams) {
  return request({
    url: '/user/list',
    method: 'post',
    data: queryParams
  })
}

/**
 * 删除用户
 * @param {Number} userId 用户ID
 */
export function deleteUser(userId) {
  return request({
    url: `/user/${userId}`,
    method: 'delete'
  })
}

/**
 * 批量删除用户
 * @param {Array} userIds 用户ID列表
 */
export function deleteUsers(userIds) {
  return request({
    url: '/user/batch',
    method: 'delete',
    data: userIds
  })
}

/**
 * 获取用户总数
 */
export function getUserCount() {
  return request({
    url: '/user/count',
    method: 'get'
  })
}

/**
 * 根据时间段统计用户数量
 * @param {String} startTime 开始时间
 * @param {String} endTime 结束时间
 */
export function getUserCountByDateRange(startTime, endTime) {
  return request({
    url: '/user/count/daterange',
    method: 'get',
    params: { startTime, endTime }
  })
}
