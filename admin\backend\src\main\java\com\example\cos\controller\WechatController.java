package com.example.cos.controller;

import com.example.cos.common.Result;
import com.example.cos.common.ResultCode;
import com.example.cos.dto.WechatLoginDTO;
import com.example.cos.dto.WechatLoginResultDTO;
import com.example.cos.entity.WechatAccessToken;
import com.example.cos.entity.WechatSessionInfo;
import com.example.cos.service.WechatService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 微信登录控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Api(tags = "微信登录管理")
@RestController
@RequestMapping("/api/wechat")
@Validated
public class WechatController {

    private static final Logger logger = LoggerFactory.getLogger(WechatController.class);

    @Autowired
    private WechatService wechatService;

    /**
     * 微信小程序一键登录
     */
    @ApiOperation(value = "微信小程序一键登录", notes = "通过微信授权码实现一键登录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "登录成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/miniapp/login")
    public Result<WechatLoginResultDTO> miniappLogin(@RequestBody @Validated WechatLoginDTO loginDTO) {
        try {
            logger.info("微信小程序登录请求: {}", loginDTO);
            
            WechatLoginResultDTO result = wechatService.miniappLogin(loginDTO);
            
            logger.info("微信小程序登录成功: userId={}", result.getUser().getId());
            return Result.success("登录成功", result);
            
        } catch (IllegalArgumentException e) {
            logger.warn("微信登录参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("微信登录失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "登录失败: " + e.getMessage());
        }
    }

    /**
     * 获取微信会话信息
     */
    @ApiOperation(value = "获取微信会话信息", notes = "通过微信授权码获取用户会话信息")
    @ApiImplicitParam(name = "code", value = "微信授权码", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "获取成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/session-info")
    public Result<WechatSessionInfo> getSessionInfo(@RequestParam String code) {
        try {
            logger.info("获取微信会话信息: code={}", code);
            
            WechatSessionInfo sessionInfo = wechatService.getSessionInfo(code);
            
            if (!sessionInfo.isSuccess()) {
                return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取会话信息失败: " + sessionInfo.getErrmsg());
            }
            
            return Result.success("获取成功", sessionInfo);
            
        } catch (Exception e) {
            logger.error("获取微信会话信息失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取微信访问令牌
     */
    @ApiOperation(value = "获取微信访问令牌", notes = "获取微信API访问令牌")
    @ApiResponses({
            @ApiResponse(code = 200, message = "获取成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/access-token")
    public Result<WechatAccessToken> getAccessToken() {
        try {
            logger.info("获取微信访问令牌");
            
            WechatAccessToken accessToken = wechatService.getAccessToken();
            
            if (!accessToken.isSuccess()) {
                return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取访问令牌失败: " + accessToken.getErrmsg());
            }
            
            return Result.success("获取成功", accessToken);
            
        } catch (Exception e) {
            logger.error("获取微信访问令牌失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户手机号
     */
    @ApiOperation(value = "获取用户手机号", notes = "通过手机号授权码获取用户手机号")
    @ApiImplicitParam(name = "code", value = "手机号授权码", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "获取成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/phone-number")
    public Result<String> getPhoneNumber(@RequestParam String code) {
        try {
            logger.info("获取用户手机号: code={}", code);
            
            String phoneNumber = wechatService.getPhoneNumber(code);
            
            return Result.success("获取成功", phoneNumber);
            
        } catch (Exception e) {
            logger.error("获取用户手机号失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取失败: " + e.getMessage());
        }
    }

    /**
     * 刷新访问令牌
     */
    @ApiOperation(value = "刷新访问令牌", notes = "使用刷新令牌获取新的访问令牌")
    @ApiImplicitParam(name = "refreshToken", value = "刷新令牌", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "刷新成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/refresh-token")
    public Result<WechatLoginResultDTO> refreshToken(@RequestParam String refreshToken) {
        try {
            logger.info("刷新访问令牌: refreshToken={}", refreshToken);
            
            WechatLoginResultDTO result = wechatService.refreshToken(refreshToken);
            
            return Result.success("刷新成功", result);
            
        } catch (Exception e) {
            logger.error("刷新访问令牌失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "刷新失败: " + e.getMessage());
        }
    }

    /**
     * 验证访问令牌
     */
    @ApiOperation(value = "验证访问令牌", notes = "验证访问令牌是否有效")
    @ApiImplicitParam(name = "accessToken", value = "访问令牌", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "验证成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/validate-token")
    public Result<Boolean> validateToken(@RequestParam String accessToken) {
        try {
            logger.info("验证访问令牌: accessToken={}", accessToken);
            
            boolean isValid = wechatService.validateToken(accessToken);
            
            return Result.success("验证完成", isValid);
            
        } catch (Exception e) {
            logger.error("验证访问令牌失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "验证失败: " + e.getMessage());
        }
    }

    /**
     * 用户登出
     */
    @ApiOperation(value = "用户登出", notes = "用户登出，清除访问令牌")
    @ApiImplicitParam(name = "accessToken", value = "访问令牌", required = true, dataType = "string", paramType = "query")
    @ApiResponses({
            @ApiResponse(code = 200, message = "登出成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/logout")
    public Result<Boolean> logout(@RequestParam String accessToken) {
        try {
            logger.info("用户登出: accessToken={}", accessToken);
            
            boolean success = wechatService.logout(accessToken);
            
            return Result.success("登出成功", success);
            
        } catch (Exception e) {
            logger.error("用户登出失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "登出失败: " + e.getMessage());
        }
    }
}
