package com.example.cos.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * CDN配置信息DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "CDN配置信息")
public class CdnConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否启用CDN加速", example = "true")
    private Boolean enableCdn;

    @ApiModelProperty(value = "CDN加速域名", example = "your-cdn-domain.com")
    private String cdnDomain;

    @ApiModelProperty(value = "是否有有效的CDN配置", example = "true")
    private Boolean hasValidCdnConfig;

    @ApiModelProperty(value = "CDN状态描述", example = "CDN加速已启用")
    private String statusDescription;

    public CdnConfigDTO() {}

    public CdnConfigDTO(Boolean enableCdn, String cdnDomain) {
        this.enableCdn = enableCdn;
        this.cdnDomain = cdnDomain;
        this.hasValidCdnConfig = Boolean.TRUE.equals(enableCdn) && 
                                 cdnDomain != null && !cdnDomain.trim().isEmpty();
        this.statusDescription = generateStatusDescription();
    }

    public Boolean getEnableCdn() {
        return enableCdn;
    }

    public void setEnableCdn(Boolean enableCdn) {
        this.enableCdn = enableCdn;
        this.hasValidCdnConfig = Boolean.TRUE.equals(enableCdn) && 
                                 cdnDomain != null && !cdnDomain.trim().isEmpty();
        this.statusDescription = generateStatusDescription();
    }

    public String getCdnDomain() {
        return cdnDomain;
    }

    public void setCdnDomain(String cdnDomain) {
        this.cdnDomain = cdnDomain;
        this.hasValidCdnConfig = Boolean.TRUE.equals(enableCdn) && 
                                 cdnDomain != null && !cdnDomain.trim().isEmpty();
        this.statusDescription = generateStatusDescription();
    }

    public Boolean getHasValidCdnConfig() {
        return hasValidCdnConfig;
    }

    public void setHasValidCdnConfig(Boolean hasValidCdnConfig) {
        this.hasValidCdnConfig = hasValidCdnConfig;
    }

    public String getStatusDescription() {
        return statusDescription;
    }

    public void setStatusDescription(String statusDescription) {
        this.statusDescription = statusDescription;
    }

    /**
     * 生成状态描述
     */
    private String generateStatusDescription() {
        if (Boolean.TRUE.equals(enableCdn)) {
            if (cdnDomain != null && !cdnDomain.trim().isEmpty()) {
                return "CDN加速已启用，使用域名: " + cdnDomain;
            } else {
                return "CDN加速已启用，但未配置有效域名";
            }
        } else {
            return "CDN加速未启用，使用COS原始域名";
        }
    }

    @Override
    public String toString() {
        return "CdnConfigDTO{" +
                "enableCdn=" + enableCdn +
                ", cdnDomain='" + cdnDomain + '\'' +
                ", hasValidCdnConfig=" + hasValidCdnConfig +
                ", statusDescription='" + statusDescription + '\'' +
                '}';
    }
}
