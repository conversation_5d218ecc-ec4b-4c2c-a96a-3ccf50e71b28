"""
阿里资产（淘宝拍卖）爬虫
重新设计的爬取流程，支持房源列表页面爬取和AI数据解析
"""

import time
import random
import json
import os
import requests
import tempfile
import pickle
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

from config import Config
from api_client import APIClient
from image_watermark import add_watermark_to_image_data
from anti_detection import AntiDetectionManager


class AliAssetScraper:
    """阿里资产（淘宝拍卖）爬虫类"""
    
    def __init__(self, api_client: APIClient):
        self.api_client = api_client
        self.driver = None
        self.wait = None
        self.scraped_urls = set()  # 记录已爬取的URL，避免重复
        self.should_stop = False  # 停止标志
        self.anti_detection = None  # 防检测管理器

        # 配置信息
        self.base_url = "https://zc-paimai.taobao.com"
        self.login_url = "https://login.taobao.com/havanaone/login/login.htm?bizName=taobao&f=top&redirectURL=https%3A%2F%2Fzc-paimai.taobao.com%2Fwow%2Fpm%2Fdefault%2Fpc%2Ff14cdb%3FdisableNav%3DYES%26hPurpose%3D%255B%25223%2522%255D%26page%3D1%26spm%3Da2129.26987656.puimod-pc-search-navbar_5143927030.3%26pmid%3D7596161532_1650452471704%26pmtk%3D20140647.0.0.0.25287131.puimod-pc-search-navbar_5143927030.4%26path%3D27181431%252C25287131%252C26982532%252C26987401%252C26987656%26scm%3D20140647.julang.biying.brand"
        self.list_url_template = "https://zc-paimai.taobao.com/wow/pm/default/pc/8d97da?pmid=7596161532_1650452471704&pmtk=20140647.0.0.0.25287131.puimod-pc-search-navbar_5143927030.4&path=27181431,25287131&hPurpose=[%221%22]&page={}&spm=a2129.25287131.puimod-pc-search-navbar_5143927030.2&scm=20140647.julang.biying.brand&locationCodes=[%22500000%22]&statusOrders=[%220%22,%221%22]"

        # AI API配置（K2模型）
        self.ai_api_url = Config.K2_API_URL if hasattr(Config, 'K2_API_URL') else "https://api.k2.com/v1/chat/completions"
        self.ai_api_key = Config.K2_API_KEY if hasattr(Config, 'K2_API_KEY') else ""
        self.ai_model = Config.K2_MODEL if hasattr(Config, 'K2_MODEL') else "k2-chat"

        # Cookie文件路径
        self.cookie_file = "ali_asset_cookies.pkl"
        
        # XPath选择器
        self.selectors = {
            'house_list_items': '//*[@id="guid-9018433170"]/div/div/div',
            'house_link': './a',
            'house_title': '//*[@id="page"]/div[4]/div/div/h1',
            'basic_info_table': '//*[@id="J_COMPONENT_MAIN_BOTTOM"]/div[3]/div/table/tbody',
            'detail_desc_table': '//*[@id="J_desc"]/table',
            'notice_detail': '//*[@id="NoticeDetail"]',
            # 支持多张图片的XPath选择器
            'house_images_pattern': '//*[@id="J_ItemDetailContent"]/div[5]/div/div[{}]/img',
            'house_images_fallback': '//*[@id="J_desc"]/div/div[{}]/img',
            # 分页相关选择器
            'next_page_button': '//*[@id="guid-8322645760"]/div/div[2]',
            'pagination_container': '//*[@id="guid-8322645760"]',
            'current_page_indicator': '//*[@id="guid-8322645760"]//span[contains(@class, "current")]'
        }
    
    def setup_driver(self, auto_login: bool = True):
        """设置Chrome浏览器驱动并尝试自动登录"""
        methods = [
            self._init_driver_local,
            self._init_driver_with_manager,
            self._init_driver_system_chrome
        ]

        for i, method in enumerate(methods, 1):
            try:
                self.log(f"尝试方法 {i}: {method.__name__}")
                if method():
                    # 浏览器初始化成功后，尝试自动登录
                    if auto_login:
                        login_success = self.auto_login()
                        if not login_success:
                            self.log("⚠️ 自动登录失败，但浏览器保持打开状态供手动登录")
                    return True
            except Exception as e:
                self.log(f"方法 {i} 失败: {str(e)}", "WARNING")
                continue

        self.log("所有初始化方法都失败了", "ERROR")
        return False

    def auto_login(self):
        """自动登录流程：先尝试cookie登录，失败则提示手动登录"""
        try:
            self.log("开始自动登录流程...")

            # 尝试使用cookies登录
            if self.login_with_cookies():
                self.log("✅ 自动登录成功")
                return True
            else:
                self.log("⚠️ cookie登录失败，需要手动登录")
                # 不要关闭浏览器，给用户手动登录的机会
                if self.manual_login():
                    return True
                else:
                    return False

        except Exception as e:
            self.log(f"❌ 自动登录失败: {str(e)}", "ERROR")
            return False

    def _init_driver_local(self) -> bool:
        """使用本地ChromeDriver初始化"""
        self.log("正在尝试使用本地ChromeDriver...")

        # 获取Chrome选项
        options = self._get_chrome_options()

        # 检查本地ChromeDriver路径
        local_paths = [
            "./chromedriver.exe",  # 当前目录
            "chromedriver.exe",    # 当前目录
            "chromedriver",        # Linux/Mac
            "./chrome/chromedriver.exe",  # chrome子目录
        ]

        driver_path = None
        for path in local_paths:
            if os.path.exists(path):
                driver_path = path
                self.log(f"找到本地ChromeDriver: {path}")
                break

        if driver_path:
            service = Service(driver_path)
            self.driver = webdriver.Chrome(service=service, options=options)
        else:
            # 尝试使用系统PATH中的chromedriver
            self.driver = webdriver.Chrome(options=options)

        return self._configure_driver()

    def _init_driver_with_manager(self) -> bool:
        """使用WebDriverManager初始化"""
        self.log("正在使用WebDriverManager下载ChromeDriver...")

        try:
            # 获取Chrome选项
            options = self._get_chrome_options()

            # 创建WebDriver服务
            service = Service(ChromeDriverManager().install())

            # 创建WebDriver实例
            self.driver = webdriver.Chrome(service=service, options=options)

            return self._configure_driver()
        except Exception as e:
            if "Could not reach host" in str(e) or "offline" in str(e).lower():
                self.log("网络连接问题，跳过WebDriverManager方法", "WARNING")
                raise Exception("网络连接问题")
            else:
                raise e

    def _init_driver_system_chrome(self) -> bool:
        """使用系统Chrome浏览器初始化"""
        self.log("正在尝试使用系统Chrome浏览器...")

        # 获取Chrome选项
        options = self._get_chrome_options()

        # 尝试指定Chrome二进制路径
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(
                os.environ.get('USERNAME', 'User')
            )
        ]

        for chrome_path in chrome_paths:
            if os.path.exists(chrome_path):
                options.binary_location = chrome_path
                self.log(f"找到Chrome浏览器: {chrome_path}")
                break

        self.driver = webdriver.Chrome(options=options)

        return self._configure_driver()

    def _get_chrome_options(self):
        """获取增强的Chrome选项配置"""
        chrome_options = Options()

        # 用户数据目录 - 保持登录状态
        user_data_dir = os.path.join(os.getcwd(), "chrome_user_data")
        if not os.path.exists(user_data_dir):
            os.makedirs(user_data_dir)
        chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
        chrome_options.add_argument('--profile-directory=Default')

        # 随机User-Agent
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        chrome_options.add_argument(f'--user-agent={random.choice(user_agents)}')

        # 核心反检测参数
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 高级反检测参数
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')
        chrome_options.add_argument('--disable-default-apps')
        chrome_options.add_argument('--disable-sync')
        chrome_options.add_argument('--no-first-run')
        chrome_options.add_argument('--no-default-browser-check')
        chrome_options.add_argument('--disable-logging')
        chrome_options.add_argument('--disable-plugins-discovery')
        chrome_options.add_argument('--disable-preconnect')
        chrome_options.add_argument('--disable-background-timer-throttling')
        chrome_options.add_argument('--disable-backgrounding-occluded-windows')
        chrome_options.add_argument('--disable-renderer-backgrounding')
        chrome_options.add_argument('--disable-ipc-flooding-protection')

        # 隐藏自动化特征
        chrome_options.add_experimental_option("prefs", {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1,
            "profile.default_content_setting_values.geolocation": 2
        })

        # 随机窗口大小
        window_sizes = [(1366, 768), (1920, 1080), (1440, 900), (1536, 864), (1600, 900)]
        width, height = random.choice(window_sizes)
        chrome_options.add_argument(f'--window-size={width},{height}')

        # 网络和性能优化
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--ignore-certificate-errors')
        chrome_options.add_argument('--ignore-ssl-errors')
        chrome_options.add_argument('--ignore-certificate-errors-spki-list')

        return chrome_options

    def _configure_driver(self) -> bool:
        """配置WebDriver并初始化防检测管理器"""
        try:
            # 配置等待和超时
            self.driver.implicitly_wait(10)
            self.driver.set_page_load_timeout(30)
            self.driver.set_script_timeout(30)

            # 确保浏览器全屏显示
            try:
                self.driver.maximize_window()
                self.log("浏览器窗口已最大化")
            except Exception as e:
                self.log(f"设置全屏失败，但继续执行: {str(e)}", "WARNING")

            # 创建显式等待对象
            self.wait = WebDriverWait(self.driver, 10)

            # 初始化防检测管理器
            try:
                self.anti_detection = AntiDetectionManager(self.driver)
                self.log("✅ 防检测管理器初始化成功")
            except Exception as e:
                self.log(f"⚠️ 防检测管理器初始化失败: {str(e)}", "WARNING")

            # 加载增强版防检测脚本
            try:
                enhanced_script_path = os.path.join(os.getcwd(), "enhanced_anti_detection.js")
                if os.path.exists(enhanced_script_path):
                    with open(enhanced_script_path, 'r', encoding='utf-8') as f:
                        enhanced_script = f.read()
                    self.driver.execute_script(enhanced_script)
                    self.log("✅ 增强版防检测脚本加载成功")
            except Exception as e:
                self.log(f"⚠️ 增强版防检测脚本加载失败: {str(e)}", "WARNING")

            # 设置请求拦截（如果支持）
            try:
                if self.anti_detection:
                    self.anti_detection.setup_request_interception()
            except Exception as e:
                self.log(f"⚠️ 请求拦截设置失败: {str(e)}", "WARNING")

            # 随机化视口
            try:
                if self.anti_detection:
                    self.anti_detection.randomize_viewport()
            except Exception as e:
                self.log(f"⚠️ 视口随机化失败: {str(e)}", "WARNING")

            # 测试浏览器是否正常工作
            self.driver.get("data:text/html,<html><body><h1>Test</h1></body></html>")

            self.log("✅ Chrome浏览器驱动设置成功")
            return True

        except Exception as e:
            self.log(f"浏览器配置失败: {str(e)}", "ERROR")
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None
            return False
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] [AliAssetScraper] {message}")
    
    def random_delay(self, min_seconds: float = 1.0, max_seconds: float = 3.0):
        """智能随机延时（增强防检测版）"""
        # 基础延迟
        base_delay = random.uniform(min_seconds, max_seconds)

        # 根据请求频率动态调整延迟
        if self.anti_detection:
            # 如果请求过于频繁，增加延迟
            if self.anti_detection.request_count > 0:
                frequency_factor = min(self.anti_detection.request_count / 10, 2.0)
                base_delay *= (1 + frequency_factor * 0.5)

        # 添加随机波动
        variation = random.uniform(-0.3, 0.7)
        final_delay = max(0.5, base_delay + variation)

        self.log(f"⏰ 智能延迟: {final_delay:.1f} 秒")
        time.sleep(final_delay)
    
    def navigate_to_list_page(self, page_num: int = 1) -> bool:
        """导航到房源列表页面（增强防检测版）"""
        try:
            # 请求频率控制
            if self.anti_detection:
                self.anti_detection.request_frequency_control()

            if page_num == 1:
                # 第一页使用基础URL（不带页码参数）
                url = self.base_url + "/wow/pm/default/pc/8d97da?pmid=7596161532_1650452471704&pmtk=20140647.0.0.0.25287131.puimod-pc-search-navbar_5143927030.4&path=27181431,25287131&hPurpose=[%221%22]&spm=a2129.25287131.puimod-pc-search-navbar_5143927030.2&scm=20140647.julang.biying.brand&locationCodes=[%22500000%22]&statusOrders=[%220%22,%221%22]"
            else:
                # 其他页面使用模板URL
                url = self.list_url_template.format(page_num)

            self.log(f"正在访问第{page_num}页房源列表: {url}")

            # 使用防检测管理器的网络错误处理
            def navigate_operation():
                self.driver.get(url)
                return True

            if self.anti_detection:
                self.anti_detection.network_error_handling(navigate_operation)
            else:
                navigate_operation()

            # 模拟人类行为
            if self.anti_detection:
                self.anti_detection.simulate_human_behavior()

            # 随机延迟
            self.random_delay(3, 6)

            # 等待页面加载完成
            try:
                self.wait.until(EC.presence_of_element_located((By.XPATH, self.selectors['house_list_items'])))
            except TimeoutException:
                self.log("页面加载超时，但继续尝试爬取", "WARNING")

                # 检测验证码
                if self.anti_detection and self.anti_detection.detect_and_handle_captcha():
                    # 重新等待页面加载
                    try:
                        self.wait.until(EC.presence_of_element_located((By.XPATH, self.selectors['house_list_items'])))
                    except TimeoutException:
                        pass

            # 模拟页面交互
            if self.anti_detection:
                self.anti_detection.simulate_page_interaction()

            self.log(f"✅ 成功访问第{page_num}页房源列表")
            return True

        except TimeoutException:
            self.log(f"❌ 访问第{page_num}页房源列表超时", "ERROR")
            return False
        except Exception as e:
            self.log(f"❌ 访问第{page_num}页房源列表失败: {str(e)}", "ERROR")
            return False
    
    def check_login_required(self) -> bool:
        """检查是否需要登录"""
        try:
            # 检查是否存在登录相关元素
            login_indicators = [
                "登录",
                "login",
                "请先登录",
                "未登录"
            ]

            page_source = self.driver.page_source.lower()
            for indicator in login_indicators:
                if indicator.lower() in page_source:
                    return True

            return False

        except Exception as e:
            self.log(f"检查登录状态失败: {str(e)}", "WARNING")
            return False

    def is_first_time_use(self) -> bool:
        """检查是否是首次使用（没有保存的cookies）"""
        return not os.path.exists(self.cookie_file)
    
    def extract_house_links(self) -> List[str]:
        """从当前页面提取所有房源详情页链接"""
        try:
            self.log("正在提取房源详情页链接...")
            
            house_links = []
            
            # 查找所有房源项
            house_items = self.driver.find_elements(By.XPATH, self.selectors['house_list_items'])
            self.log(f"找到 {len(house_items)} 个房源项")
            
            for i, item in enumerate(house_items, 1):
                try:
                    # 在每个房源项中查找链接
                    link_element = item.find_element(By.XPATH, self.selectors['house_link'])
                    href = link_element.get_attribute('href')
                    
                    if href:
                        # 转换为绝对URL
                        absolute_url = urljoin(self.base_url, href)
                        
                        # 避免重复
                        if absolute_url not in self.scraped_urls:
                            house_links.append(absolute_url)
                            self.scraped_urls.add(absolute_url)
                            self.log(f"✅ 第{i}个房源链接: {absolute_url}")
                        else:
                            self.log(f"⚠️ 第{i}个房源链接已存在，跳过: {absolute_url}")
                    
                except NoSuchElementException:
                    self.log(f"⚠️ 第{i}个房源项未找到链接", "WARNING")
                    continue
                except Exception as e:
                    self.log(f"⚠️ 提取第{i}个房源链接失败: {str(e)}", "WARNING")
                    continue
            
            self.log(f"✅ 成功提取 {len(house_links)} 个房源链接")
            return house_links
            
        except Exception as e:
            self.log(f"❌ 提取房源链接失败: {str(e)}", "ERROR")
            return []
    
    def scrape_house_detail(self, house_url: str) -> Optional[Dict[str, Any]]:
        """爬取单个房源的详细信息（增强防检测版）"""
        try:
            self.log(f"正在爬取房源详情: {house_url}")

            # 请求频率控制
            if self.anti_detection:
                self.anti_detection.request_frequency_control()

            # 使用重试机制访问房源详情页
            def navigate_to_detail():
                self.driver.get(house_url)
                return True

            if self.anti_detection:
                self.anti_detection.retry_operation(navigate_to_detail, max_retries=3)
            else:
                navigate_to_detail()

            # 模拟人类行为
            if self.anti_detection:
                self.anti_detection.simulate_human_behavior()

            # 随机延迟
            self.random_delay(3, 6)

            # 等待页面加载
            try:
                self.wait.until(EC.presence_of_element_located((By.XPATH, self.selectors['house_title'])))
            except TimeoutException:
                self.log("房源详情页加载超时，尝试检测验证码", "WARNING")

                # 检测并处理验证码
                if self.anti_detection and self.anti_detection.detect_and_handle_captcha():
                    # 重新等待页面加载
                    try:
                        self.wait.until(EC.presence_of_element_located((By.XPATH, self.selectors['house_title'])))
                    except TimeoutException:
                        self.log("验证码处理后仍然超时", "WARNING")

            house_data = {
                'originalUrl': house_url,
                'extractedText': '',
                'imageUrls': ''
            }

            # 模拟页面交互
            if self.anti_detection:
                self.anti_detection.simulate_page_interaction()

            # 提取文本信息
            extracted_text = self.extract_text_content()
            house_data['extractedText'] = extracted_text

            # 随机延迟
            self.random_delay(2, 4)

            # 提取房源图片
            image_urls = self.extract_house_images()
            house_data['imageUrls'] = ','.join(image_urls) if image_urls else ''

            self.log(f"✅ 成功爬取房源详情，文本长度: {len(extracted_text)}, 图片数量: {len(image_urls)}")
            return house_data

        except Exception as e:
            self.log(f"❌ 爬取房源详情失败: {str(e)}", "ERROR")

            # 网络错误处理
            if self.anti_detection:
                try:
                    return self.anti_detection.network_error_handling(self.scrape_house_detail, house_url)
                except:
                    pass

            return None

    def extract_text_content(self) -> str:
        """提取页面中的所有文本内容"""
        try:
            self.log("正在提取页面文本内容...")

            all_text = []

            # 1. 提取房源标题
            try:
                title_element = self.driver.find_element(By.XPATH, self.selectors['house_title'])
                title_text = self.get_element_full_text(title_element)
                if title_text:
                    all_text.append(f"房源标题: {title_text}")
                    self.log(f"✅ 提取标题: {title_text[:50]}...")
            except NoSuchElementException:
                self.log("⚠️ 未找到房源标题", "WARNING")

            # 2. 提取基本信息表格
            try:
                basic_info_element = self.driver.find_element(By.XPATH, self.selectors['basic_info_table'])
                basic_info_text = self.get_element_full_text(basic_info_element)
                if basic_info_text:
                    all_text.append(f"基本信息: {basic_info_text}")
                    self.log(f"✅ 提取基本信息: {len(basic_info_text)} 字符")
            except NoSuchElementException:
                self.log("⚠️ 未找到基本信息表格", "WARNING")

            # 3. 提取详细描述表格
            try:
                detail_desc_element = self.driver.find_element(By.XPATH, self.selectors['detail_desc_table'])
                detail_desc_text = self.get_element_full_text(detail_desc_element)
                if detail_desc_text:
                    all_text.append(f"详细描述: {detail_desc_text}")
                    self.log(f"✅ 提取详细描述: {len(detail_desc_text)} 字符")
            except NoSuchElementException:
                self.log("⚠️ 未找到详细描述表格", "WARNING")

            # 4. 提取公告详情
            try:
                notice_element = self.driver.find_element(By.XPATH, self.selectors['notice_detail'])
                notice_text = self.get_element_full_text(notice_element)
                if notice_text:
                    all_text.append(f"公告详情: {notice_text}")
                    self.log(f"✅ 提取公告详情: {len(notice_text)} 字符")
            except NoSuchElementException:
                self.log("⚠️ 未找到公告详情", "WARNING")

            # 合并所有文本
            combined_text = "\n\n".join(all_text)
            self.log(f"✅ 文本提取完成，总长度: {len(combined_text)} 字符")

            return combined_text

        except Exception as e:
            self.log(f"❌ 提取文本内容失败: {str(e)}", "ERROR")
            return ""

    def get_element_full_text(self, element) -> str:
        """获取元素及其所有子元素的完整文本内容"""
        try:
            # 获取元素的所有文本内容，包括子元素
            text_content = element.get_attribute('textContent') or element.text

            # 清理文本：去除多余空白字符
            if text_content:
                lines = [line.strip() for line in text_content.split('\n') if line.strip()]
                return ' '.join(lines)

            return ""

        except Exception as e:
            self.log(f"获取元素文本失败: {str(e)}", "WARNING")
            return ""

    def extract_house_images(self) -> List[str]:
        """提取房源图片并上传到服务器"""
        try:
            self.log("正在提取房源图片...")

            uploaded_urls = []

            # 尝试提取多张房源图片
            for i in range(1, 21):  # 最多尝试20张图片
                try:
                    # 首先尝试主要的XPath模式
                    img_xpath = self.selectors['house_images_pattern'].format(i)
                    img_element = self.driver.find_element(By.XPATH, img_xpath)
                    img_src = img_element.get_attribute('src')

                    if img_src and img_src.startswith('http'):
                        # 下载并上传图片
                        uploaded_url = self.download_and_upload_image(img_src, f"ali_house_image_{i}")
                        if uploaded_url:
                            uploaded_urls.append(uploaded_url)
                            self.log(f"✅ 第{i}张房源图片上传成功（主要路径）")
                        else:
                            self.log(f"⚠️ 第{i}张房源图片上传失败", "WARNING")

                except NoSuchElementException:
                    # 尝试备用的XPath选择器
                    try:
                        img_xpath_fallback = self.selectors['house_images_fallback'].format(i)
                        img_element = self.driver.find_element(By.XPATH, img_xpath_fallback)
                        img_src = img_element.get_attribute('src')

                        if img_src and img_src.startswith('http'):
                            # 下载并上传图片
                            uploaded_url = self.download_and_upload_image(img_src, f"ali_house_image_{i}")
                            if uploaded_url:
                                uploaded_urls.append(uploaded_url)
                                self.log(f"✅ 第{i}张房源图片上传成功（备用路径）")
                            else:
                                self.log(f"⚠️ 第{i}张房源图片上传失败", "WARNING")

                    except NoSuchElementException:
                        # 两个路径都没找到，说明没有更多图片了
                        if i == 1:
                            self.log("⚠️ 未找到任何房源图片", "WARNING")
                        break
                    except Exception as e:
                        self.log(f"⚠️ 备用路径处理第{i}张图片失败: {str(e)}", "WARNING")
                        continue

                except Exception as e:
                    self.log(f"⚠️ 主要路径处理第{i}张图片失败: {str(e)}", "WARNING")
                    continue

            self.log(f"✅ 共上传 {len(uploaded_urls)} 张房源图片")
            return uploaded_urls

        except Exception as e:
            self.log(f"❌ 提取房源图片失败: {str(e)}", "ERROR")
            return []

    def download_and_upload_image(self, img_url: str, filename_prefix: str) -> str:
        """下载图片并上传到服务器（带水印）"""
        try:
            # 下载图片
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer': self.base_url
            }

            response = requests.get(img_url, headers=headers, timeout=30)
            response.raise_for_status()

            # 添加水印
            try:
                watermarked_data = add_watermark_to_image_data(response.content)
                self.log(f"✅ 成功为图片添加水印: {filename_prefix}")
            except Exception as e:
                self.log(f"添加水印失败，使用原图: {str(e)}", "WARNING")
                watermarked_data = response.content

            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
                temp_file.write(watermarked_data)
                temp_file_path = temp_file.name

            try:
                # 上传到服务器
                upload_url = f"{self.api_client.base_url}/api/cos/upload"

                with open(temp_file_path, 'rb') as f:
                    files = {'file': (f'{filename_prefix}.jpg', f, 'image/jpeg')}

                    # 为文件上传创建专门的headers
                    upload_headers = {}
                    for key, value in self.api_client.session.headers.items():
                        if key.lower() != 'content-type':
                            upload_headers[key] = value

                    upload_response = requests.post(
                        upload_url,
                        files=files,
                        headers=upload_headers,
                        timeout=30
                    )

                if upload_response.status_code == 200:
                    result = upload_response.json()
                    if result.get('code') == 200 and result.get('data'):
                        file_url = result['data'].get('fileUrl', '')
                        return file_url
                    else:
                        self.log(f"图片上传响应错误: {result}", "ERROR")
                        return ""
                else:
                    self.log(f"图片上传请求失败: {upload_response.status_code}", "ERROR")
                    return ""

            finally:
                # 删除临时文件
                try:
                    os.remove(temp_file_path)
                except:
                    pass

        except Exception as e:
            self.log(f"下载并上传图片失败: {str(e)}", "ERROR")
            return ""

    def parse_with_ai(self, extracted_text: str, image_urls: str, original_url: str) -> Optional[Dict[str, Any]]:
        """使用AI解析提取的文本内容，生成房源数据"""
        try:
            self.log("正在使用AI解析房源数据...")

            if not self.ai_api_key:
                self.log("❌ AI API密钥未配置，跳过AI解析", "ERROR")
                return None

            # 构建AI提示词
            prompt = self.build_ai_prompt(extracted_text, image_urls, original_url)

            # 调用AI API
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.ai_api_key}'
            }

            payload = {
                "model": self.ai_model,
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的房地产数据分析师，擅长从拍卖网站的文本信息中提取结构化的房源数据。请严格按照要求的JSON格式返回数据，不要添加任何额外的解释或格式。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1,
                "max_tokens": 2000
            }

            response = requests.post(
                self.ai_api_url,
                headers=headers,
                json=payload,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                ai_content = result['choices'][0]['message']['content']

                # 尝试解析AI返回的JSON
                try:
                    # 提取JSON部分（去除可能的额外文本）
                    json_start = ai_content.find('{')
                    json_end = ai_content.rfind('}') + 1

                    if json_start >= 0 and json_end > json_start:
                        json_str = ai_content[json_start:json_end]
                        house_data = json.loads(json_str)

                        # 添加图片URL和原始URL
                        house_data['imageUrls'] = image_urls
                        house_data['originalUrl'] = original_url

                        self.log("✅ AI解析成功")
                        return house_data
                    else:
                        self.log("❌ AI返回内容中未找到有效JSON", "ERROR")
                        return None

                except json.JSONDecodeError as e:
                    self.log(f"❌ AI返回的JSON格式错误: {str(e)}", "ERROR")
                    self.log(f"AI返回内容: {ai_content[:500]}...")
                    return None
            else:
                self.log(f"❌ AI API调用失败: {response.status_code} - {response.text}", "ERROR")
                return None

        except Exception as e:
            self.log(f"❌ AI解析失败: {str(e)}", "ERROR")
            return None

    def build_ai_prompt(self, extracted_text: str, image_urls: str, original_url: str) -> str:
        """构建AI解析的提示词"""
        prompt = f"""
请分析以下淘宝拍卖房源信息，并按照指定的JSON格式返回结构化数据。

原始URL: {original_url}
图片URLs: {image_urls}

房源信息文本:
{extracted_text}

请根据以上信息，生成符合以下API格式的JSON数据：

{{
  "auctionStatus": 1,                   // 拍卖状态：0-未开拍，1-一拍，2-二拍，3-变卖，4-已结束（从标题中识别"一拍"、"二拍"、"变卖"等关键词）
  "auctionTimes": 1,                    // 拍卖次数：1-一拍，2-二拍，3-变卖
  "buildingArea": 120.5,                // 建筑面积（平方米）
  "communityName": "小区名称",           // 小区名称
  "constructionYear": 2010,             // 建造年份
  "decoration": 2,                      // 装修情况：0-毛坯，1-简装，2-精装，3-豪装
  "deposit": 120000,                    // 保证金（元）
  "endTime": "2025-01-22 10:00:00",     // 结束时间，格式：YYYY-MM-DD HH:mm:ss（从"将于2025年07月31日10：00时起至2025年08月01日10：00时止"等格式中提取，如果找不到则设为startTime后7天）
  "evaluationPrice": 1500000,           // 评估价（元）
  "floor": "15/30",                     // 楼层信息，格式：当前楼层/总楼层
  "houseCategory": 0,                   // 房源类别：0-住宅，1-商业，2-工业，3-其他
  "houseType": "3室2厅1卫",             // 房型
  "isSpecial": false,                   // 是否特殊房屋，默认false
  "isSelected": false,                  // 是否精选，默认false
  "latitude": 39.916527,                // 纬度（如果有地址信息请推断大概位置）
  "longitude": 116.397128,              // 经度（如果有地址信息请推断大概位置）
  "priceIncrement": 10000,              // 加价幅度（元）
  "propertyType": 1,                    // 产权类型：1-商品房，2-经济适用房（注意：值必须≤2，如果AI推断为其他值请映射到1或2）
  "stairsType": 1,                      // 楼梯类型：1-电梯房，0-楼梯房（注意：值必须≤1，如果是楼梯房请设为0，电梯房设为1）
  "startTime": "2025-01-15 10:00:00",   // 开始时间，格式：YYYY-MM-DD HH:mm:ss（从"将于2025年07月31日10：00时起至2025年08月01日10：00时止"等格式中提取，如果找不到则设为当前日期10:00:00）
  "startingPrice": 1200000,             // 起拍价（元）
  "tags": "学区房,地铁房,精装修",        // 标签，用逗号分隔
  "title": "房源标题"                    // 房源标题
}}

重要注意事项：
1. **拍卖状态识别**：从房源标题中识别拍卖状态，如"一拍"对应1，"二拍"对应2，"变卖"对应3，"已结束"对应4
2. **时间提取增强**：
   - 支持"将于2025年07月31日10：00时起至2025年08月01日10：00时止"格式
   - 支持"2025-07-31 10:00:00 至 2025-08-01 10:00:00"格式
   - 如果找不到具体时间，startTime设为当前日期10:00:00，endTime设为7天后10:00:00
   - **时间字段不能为null**，必须提供有效的时间值
3. **propertyType限制**：值必须≤2，如果推断为3或4，请映射到1（商品房）或2（经济适用房）
4. **stairsType限制**：值必须≤1，电梯房设为1，楼梯房设为0，如果推断为2或更大值请映射到1
5. **必填字段**：isSpecial和isSelected必须包含，默认值为false；startTime和endTime必须有值，不能为null
6. **移除自动计算字段**：不要包含bargainSpace、auctionCycle、startingUnitPrice、marketUnitPrice、discountRate等字段
7. **未知信息处理**：对于无法从文本中获取的信息，返回null值，但startTime和endTime除外
8. **时间格式**：必须严格按照 YYYY-MM-DD HH:mm:ss 格式，如"2025-08-01 10:00:00"
9. **数值类型**：确保数字字段是纯数字，不包含单位或其他文字
10. **JSON格式**：只返回JSON数据，不要添加任何解释文字，确保格式正确可解析
"""
        return prompt

    def create_house_record(self, house_data: Dict[str, Any]) -> bool:
        """调用API创建房源记录"""
        try:
            self.log("正在创建房源记录...")

            # 数据处理和校验
            processed_data = self.process_house_data(house_data)

            # 打印AI返回的数据和房源图片URL（用于调试）
            self.log("=== AI解析返回的数据 ===")
            self.log(f"房源标题: {processed_data.get('title', '未知')}")
            self.log(f"房源类型: {processed_data.get('houseType', '未知')}")
            self.log(f"拍卖状态: {processed_data.get('auctionStatus', '未知')}")
            self.log(f"起拍价: {processed_data.get('startingPrice', '未知')}")
            self.log(f"评估价: {processed_data.get('evaluationPrice', '未知')}")
            self.log(f"房源面积: {processed_data.get('buildingArea', '未知')}")
            self.log(f"产权类型: {processed_data.get('propertyType', '未知')}")
            self.log(f"是否特殊房屋: {processed_data.get('isSpecial', '未知')}")
            self.log(f"是否精选: {processed_data.get('isSelected', '未知')}")
            self.log(f"拍卖结束时间: {processed_data.get('endTime', '未知')}")
            self.log(f"原始URL: {processed_data.get('originalUrl', '未知')}")

            # 打印房源图片URL
            image_urls = processed_data.get('imageUrls', '')
            if image_urls:
                self.log(f"房源图片URL: {image_urls}")
            else:
                self.log("房源图片URL: 无")

            # 调用创建房源API
            response = self.api_client.post('/api/house/create', processed_data)

            if response and response.get('code') == 200:
                house_id = response.get('data', {}).get('id', 'unknown')
                self.log(f"✅ 房源记录创建成功，ID: {house_id}")
                return True
            else:
                error_msg = response.get('message', '未知错误') if response else '请求失败'
                self.log(f"❌ 房源记录创建失败: {error_msg}", "ERROR")

                # 失败时也要打印AI返回的数据和图片URL
                self.log("=== 创建失败时的调试信息 ===", "ERROR")
                self.log(f"AI返回的完整数据: {house_data}", "ERROR")
                self.log(f"处理后的数据: {processed_data}", "ERROR")
                return False

        except Exception as e:
            self.log(f"❌ 创建房源记录失败: {str(e)}", "ERROR")

            # 异常时也要打印AI返回的数据和图片URL
            self.log("=== 异常时的调试信息 ===", "ERROR")
            self.log(f"AI返回的完整数据: {house_data}", "ERROR")
            return False

    def process_house_data(self, house_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理和校验房源数据，确保符合API要求"""
        processed_data = house_data.copy()

        # 1. 修复propertyType字段：确保值≤2
        property_type = processed_data.get('propertyType', 1)
        if isinstance(property_type, (int, float)) and property_type > 2:
            processed_data['propertyType'] = 1  # 默认映射到商品房
            self.log(f"⚠️ propertyType值{property_type}超出范围，已映射到1（商品房）", "WARNING")
        elif not isinstance(property_type, (int, float)):
            processed_data['propertyType'] = 1
            self.log(f"⚠️ propertyType值{property_type}不是数字，已设置为1（商品房）", "WARNING")

        # 1.1 修复stairsType字段：确保值≤1
        stairs_type = processed_data.get('stairsType', 1)
        if isinstance(stairs_type, (int, float)) and stairs_type > 1:
            processed_data['stairsType'] = 1  # 默认映射到电梯房
            self.log(f"⚠️ stairsType值{stairs_type}超出范围，已映射到1（电梯房）", "WARNING")
        elif not isinstance(stairs_type, (int, float)):
            processed_data['stairsType'] = 1
            self.log(f"⚠️ stairsType值{stairs_type}不是数字，已设置为1（电梯房）", "WARNING")

        # 2. 添加isSpecial字段：是否特殊房屋（API期望整数类型：0-否，1-是）
        if 'isSpecial' not in processed_data:
            processed_data['isSpecial'] = 0
            self.log("✅ 添加isSpecial字段，默认值：0（非特殊房屋）")

        # 3. 添加isSelected字段：是否精选（API期望整数类型：0-否，1-是）
        if 'isSelected' not in processed_data:
            processed_data['isSelected'] = 0
            self.log("✅ 添加isSelected字段，默认值：0（非精选）")

        # 4. 移除自动计算字段（数据库会自动计算）
        auto_calc_fields = ['bargainSpace', 'auctionCycle', 'startingUnitPrice', 'marketUnitPrice', 'discountRate']
        for field in auto_calc_fields:
            if field in processed_data:
                del processed_data[field]
                self.log(f"✅ 移除自动计算字段：{field}")

        # 5. 时间字段校验和默认值设置
        # 确保时间字段不为空
        time_fields = ['startTime', 'endTime']
        for field in time_fields:
            if not processed_data.get(field):
                if field == 'startTime':
                    # 设置默认开始时间为当前时间
                    from datetime import datetime
                    processed_data[field] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self.log(f"⚠️ {field}为空，设置为当前时间: {processed_data[field]}", "WARNING")
                elif field == 'endTime':
                    # 设置默认结束时间为开始时间后7天
                    from datetime import datetime, timedelta
                    if processed_data.get('startTime'):
                        try:
                            start_time = datetime.strptime(processed_data['startTime'], "%Y-%m-%d %H:%M:%S")
                            end_time = start_time + timedelta(days=7)
                            processed_data[field] = end_time.strftime("%Y-%m-%d %H:%M:%S")
                            self.log(f"⚠️ {field}为空，设置为开始时间后7天: {processed_data[field]}", "WARNING")
                        except:
                            processed_data[field] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            self.log(f"⚠️ {field}为空，设置为当前时间: {processed_data[field]}", "WARNING")
                    else:
                        processed_data[field] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        self.log(f"⚠️ {field}为空，设置为当前时间: {processed_data[field]}", "WARNING")

        # 6. 数据类型校验和转换
        # 确保数值字段是正确的类型
        numeric_fields = ['auctionStatus', 'auctionTimes', 'buildingArea', 'constructionYear',
                         'decoration', 'deposit', 'evaluationPrice', 'houseCategory',
                         'latitude', 'longitude', 'priceIncrement', 'propertyType',
                         'stairsType', 'startingPrice']

        for field in numeric_fields:
            if field in processed_data and processed_data[field] is not None:
                try:
                    if field in ['latitude', 'longitude', 'buildingArea']:
                        processed_data[field] = float(processed_data[field])
                    else:
                        processed_data[field] = int(processed_data[field])
                except (ValueError, TypeError):
                    self.log(f"⚠️ 字段{field}值{processed_data[field]}无法转换为数字，设置为null", "WARNING")
                    processed_data[field] = None

        # 6. 布尔字段转换为整数（API期望整数类型：0-否，1-是）
        boolean_to_int_fields = ['isSpecial', 'isSelected']
        for field in boolean_to_int_fields:
            if field in processed_data:
                if isinstance(processed_data[field], str):
                    # 字符串转换：'true', '1', 'yes' -> 1，其他 -> 0
                    processed_data[field] = 1 if processed_data[field].lower() in ['true', '1', 'yes'] else 0
                elif isinstance(processed_data[field], bool):
                    # 布尔值转换：True -> 1, False -> 0
                    processed_data[field] = 1 if processed_data[field] else 0
                elif not isinstance(processed_data[field], int):
                    # 其他类型转换为整数
                    processed_data[field] = 1 if processed_data[field] else 0

        return processed_data

    def save_cookies(self):
        """保存当前浏览器的cookies到文件"""
        try:
            if self.driver:
                cookies = self.driver.get_cookies()
                with open(self.cookie_file, 'wb') as f:
                    pickle.dump(cookies, f)
                self.log(f"✅ 成功保存cookies到文件: {self.cookie_file}")
                return True
        except Exception as e:
            self.log(f"❌ 保存cookies失败: {str(e)}", "ERROR")
            return False

    def load_cookies(self):
        """从文件加载cookies到浏览器"""
        try:
            if os.path.exists(self.cookie_file) and self.driver:
                with open(self.cookie_file, 'rb') as f:
                    cookies = pickle.load(f)

                # 先访问主页面，然后添加cookies
                self.driver.get(self.base_url)
                time.sleep(2)

                for cookie in cookies:
                    try:
                        self.driver.add_cookie(cookie)
                    except Exception as e:
                        self.log(f"⚠️ 添加cookie失败: {str(e)}", "WARNING")
                        continue

                self.log(f"✅ 成功加载cookies，共{len(cookies)}个")
                return True
            else:
                self.log("⚠️ cookies文件不存在或浏览器未初始化", "WARNING")
                return False
        except Exception as e:
            self.log(f"❌ 加载cookies失败: {str(e)}", "ERROR")
            return False

    def check_login_status(self) -> bool:
        """检查当前登录状态"""
        try:
            # 访问一个需要登录的页面来检查登录状态
            self.driver.get(self.base_url)
            time.sleep(3)

            # 检查页面是否包含登录相关的元素
            page_source = self.driver.page_source.lower()

            # 如果页面包含用户信息或者没有登录提示，说明已登录
            login_indicators = ["登录", "login", "请先登录", "未登录"]
            logout_indicators = ["退出", "logout", "用户中心", "我的淘宝"]

            has_login_indicator = any(indicator in page_source for indicator in login_indicators)
            has_logout_indicator = any(indicator in page_source for indicator in logout_indicators)

            if has_logout_indicator and not has_login_indicator:
                self.log("✅ 检测到已登录状态")
                return True
            else:
                self.log("⚠️ 检测到未登录状态")
                return False

        except Exception as e:
            self.log(f"❌ 检查登录状态失败: {str(e)}", "ERROR")
            return False

    def login_with_cookies(self) -> bool:
        """使用保存的cookies尝试登录"""
        try:
            self.log("正在尝试使用保存的cookies登录...")

            # 检查cookies文件是否存在
            if not os.path.exists(self.cookie_file):
                self.log("⚠️ cookies文件不存在，这是首次使用")
                return False

            # 加载cookies
            if self.load_cookies():
                # 刷新页面验证登录状态
                self.driver.refresh()
                time.sleep(3)

                # 检查登录状态
                if self.check_login_status():
                    self.log("✅ 使用cookies登录成功")
                    return True
                else:
                    self.log("⚠️ cookies已过期，需要重新登录")
                    return False
            else:
                self.log("⚠️ 无法加载cookies")
                return False

        except Exception as e:
            self.log(f"❌ 使用cookies登录失败: {str(e)}", "ERROR")
            return False

    def manual_login(self) -> bool:
        """手动登录并保存cookies"""
        try:
            self.log("正在打开淘宝登录页面...")

            # 直接访问淘宝登录页面
            self.driver.get(self.login_url)
            time.sleep(3)

            # 等待用户手动登录
            self.log("请在浏览器中完成登录操作...")
            self.log("登录成功后会自动跳转到阿里资产页面")
            input("完成登录后，请按Enter键继续...")

            # 等待页面跳转完成
            time.sleep(3)

            # 检查当前URL是否已跳转到目标页面
            current_url = self.driver.current_url
            if "zc-paimai.taobao.com" in current_url:
                self.log("✅ 检测到已跳转到阿里资产页面")
                # 保存cookies
                self.save_cookies()
                self.log("✅ 手动登录成功，cookies已保存")
                return True
            else:
                # 如果没有自动跳转，手动导航到目标页面验证登录状态
                self.log("正在验证登录状态...")
                self.driver.get(self.base_url)
                time.sleep(3)

                if self.check_login_status():
                    # 保存cookies
                    self.save_cookies()
                    self.log("✅ 手动登录成功，cookies已保存")
                    return True
                else:
                    self.log("❌ 登录验证失败，请重试")
                    return False

        except Exception as e:
            self.log(f"❌ 手动登录失败: {str(e)}", "ERROR")
            return False

    def open_login_page(self) -> bool:
        """打开登录页面供用户登录（专门为GUI调用设计）"""
        try:
            self.log("正在打开淘宝登录页面...")

            # 直接访问淘宝登录页面
            self.driver.get(self.login_url)
            time.sleep(3)

            self.log("✅ 登录页面已打开，请在浏览器中完成登录")
            self.log("登录成功后会自动跳转到阿里资产页面")
            return True

        except Exception as e:
            self.log(f"❌ 打开登录页面失败: {str(e)}", "ERROR")
            return False

    def verify_login_and_save(self) -> bool:
        """验证登录状态并保存cookies（供GUI调用）"""
        try:
            # 检查当前URL是否已跳转到目标页面
            current_url = self.driver.current_url
            if "zc-paimai.taobao.com" in current_url:
                self.log("✅ 检测到已跳转到阿里资产页面")
                # 保存cookies
                self.save_cookies()
                self.log("✅ 登录验证成功，cookies已保存")
                return True
            else:
                # 如果没有自动跳转，手动导航到目标页面验证登录状态
                self.log("正在验证登录状态...")
                self.driver.get(self.base_url)
                time.sleep(3)

                if self.check_login_status():
                    # 保存cookies
                    self.save_cookies()
                    self.log("✅ 登录验证成功，cookies已保存")
                    return True
                else:
                    self.log("❌ 登录验证失败")
                    return False

        except Exception as e:
            self.log(f"❌ 验证登录状态失败: {str(e)}", "ERROR")
            return False

    def close(self):
        """关闭浏览器和清理资源"""
        try:
            if self.driver:
                self.log("正在关闭浏览器...")
                self.driver.quit()
                self.driver = None
                self.wait = None
                self.log("✅ 浏览器已关闭")
        except Exception as e:
            self.log(f"关闭浏览器时出错: {str(e)}", "WARNING")

    def stop_scraping(self):
        """停止爬取任务"""
        try:
            self.log("正在停止爬取任务...")
            self.should_stop = True
            self.log("✅ 爬取任务已停止")
        except Exception as e:
            self.log(f"停止爬取任务时出错: {str(e)}", "WARNING")

    def click_next_page(self) -> bool:
        """点击下一页按钮（增强防检测版）"""
        try:
            self.log("正在尝试点击下一页...")

            # 查找下一页按钮
            next_button = self.driver.find_element(By.XPATH, self.selectors['next_page_button'])

            # 检查按钮是否可点击
            if next_button.is_enabled() and next_button.is_displayed():
                # 元素定位随机化
                if self.anti_detection:
                    self.anti_detection.element_position_randomization(next_button)

                # 滚动到按钮位置（带随机偏移）
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_button)

                # 模拟人类行为
                if self.anti_detection:
                    self.anti_detection.simulate_human_behavior()

                # 随机延迟
                self.random_delay(1, 3)

                # 使用随机点击方法
                if self.anti_detection:
                    self.anti_detection.random_click_method(next_button)
                else:
                    next_button.click()

                self.log("✅ 成功点击下一页按钮")

                # 监控页面变化
                if self.anti_detection:
                    page_changed = self.anti_detection.monitor_page_changes(timeout=10)
                    if not page_changed:
                        self.log("⚠️ 页面未发生变化，可能点击失败", "WARNING")

                # 随机延迟
                self.random_delay(3, 6)

                # 等待新页面内容加载
                try:
                    self.wait.until(EC.presence_of_element_located((By.XPATH, self.selectors['house_list_items'])))
                    self.log("✅ 下一页内容加载完成")

                    # 模拟页面交互
                    if self.anti_detection:
                        self.anti_detection.simulate_page_interaction()

                    return True
                except TimeoutException:
                    self.log("⚠️ 下一页内容加载超时", "WARNING")

                    # 检测验证码
                    if self.anti_detection and self.anti_detection.detect_and_handle_captcha():
                        # 重新等待页面加载
                        try:
                            self.wait.until(EC.presence_of_element_located((By.XPATH, self.selectors['house_list_items'])))
                            return True
                        except TimeoutException:
                            pass

                    return False

            else:
                self.log("⚠️ 下一页按钮不可点击或不可见", "WARNING")
                return False

        except NoSuchElementException:
            self.log("⚠️ 未找到下一页按钮，可能已到最后一页", "WARNING")
            return False
        except Exception as e:
            self.log(f"❌ 点击下一页失败: {str(e)}", "ERROR")

            # 使用重试机制
            if self.anti_detection:
                try:
                    return self.anti_detection.retry_operation(self.click_next_page, max_retries=2)
                except:
                    pass

            return False

    def get_current_page_number(self) -> int:
        """获取当前页码"""
        try:
            # 尝试从分页容器中获取当前页码
            pagination_element = self.driver.find_element(By.XPATH, self.selectors['pagination_container'])

            # 查找当前页码指示器
            try:
                current_page_element = pagination_element.find_element(By.XPATH, ".//span[contains(@class, 'current')]")
                page_text = current_page_element.text.strip()

                # 提取数字
                import re
                page_match = re.search(r'\d+', page_text)
                if page_match:
                    current_page = int(page_match.group())
                    self.log(f"当前页码: {current_page}")
                    return current_page
            except NoSuchElementException:
                pass

            # 如果找不到明确的页码指示器，尝试从URL中获取
            current_url = self.driver.current_url
            page_match = re.search(r'page=(\d+)', current_url)
            if page_match:
                current_page = int(page_match.group(1))
                self.log(f"从URL获取当前页码: {current_page}")
                return current_page

            # 默认返回1
            self.log("无法确定当前页码，默认为第1页")
            return 1

        except Exception as e:
            self.log(f"获取当前页码失败: {str(e)}", "WARNING")
            return 1

    def has_next_page(self) -> bool:
        """检查是否有下一页"""
        try:
            # 查找下一页按钮
            next_button = self.driver.find_element(By.XPATH, self.selectors['next_page_button'])

            # 检查按钮是否可用
            if next_button.is_enabled() and next_button.is_displayed():
                # 检查按钮文本或类名，确认不是禁用状态
                button_text = next_button.text.lower()
                button_class = next_button.get_attribute('class').lower()

                # 如果按钮包含"禁用"、"disabled"等关键词，说明没有下一页
                disabled_indicators = ['disabled', '禁用', 'inactive', 'unavailable']
                if any(indicator in button_text or indicator in button_class for indicator in disabled_indicators):
                    self.log("下一页按钮已禁用，没有更多页面")
                    return False

                self.log("检测到可用的下一页按钮")
                return True
            else:
                self.log("下一页按钮不可用")
                return False

        except NoSuchElementException:
            self.log("未找到下一页按钮")
            return False
        except Exception as e:
            self.log(f"检查下一页状态失败: {str(e)}", "WARNING")
            return False

    def scrape_houses(self, max_pages: int = 5, max_houses_per_page: int = 20) -> Dict[str, int]:
        """主要的爬取流程"""
        try:
            self.log(f"开始爬取阿里资产房源，最大页数: {max_pages}")

            stats = {
                'total_processed': 0,
                'success_count': 0,
                'failed_count': 0,
                'pages_processed': 0
            }

            # 设置浏览器驱动（不自动登录，直接开始爬取）
            if not self.setup_driver(auto_login=False):
                return stats

            try:
                # 首先导航到第一页
                if not self.navigate_to_list_page(1):
                    self.log("无法访问第一页，停止爬取")
                    return stats

                # 使用翻页功能遍历页面
                current_page = 1
                while current_page <= max_pages and not self.should_stop:
                    self.log(f"\n=== 开始处理第 {current_page} 页 ===")

                    # 检查停止标志
                    if self.should_stop:
                        self.log("检测到停止信号，终止爬取")
                        break

                    # 提取房源链接
                    house_links = self.extract_house_links()
                    if not house_links:
                        self.log(f"第 {current_page} 页没有找到房源链接，停止爬取")
                        break

                    # 限制每页处理的房源数量
                    house_links = house_links[:max_houses_per_page]
                    stats['pages_processed'] += 1

                    # 处理每个房源
                    for i, house_url in enumerate(house_links, 1):
                        # 检查停止标志
                        if self.should_stop:
                            self.log("检测到停止信号，终止房源处理")
                            break

                        self.log(f"\n--- 处理第 {current_page} 页第 {i} 个房源 ---")
                        stats['total_processed'] += 1

                        # 爬取房源详情
                        house_detail = self.scrape_house_detail(house_url)
                        if not house_detail:
                            stats['failed_count'] += 1
                            continue

                        # AI解析数据
                        parsed_data = self.parse_with_ai(
                            house_detail['extractedText'],
                            house_detail['imageUrls'],
                            house_detail['originalUrl']
                        )

                        if not parsed_data:
                            self.log("AI解析失败，跳过此房源")
                            stats['failed_count'] += 1
                            continue

                        # 创建房源记录
                        if self.create_house_record(parsed_data):
                            stats['success_count'] += 1
                            self.log(f"✅ 房源处理成功: {parsed_data.get('title', '未知标题')}")
                        else:
                            stats['failed_count'] += 1
                            self.log("❌ 房源记录创建失败")

                        # 随机延时，避免被反爬
                        self.random_delay(3, 6)

                    # 检查是否有下一页，如果有则点击翻页
                    if current_page < max_pages and self.has_next_page():
                        self.log(f"准备翻到第 {current_page + 1} 页...")
                        if self.click_next_page():
                            current_page += 1
                            # 页面间延时
                            self.random_delay(5, 10)
                        else:
                            self.log("翻页失败，停止爬取")
                            break
                    else:
                        if current_page >= max_pages:
                            self.log(f"已达到最大页数限制 ({max_pages})，停止爬取")
                        else:
                            self.log("没有更多页面，停止爬取")
                        break

                # 输出统计信息
                self.log(f"\n=== 爬取完成 ===")
                self.log(f"处理页数: {stats['pages_processed']}")
                self.log(f"总处理房源: {stats['total_processed']}")
                self.log(f"成功创建: {stats['success_count']}")
                self.log(f"失败数量: {stats['failed_count']}")
                self.log(f"成功率: {stats['success_count']/stats['total_processed']*100:.1f}%" if stats['total_processed'] > 0 else "成功率: 0%")

                return stats

            finally:
                self.cleanup()

        except Exception as e:
            self.log(f"❌ 爬取过程中发生错误: {str(e)}", "ERROR")
            return stats

    def cleanup(self):
        """清理资源"""
        try:
            # 清理防检测管理器
            if self.anti_detection:
                try:
                    self.anti_detection.clear_browser_traces()
                    self.log("✅ 防检测管理器清理完成")
                except Exception as e:
                    self.log(f"⚠️ 防检测管理器清理失败: {str(e)}", "WARNING")
                finally:
                    self.anti_detection = None

            # 关闭浏览器驱动
            if self.driver:
                self.driver.quit()
                self.log("✅ 浏览器驱动已关闭")
        except Exception as e:
            self.log(f"清理资源时出错: {str(e)}", "WARNING")


def main():
    """主函数，用于测试"""
    try:
        print("=== 阿里资产（淘宝拍卖）爬虫测试 ===")

        # 创建API客户端
        api_client = APIClient()

        # 创建爬虫实例
        scraper = AliAssetScraper(api_client)

        # 开始爬取（测试模式：只爬取2页，每页最多5个房源）
        stats = scraper.scrape_houses(max_pages=2, max_houses_per_page=5)

        print(f"\n=== 爬取结果 ===")
        print(f"处理页数: {stats['pages_processed']}")
        print(f"总处理房源: {stats['total_processed']}")
        print(f"成功创建: {stats['success_count']}")
        print(f"失败数量: {stats['failed_count']}")

        if stats['total_processed'] > 0:
            success_rate = stats['success_count'] / stats['total_processed'] * 100
            print(f"成功率: {success_rate:.1f}%")

    except KeyboardInterrupt:
        print("\n用户中断爬取")
    except Exception as e:
        print(f"程序执行出错: {str(e)}")


if __name__ == "__main__":
    main()
