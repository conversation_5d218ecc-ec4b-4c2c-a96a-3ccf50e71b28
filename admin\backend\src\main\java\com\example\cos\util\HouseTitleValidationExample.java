package com.example.cos.util;

import com.example.cos.entity.HouseResource;
import com.example.cos.service.HouseResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 房源标题唯一性检查使用示例
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class HouseTitleValidationExample {

    @Autowired
    private HouseResourceService houseResourceService;

    /**
     * 示例：创建新房源时的标题唯一性检查
     */
    public void createHouseExample() {
        HouseResource newHouse = new HouseResource();
        newHouse.setTitle("阳光花园三室两厅精装修");
        newHouse.setStartingPrice(new BigDecimal("1200000.00"));
        newHouse.setEvaluationPrice(new BigDecimal("1500000.00"));
        newHouse.setStartTime(LocalDateTime.now().plusDays(1));
        newHouse.setEndTime(LocalDateTime.now().plusDays(8));
        newHouse.setBuildingArea(new BigDecimal("120.50"));
        newHouse.setOriginalUrl("https://example.com/house/123");

        try {
            // 使用createHouseResource方法，内部会自动进行标题唯一性检查
            HouseResource created = houseResourceService.createHouseResource(newHouse);
            System.out.println("房源创建成功: " + created.getId());
        } catch (RuntimeException e) {
            if (e.getMessage().contains("房源数据验证失败")) {
                System.out.println("房源创建失败，可能是标题重复: " + e.getMessage());
            }
        }
    }

    /**
     * 示例：更新房源时的标题唯一性检查
     */
    public void updateHouseExample() {
        // 假设要更新ID为1的房源
        HouseResource existingHouse = houseResourceService.getById(1L);
        if (existingHouse != null) {
            existingHouse.setTitle("新的房源标题");

            try {
                // 使用updateHouseResource方法，内部会自动进行标题唯一性检查（排除当前记录）
                boolean updated = houseResourceService.updateHouseResource(existingHouse);
                if (updated) {
                    System.out.println("房源更新成功");
                }
            } catch (RuntimeException e) {
                if (e.getMessage().contains("房源数据验证失败")) {
                    System.out.println("房源更新失败，可能是标题重复: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 示例：直接检查标题是否重复
     */
    public void checkTitleDuplicateExample() {
        String titleToCheck = "阳光花园三室两厅精装修";

        // 检查标题是否重复（用于新增操作）
        boolean isDuplicate = houseResourceService.isTitleDuplicate(titleToCheck);
        if (isDuplicate) {
            System.out.println("标题已存在，不能使用: " + titleToCheck);
        } else {
            System.out.println("标题可以使用: " + titleToCheck);
        }

        // 检查标题是否重复（排除指定ID，用于更新操作）
        Long excludeId = 1L;
        boolean isDuplicateExclude = houseResourceService.isTitleDuplicate(titleToCheck, excludeId);
        if (isDuplicateExclude) {
            System.out.println("标题已存在（排除ID " + excludeId + "），不能使用: " + titleToCheck);
        } else {
            System.out.println("标题可以使用（排除ID " + excludeId + "）: " + titleToCheck);
        }
    }
}
