{"groups": [{"name": "tencent.cos", "type": "com.example.cos.config.CosProperties", "sourceType": "com.example.cos.config.CosProperties"}, {"name": "wechat.miniapp", "type": "com.example.cos.config.WechatProperties", "sourceType": "com.example.cos.config.WechatProperties"}], "properties": [{"name": "tencent.cos.bucket-name", "type": "java.lang.String", "description": "存储桶名称", "sourceType": "com.example.cos.config.CosProperties"}, {"name": "tencent.cos.cdn-domain", "type": "java.lang.String", "description": "CDN加速域名", "sourceType": "com.example.cos.config.CosProperties"}, {"name": "tencent.cos.connection-timeout", "type": "java.lang.Integer", "description": "连接超时时间（毫秒）", "sourceType": "com.example.cos.config.CosProperties"}, {"name": "tencent.cos.enable-cdn", "type": "java.lang.Bo<PERSON>an", "description": "是否启用CDN加速", "sourceType": "com.example.cos.config.CosProperties"}, {"name": "tencent.cos.region", "type": "java.lang.String", "description": "地域信息", "sourceType": "com.example.cos.config.CosProperties"}, {"name": "tencent.cos.secret-id", "type": "java.lang.String", "description": "腾讯云API密钥ID", "sourceType": "com.example.cos.config.CosProperties"}, {"name": "tencent.cos.secret-key", "type": "java.lang.String", "description": "腾讯云API密钥Key", "sourceType": "com.example.cos.config.CosProperties"}, {"name": "tencent.cos.socket-timeout", "type": "java.lang.Integer", "description": "读取超时时间（毫秒）", "sourceType": "com.example.cos.config.CosProperties"}, {"name": "wechat.miniapp.api-base-url", "type": "java.lang.String", "description": "微信API基础URL", "sourceType": "com.example.cos.config.WechatProperties"}, {"name": "wechat.miniapp.app-id", "type": "java.lang.String", "description": "小程序AppID", "sourceType": "com.example.cos.config.WechatProperties"}, {"name": "wechat.miniapp.app-secret", "type": "java.lang.String", "description": "小程序AppSecret", "sourceType": "com.example.cos.config.WechatProperties"}, {"name": "wechat.miniapp.get-phone-number-url", "type": "java.lang.String", "description": "获取用户手机号URL", "sourceType": "com.example.cos.config.WechatProperties"}, {"name": "wechat.miniapp.jscode2session-url", "type": "java.lang.String", "description": "登录凭证校验URL", "sourceType": "com.example.cos.config.WechatProperties"}, {"name": "wechat.miniapp.token-url", "type": "java.lang.String", "description": "获取access_token的URL", "sourceType": "com.example.cos.config.WechatProperties"}], "hints": []}