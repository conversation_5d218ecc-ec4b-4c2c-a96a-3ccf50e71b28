<!--pages/sell-house/sell-house.wxml-->
<view class="container">
  <!-- 表单区域 -->
  <view class="form-container">
    <!-- 房屋信息表单 -->
    <view class="form-section">
      
      <picker range="{{houseTypes}}" value="{{houseTypeIndex}}" bindchange="onHouseTypeChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
        <view class="form-item">
          <view class="form-label">房屋类型</view>
          <view class="form-value-container">
            <text class="form-value">{{houseTypes[houseTypeIndex]}}</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </picker>

      <!-- 所在城市 -->
      <view class="form-item">
        <view class="form-label">所在城市</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入所在城市" value="{{cityName}}" bindinput="onCityInput"/>
        </view>
      </view>

      <!-- 小区名称 -->
      <view class="form-item">
        <view class="form-label">小区名称</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入小区名称" value="{{communityName}}" bindinput="onCommunityInput"/>
        </view>
      </view>

      <!-- 住房面积 -->
      <view class="form-item">
        <view class="form-label">住房面积m²</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入住房面积" value="{{houseArea}}" bindinput="onAreaInput" type="digit"/>
          <text class="form-unit"></text>
        </view>
      </view>

      <!-- 期望售价 -->
      <view class="form-item">
        <view class="form-label">期望售价(万)</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入期望售价" value="{{expectedPrice}}" bindinput="onPriceInput" type="digit"/>
          <text class="form-unit"></text>
        </view>
      </view>
      <!-- 联系人 -->
      <view class="form-item">
        <view class="form-label">联系人</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入联系人" value="{{contactName}}" bindinput="onContactNameInput"/>
        </view>
      </view>

      <!-- 性别选择 -->
      <picker range="{{genderOptions}}" value="{{genderIndex}}" bindchange="onGenderChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
        <view class="form-item">
          <view class="form-label">先生/女士</view>
          <view class="form-value-container">
            <text class="form-value">{{genderOptions[genderIndex]}}</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </picker>

      <!-- 联系方式 -->
      <view class="form-item" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
        <view class="form-label">联系方式</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入联系方式" value="{{contactPhone}}" bindinput="onContactPhoneInput" type="number"/>
        </view>
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="form-section">
      <view class="section-title">备注</view>
      <view class="form-item textarea-item">
        <textarea class="form-textarea" placeholder="请输入" value="{{remarks}}" bindinput="onRemarksInput" maxlength="200" style="height: 135rpx; display: block; box-sizing: border-box; left: 0rpx; top: 0rpx"></textarea>
      </view>
    </view>

    <!-- 提交记录链接 -->
    <view class="submit-record">
      <text class="record-link" bindtap="viewSubmitRecord">提交记录>></text>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-container">
    <button class="submit-btn" bindtap="onSubmit" style="width: 650rpx;">确认提交</button>
  </view>
</view>
