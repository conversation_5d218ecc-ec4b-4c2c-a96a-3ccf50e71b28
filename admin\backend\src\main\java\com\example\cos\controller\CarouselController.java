package com.example.cos.controller;

import com.example.cos.common.Result;
import com.example.cos.common.ResultCode;
import com.example.cos.dto.CarouselCreateDTO;
import com.example.cos.dto.CarouselUpdateDTO;
import com.example.cos.entity.Carousel;
import com.example.cos.service.CarouselService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 轮播图管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Api(tags = "轮播图管理")
@RestController
@RequestMapping("/api/carousel")
@Validated
public class CarouselController {

    private static final Logger logger = LoggerFactory.getLogger(CarouselController.class);

    @Autowired
    private CarouselService carouselService;

    /**
     * 获取所有轮播图列表（管理端）
     */
    @ApiOperation(value = "获取所有轮播图列表", notes = "获取所有轮播图，按排序序号升序排列")
    @GetMapping("/list")
    public Result<List<Carousel>> getAllCarousels() {
        try {
            List<Carousel> carousels = carouselService.getAllCarouselsOrderBySort();
            return Result.success("获取轮播图列表成功", carousels);
        } catch (Exception e) {
            logger.error("获取轮播图列表失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取轮播图列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取启用的轮播图列表（前端展示）
     */
    @ApiOperation(value = "获取启用的轮播图列表", notes = "获取启用状态的轮播图，按排序序号升序排列")
    @GetMapping("/enabled")
    public Result<List<Carousel>> getEnabledCarousels() {
        try {
            List<Carousel> carousels = carouselService.getEnabledCarousels();
            return Result.success("获取启用轮播图列表成功", carousels);
        } catch (Exception e) {
            logger.error("获取启用轮播图列表失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取启用轮播图列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取轮播图详情
     */
    @ApiOperation(value = "获取轮播图详情", notes = "根据ID获取轮播图详细信息")
    @GetMapping("/{id}")
    public Result<Carousel> getCarouselById(@PathVariable Long id) {
        try {
            logger.info("获取轮播图详情: {}", id);
            
            Carousel carousel = carouselService.getById(id);
            if (carousel == null) {
                return Result.error(ResultCode.NOT_FOUND.getCode(), "轮播图不存在");
            }
            
            return Result.success("获取轮播图详情成功", carousel);
        } catch (Exception e) {
            logger.error("获取轮播图详情失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取轮播图详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建轮播图
     */
    @ApiOperation(value = "创建轮播图", notes = "创建新的轮播图")
    @PostMapping
    public Result<Carousel> createCarousel(@RequestBody @Validated CarouselCreateDTO createDTO) {
        try {
            logger.info("创建轮播图请求: {}", createDTO);
            
            Carousel carousel = carouselService.createCarousel(createDTO);
            
            return Result.success("创建轮播图成功", carousel);
        } catch (IllegalArgumentException e) {
            logger.warn("创建轮播图参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("创建轮播图失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "创建轮播图失败: " + e.getMessage());
        }
    }

    /**
     * 更新轮播图
     */
    @ApiOperation(value = "更新轮播图", notes = "更新轮播图信息")
    @PutMapping
    public Result<Carousel> updateCarousel(@RequestBody @Validated CarouselUpdateDTO updateDTO) {
        try {
            logger.info("更新轮播图请求: {}", updateDTO);
            
            Carousel carousel = carouselService.updateCarousel(updateDTO);
            
            return Result.success("更新轮播图成功", carousel);
        } catch (IllegalArgumentException e) {
            logger.warn("更新轮播图参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (RuntimeException e) {
            logger.warn("更新轮播图失败: {}", e.getMessage());
            return Result.error(ResultCode.NOT_FOUND.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("更新轮播图异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "更新轮播图失败: " + e.getMessage());
        }
    }

    /**
     * 删除轮播图
     */
    @ApiOperation(value = "删除轮播图", notes = "根据ID删除轮播图")
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteCarousel(@PathVariable Long id) {
        try {
            logger.info("删除轮播图: {}", id);
            
            boolean deleted = carouselService.deleteCarousel(id);
            
            return Result.success("删除轮播图成功", deleted);
        } catch (RuntimeException e) {
            logger.warn("删除轮播图失败: {}", e.getMessage());
            return Result.error(ResultCode.NOT_FOUND.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("删除轮播图异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "删除轮播图失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除轮播图
     */
    @ApiOperation(value = "批量删除轮播图", notes = "根据ID列表批量删除轮播图")
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteCarousels(@RequestBody List<Long> ids) {
        try {
            logger.info("批量删除轮播图: {}", ids);
            
            boolean deleted = carouselService.batchDeleteCarousels(ids);
            
            return Result.success("批量删除轮播图成功", deleted);
        } catch (IllegalArgumentException e) {
            logger.warn("批量删除轮播图参数错误: {}", e.getMessage());
            return Result.error(ResultCode.PARAM_ERROR.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("批量删除轮播图异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "批量删除轮播图失败: " + e.getMessage());
        }
    }

    /**
     * 切换轮播图启用状态
     */
    @ApiOperation(value = "切换轮播图状态", notes = "切换轮播图的启用/禁用状态")
    @PutMapping("/{id}/toggle")
    public Result<Carousel> toggleCarouselStatus(@PathVariable Long id) {
        try {
            logger.info("切换轮播图状态: {}", id);
            
            Carousel carousel = carouselService.toggleCarouselStatus(id);
            
            return Result.success("切换轮播图状态成功", carousel);
        } catch (RuntimeException e) {
            logger.warn("切换轮播图状态失败: {}", e.getMessage());
            return Result.error(ResultCode.NOT_FOUND.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("切换轮播图状态异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "切换轮播图状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新轮播图排序
     */
    @ApiOperation(value = "批量更新排序", notes = "批量更新轮播图的排序序号")
    @PutMapping("/batch-sort")
    public Result<Boolean> batchUpdateSort(@RequestBody List<Carousel> carousels) {
        try {
            logger.info("批量更新轮播图排序，数量: {}", carousels.size());
            
            boolean updated = carouselService.batchUpdateSort(carousels);
            
            return Result.success("批量更新排序成功", updated);
        } catch (Exception e) {
            logger.error("批量更新排序异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "批量更新排序失败: " + e.getMessage());
        }
    }
}
