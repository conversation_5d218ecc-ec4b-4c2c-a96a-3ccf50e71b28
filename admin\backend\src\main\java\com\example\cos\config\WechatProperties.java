package com.example.cos.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 微信小程序配置属性
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "wechat.miniapp")
public class WechatProperties {

    /**
     * 小程序AppID
     */
    private String appId;

    /**
     * 小程序AppSecret
     */
    private String appSecret;

    /**
     * 微信API基础URL
     */
    private String apiBaseUrl = "https://api.weixin.qq.com";

    /**
     * 获取access_token的URL
     */
    private String tokenUrl = "/cgi-bin/token";

    /**
     * 登录凭证校验URL
     */
    private String jscode2sessionUrl = "/sns/jscode2session";

    /**
     * 获取用户手机号URL
     */
    private String getPhoneNumberUrl = "/wxa/business/getuserphonenumber";

    public WechatProperties() {}

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getApiBaseUrl() {
        return apiBaseUrl;
    }

    public void setApiBaseUrl(String apiBaseUrl) {
        this.apiBaseUrl = apiBaseUrl;
    }

    public String getTokenUrl() {
        return tokenUrl;
    }

    public void setTokenUrl(String tokenUrl) {
        this.tokenUrl = tokenUrl;
    }

    public String getJscode2sessionUrl() {
        return jscode2sessionUrl;
    }

    public void setJscode2sessionUrl(String jscode2sessionUrl) {
        this.jscode2sessionUrl = jscode2sessionUrl;
    }

    public String getGetPhoneNumberUrl() {
        return getPhoneNumberUrl;
    }

    public void setGetPhoneNumberUrl(String getPhoneNumberUrl) {
        this.getPhoneNumberUrl = getPhoneNumberUrl;
    }

    @Override
    public String toString() {
        return "WechatProperties{" +
                "appId='" + appId + '\'' +
                ", appSecret='" + appSecret + '\'' +
                ", apiBaseUrl='" + apiBaseUrl + '\'' +
                ", tokenUrl='" + tokenUrl + '\'' +
                ", jscode2sessionUrl='" + jscode2sessionUrl + '\'' +
                ", getPhoneNumberUrl='" + getPhoneNumberUrl + '\'' +
                '}';
    }
}
