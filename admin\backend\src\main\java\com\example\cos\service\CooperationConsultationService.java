package com.example.cos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.cos.dto.CooperationConsultationCreateDTO;
import com.example.cos.entity.CooperationConsultation;

import java.util.List;

/**
 * 合作咨询服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CooperationConsultationService extends IService<CooperationConsultation> {

    /**
     * 创建合作咨询
     * 
     * @param createDTO 创建DTO
     * @return 创建的咨询信息
     */
    CooperationConsultation createConsultation(CooperationConsultationCreateDTO createDTO);

    /**
     * 根据用户ID查询合作咨询
     * 
     * @param userId 用户ID
     * @return 咨询列表
     */
    List<CooperationConsultation> findByUserId(Integer userId);

    /**
     * 根据城市查询合作咨询
     * 
     * @param city 城市
     * @return 咨询列表
     */
    List<CooperationConsultation> findByCity(String city);

    /**
     * 根据姓名查询合作咨询
     * 
     * @param name 姓名
     * @return 咨询列表
     */
    List<CooperationConsultation> findByName(String name);

    /**
     * 更新合作咨询
     * 
     * @param consultation 咨询信息
     * @return 是否更新成功
     */
    boolean updateConsultation(CooperationConsultation consultation);

    /**
     * 删除合作咨询
     * 
     * @param id 咨询ID
     * @return 是否删除成功
     */
    boolean deleteConsultation(Integer id);
}
