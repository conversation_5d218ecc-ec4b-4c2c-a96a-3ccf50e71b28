// pages/sell-house/sell-house.js
const api = require('../../config/api.js')
const userService = require('../../services/user.js')
const util = require('../../utils/util.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 房屋类型选项
    houseTypes: ['住宅', '公寓', '写字楼', '商业', '工厂', '车位'],
    houseTypeIndex: 0,
    
    // 性别选项
    genderOptions: ['先生', '女士'],
    genderIndex: 0,
    
    // 表单数据
    cityName: '',
    communityName: '',
    houseArea: '',
    expectedPrice: '',
    contactName: '',
    contactPhone: '',
    remarks: '',
    
    // 提交状态
    canSubmit: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('卖房页面加载');
    this.checkLoginStatus();
  },

  /**
   * 检查登录状态（仅在页面加载时提示）
   */
  checkLoginStatus() {
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      // 延迟显示，避免页面加载时立即弹窗
      setTimeout(() => {
        wx.showModal({
          title: '温馨提示',
          content: '提交房源信息需要先登录，是否现在去登录？',
          showCancel: true,
          cancelText: '稍后登录',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              // 跳转到个人中心页面进行登录
              wx.switchTab({
                url: '/pages/profile/profile'
              })
            }
            // 用户选择稍后登录，可以继续填写表单
          }
        })
      }, 500)
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时检查登录状态
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      // 如果未登录，不显示弹窗，只在提交时提示
      console.log('用户未登录，请在提交时登录')
    }
    this.checkCanSubmit();
  },

  /**
   * 房屋类型选择
   */
  onHouseTypeChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      houseTypeIndex: index
    });
    this.checkCanSubmit();
  },

  /**
   * 性别选择
   */
  onGenderChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      genderIndex: index
    });
    this.checkCanSubmit();
  },

  /**
   * 城市输入
   */
  onCityInput(e) {
    this.setData({
      cityName: e.detail.value
    });
    this.checkCanSubmit();
  },

  /**
   * 小区名称输入
   */
  onCommunityInput(e) {
    this.setData({
      communityName: e.detail.value
    });
    this.checkCanSubmit();
  },

  /**
   * 面积输入
   */
  onAreaInput(e) {
    const value = this.validateNumberInput(e.detail.value);
    this.setData({
      houseArea: value
    });
    this.checkCanSubmit();
  },

  /**
   * 价格输入
   */
  onPriceInput(e) {
    const value = this.validateNumberInput(e.detail.value);
    this.setData({
      expectedPrice: value
    });
    this.checkCanSubmit();
  },

  /**
   * 联系人输入
   */
  onContactNameInput(e) {
    this.setData({
      contactName: e.detail.value
    });
    this.checkCanSubmit();
  },

  /**
   * 联系电话输入
   */
  onContactPhoneInput(e) {
    const value = this.validatePhoneInput(e.detail.value);
    this.setData({
      contactPhone: value
    });
    this.checkCanSubmit();
  },

  /**
   * 备注输入
   */
  onRemarksInput(e) {
    this.setData({
      remarks: e.detail.value
    });
  },

  /**
   * 验证数字输入
   */
  validateNumberInput(value) {
    // 只允许数字和小数点
    let cleanValue = value.replace(/[^\d.]/g, '');
    
    // 确保只有一个小数点
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      cleanValue = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // 限制小数点后最多2位
    if (parts.length === 2 && parts[1].length > 2) {
      cleanValue = parts[0] + '.' + parts[1].substring(0, 2);
    }
    
    return cleanValue;
  },

  /**
   * 验证手机号输入
   */
  validatePhoneInput(value) {
    // 只允许数字
    return value.replace(/[^\d]/g, '');
  },

  /**
   * 检查是否可以提交
   */
  checkCanSubmit() {
    const { cityName, communityName, houseArea, expectedPrice, contactName, contactPhone } = this.data;
    
    const canSubmit = cityName.trim() !== '' && 
                     communityName.trim() !== '' && 
                     houseArea.trim() !== '' && 
                     expectedPrice.trim() !== '' && 
                     contactName.trim() !== '' && 
                     contactPhone.trim() !== '' &&
                     contactPhone.length >= 11;
    
    this.setData({
      canSubmit: canSubmit
    });
  },

  /**
   * 查看提交记录
   */
  viewSubmitRecord() {
    // 检查登录状态
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      util.showError('请先登录后查看提交记录')
      return
    }

    // 跳转到房源出售记录页面
    wx.navigateTo({
      url: '/pages/house-sale-records/house-sale-records'
    })
  },

  /**
   * 提交表单
   */
  onSubmit() {
    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请完善必填信息',
        icon: 'none'
      });
      return;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(this.data.contactPhone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    // 获取当前用户信息
    const currentUser = userService.getLocalUserInfo()
    if (!currentUser || !currentUser.id) {
      util.showError('请先登录')
      return
    }

    // 构建提交数据（按照API接口要求）
    const submitData = {
      userId: currentUser.id,
      houseType: this.data.houseTypes[this.data.houseTypeIndex],
      city: this.data.cityName,
      communityName: this.data.communityName,
      housingArea: parseFloat(this.data.houseArea),
      expectedPrice: parseFloat(this.data.expectedPrice),
      contactPerson: this.data.contactName,
      gender: this.data.genderOptions[this.data.genderIndex],
      contactInfo: this.data.contactPhone,
      remarks: this.data.remarks || ''
    };

    console.log('提交数据:', submitData);

    // 调用房源出售登记API
    this.submitHouseSale(submitData);
  },

  /**
   * 提交房源出售登记
   */
  async submitHouseSale(submitData) {
    try {
      util.showLoading('提交中...')

      // 获取用户token
      const accessToken = wx.getStorageSync('accessToken')
      if (!accessToken) {
        util.hideLoading()
        util.showError('请先登录')
        return
      }

      // 调用房源出售登记API
      const response = await api.post(api.API.HOUSE_SALE_CREATE, submitData, {
        'Authorization': `Bearer ${accessToken}`
      })

      util.hideLoading()

      if (response.code === 200) {
        // 提交成功
        console.log('房源登记成功:', response.data)

        wx.showModal({
          title: '提交成功',
          content: `您的房源信息已提交成功，登记ID：${response.data.id}，我们会尽快联系您！`,
          showCancel: false,
          confirmText: '确定',
          success: (res) => {
            if (res.confirm) {
              // 清空表单
              this.resetForm()
              // 返回上一页
              wx.navigateBack()
            }
          }
        })
      } else {
        // 提交失败
        util.showError(response.message || '提交失败，请重试')
      }

    } catch (error) {
      util.hideLoading()
      console.error('提交房源信息失败:', error)

      const errorMessage = error.message || error.errMsg || '提交失败'
      if (errorMessage.includes('登录') || errorMessage.includes('401')) {
        util.showError('登录已过期，请重新登录')
      } else if (errorMessage.includes('网络')) {
        util.showError('网络连接失败，请检查网络')
      } else {
        util.showError('提交失败，请重试')
      }
    }
  },

  /**
   * 重置表单
   */
  resetForm() {
    this.setData({
      houseTypeIndex: 0,
      genderIndex: 0,
      cityName: '',
      communityName: '',
      houseArea: '',
      expectedPrice: '',
      contactName: '',
      contactPhone: '',
      remarks: '',
      canSubmit: false
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '锦绣资产 - 我要卖房',
      path: '/pages/sell-house/sell-house'
    };
  }
});
