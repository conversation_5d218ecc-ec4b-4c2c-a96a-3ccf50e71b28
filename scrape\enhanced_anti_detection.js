// 增强版防检测JavaScript脚本
// 专为阿里资产爬虫设计

(function() {
    'use strict';
    
    console.log('🚀 加载增强版防检测脚本...');
    
    // 1. 环境特征模拟 - 操作系统环境模拟
    Object.defineProperty(navigator, 'platform', {
        get: () => 'Win32',
        configurable: true
    });
    
    Object.defineProperty(navigator, 'userAgent', {
        get: () => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        configurable: true
    });
    
    // 2. 语言环境设置
    Object.defineProperty(navigator, 'languages', {
        get: () => ['zh-CN', 'zh', 'en-US', 'en'],
        configurable: true
    });
    
    Object.defineProperty(navigator, 'language', {
        get: () => 'zh-CN',
        configurable: true
    });
    
    // 3. 屏幕分辨率模拟
    Object.defineProperty(screen, 'width', {
        get: () => 1920,
        configurable: true
    });
    
    Object.defineProperty(screen, 'height', {
        get: () => 1080,
        configurable: true
    });
    
    Object.defineProperty(screen, 'availWidth', {
        get: () => 1920,
        configurable: true
    });
    
    Object.defineProperty(screen, 'availHeight', {
        get: () => 1040,
        configurable: true
    });
    
    Object.defineProperty(screen, 'colorDepth', {
        get: () => 24,
        configurable: true
    });
    
    Object.defineProperty(screen, 'pixelDepth', {
        get: () => 24,
        configurable: true
    });
    
    // 4. 硬件信息模拟
    Object.defineProperty(navigator, 'hardwareConcurrency', {
        get: () => 8,
        configurable: true
    });
    
    Object.defineProperty(navigator, 'deviceMemory', {
        get: () => 8,
        configurable: true
    });
    
    Object.defineProperty(navigator, 'maxTouchPoints', {
        get: () => 0,
        configurable: true
    });
    
    // 5. 隐藏webdriver属性
    Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
        configurable: true
    });
    
    // 6. 行为模拟 - 人工化鼠标移动
    let mouseX = Math.floor(Math.random() * window.innerWidth);
    let mouseY = Math.floor(Math.random() * window.innerHeight);
    
    function simulateMouseMovement() {
        const steps = Math.floor(Math.random() * 6) + 3; // 3-8步
        
        for (let i = 0; i < steps; i++) {
            setTimeout(() => {
                mouseX += (Math.random() - 0.5) * 20;
                mouseY += (Math.random() - 0.5) * 20;
                
                mouseX = Math.max(0, Math.min(window.innerWidth, mouseX));
                mouseY = Math.max(0, Math.min(window.innerHeight, mouseY));
                
                const event = new MouseEvent('mousemove', {
                    clientX: mouseX,
                    clientY: mouseY,
                    bubbles: true
                });
                document.dispatchEvent(event);
            }, i * (Math.random() * 100 + 50));
        }
    }
    
    // 定期执行鼠标移动
    setInterval(simulateMouseMovement, Math.random() * 10000 + 5000);
    
    // 7. 随机化策略 - 点击策略多样化
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    EventTarget.prototype.addEventListener = function(type, listener, options) {
        if (type === 'click') {
            const wrappedListener = function(event) {
                // 添加随机延迟
                setTimeout(() => {
                    listener.call(this, event);
                }, Math.random() * 50);
            };
            return originalAddEventListener.call(this, type, wrappedListener, options);
        }
        return originalAddEventListener.call(this, type, listener, options);
    };
    
    // 8. 元素定位随机化
    function addRandomOffset(element) {
        if (element && element.getBoundingClientRect) {
            const rect = element.getBoundingClientRect();
            const offsetX = (Math.random() - 0.5) * 10;
            const offsetY = (Math.random() - 0.5) * 10;
            
            element.style.transform = `translate(${offsetX}px, ${offsetY}px)`;
            
            setTimeout(() => {
                element.style.transform = '';
            }, Math.random() * 1000 + 500);
        }
    }
    
    // 9. 随机化策略 - 页面访问随机化
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        // 添加随机延迟
        return new Promise(resolve => {
            setTimeout(() => {
                resolve(originalFetch.apply(this, args));
            }, Math.random() * 1000 + 500);
        });
    };
    
    // 10. 批次处理随机化
    let requestCount = 0;
    const originalXMLHttpRequest = window.XMLHttpRequest;
    window.XMLHttpRequest = function() {
        const xhr = new originalXMLHttpRequest();
        const originalSend = xhr.send;
        
        xhr.send = function(...args) {
            requestCount++;
            
            // 每10个请求后增加延迟
            if (requestCount % 10 === 0) {
                setTimeout(() => {
                    originalSend.apply(this, args);
                }, Math.random() * 5000 + 2000);
            } else {
                setTimeout(() => {
                    originalSend.apply(this, args);
                }, Math.random() * 500 + 200);
            }
        };
        
        return xhr;
    };
    
    // 11. 异常处理 - 验证码处理
    function detectCaptcha() {
        const captchaSelectors = [
            '[class*="captcha"]',
            '[class*="verify"]',
            '[class*="slider"]',
            'canvas[class*="captcha"]',
            'iframe[src*="captcha"]'
        ];
        
        for (const selector of captchaSelectors) {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                console.log('🔍 检测到验证码元素');
                return true;
            }
        }
        return false;
    }
    
    // 定期检测验证码
    setInterval(detectCaptcha, 3000);
    
    // 12. 网络异常处理
    window.addEventListener('error', function(event) {
        if (event.message && event.message.includes('network')) {
            console.log('🌐 检测到网络错误，准备重试...');
            setTimeout(() => {
                window.location.reload();
            }, Math.random() * 5000 + 3000);
        }
    });
    
    // 13. 高级防检测特性 - 浏览器指纹随机化
    const originalGetContext = HTMLCanvasElement.prototype.getContext;
    HTMLCanvasElement.prototype.getContext = function(type, ...args) {
        const context = originalGetContext.call(this, type, ...args);
        
        if (type === '2d' && context) {
            const originalFillText = context.fillText;
            context.fillText = function(text, x, y, maxWidth) {
                // 添加微小的随机偏移
                const offsetX = (Math.random() - 0.5) * 0.1;
                const offsetY = (Math.random() - 0.5) * 0.1;
                return originalFillText.call(this, text, x + offsetX, y + offsetY, maxWidth);
            };
        }
        
        return context;
    };
    
    // 14. WebGL指纹随机化
    const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
    WebGLRenderingContext.prototype.getParameter = function(parameter) {
        // UNMASKED_VENDOR_WEBGL
        if (parameter === 37445) {
            const vendors = ['Intel Inc.', 'NVIDIA Corporation', 'AMD'];
            return vendors[Math.floor(Math.random() * vendors.length)];
        }
        // UNMASKED_RENDERER_WEBGL  
        if (parameter === 37446) {
            const renderers = [
                'Intel Iris OpenGL Engine',
                'NVIDIA GeForce GTX 1060',
                'AMD Radeon RX 580'
            ];
            return renderers[Math.floor(Math.random() * renderers.length)];
        }
        return originalGetParameter.call(this, parameter);
    };
    
    // 15. 时区检测防护
    const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
    Date.prototype.getTimezoneOffset = function() {
        return -480; // 中国时区 UTC+8
    };
    
    // 16. 插件检测防护
    Object.defineProperty(navigator, 'plugins', {
        get: () => {
            return Object.setPrototypeOf([
                {
                    0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
                    description: "Portable Document Format",
                    filename: "internal-pdf-viewer",
                    length: 1,
                    name: "Chrome PDF Plugin"
                },
                {
                    0: {type: "application/pdf", suffixes: "pdf", description: ""},
                    description: "",
                    filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                    length: 1,
                    name: "Chrome PDF Viewer"
                }
            ], PluginArray.prototype);
        },
        configurable: true
    });
    
    // 17. 权限API防护
    const originalQuery = navigator.permissions.query;
    navigator.permissions.query = function(parameters) {
        if (parameters.name === 'notifications') {
            return Promise.resolve({ state: 'default' });
        }
        return originalQuery.call(this, parameters);
    };
    
    // 18. Chrome运行时防护
    if (!window.chrome) {
        window.chrome = {};
    }
    
    window.chrome.runtime = {
        onConnect: undefined,
        onMessage: undefined,
        sendMessage: undefined,
        connect: undefined
    };
    
    window.chrome.app = {
        isInstalled: false
    };
    
    // 19. 清理自动化痕迹
    const automationProps = [
        'cdc_adoQpoasnfa76pfcZLmcfl_Array',
        'cdc_adoQpoasnfa76pfcZLmcfl_Promise', 
        'cdc_adoQpoasnfa76pfcZLmcfl_Symbol',
        'selenium',
        '_selenium',
        '__selenium_unwrapped',
        '__selenium_evaluate',
        '__webdriver_evaluate',
        '__driver_evaluate',
        '__webdriver_unwrapped',
        '__driver_unwrapped',
        '_Selenium_IDE_Recorder'
    ];
    
    automationProps.forEach(prop => {
        delete window[prop];
    });
    
    // 20. 函数toString防护
    const originalToString = Function.prototype.toString;
    Function.prototype.toString = function() {
        if (this === navigator.webdriver) {
            return 'function webdriver() { [native code] }';
        }
        return originalToString.call(this);
    };
    
    console.log('✅ 增强版防检测脚本加载完成');
    
})();
