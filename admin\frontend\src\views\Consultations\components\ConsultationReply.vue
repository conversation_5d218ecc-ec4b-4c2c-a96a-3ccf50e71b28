<template>
  <div class="consultation-reply">
    <!-- 咨询信息摘要 -->
    <div class="consultation-summary">
      <h4>咨询信息</h4>
      <el-descriptions :column="2" size="small">
        <el-descriptions-item label="姓名">{{ consultationData.name }}</el-descriptions-item>
        <el-descriptions-item label="联系方式">{{ consultationData.contactInfo }}</el-descriptions-item>
        <el-descriptions-item label="咨询内容" :span="2">
          <div class="content-preview">{{ consultationData.consultationContent }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 回复表单 -->
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="reply-form"
    >
      <el-form-item label="回复方式" prop="replyMethod">
        <el-radio-group v-model="form.replyMethod">
          <el-radio label="电话回复">电话回复</el-radio>
          <el-radio label="短信回复">短信回复</el-radio>
          <el-radio label="邮件回复">邮件回复</el-radio>
          <el-radio label="微信回复">微信回复</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="回复内容" prop="replyContent">
        <el-input
          v-model="form.replyContent"
          type="textarea"
          :rows="6"
          placeholder="请输入回复内容"
        />
      </el-form-item>

      <el-form-item label="处理状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择处理状态" style="width: 200px">
          <el-option label="处理中" value="处理中" />
          <el-option label="已回复" value="已回复" />
          <el-option label="已关闭" value="已关闭" />
        </el-select>
      </el-form-item>

      <el-form-item label="跟进提醒">
        <el-switch
          v-model="form.needFollowUp"
          active-text="需要跟进"
          inactive-text="无需跟进"
        />
      </el-form-item>

      <el-form-item label="跟进时间" prop="followUpTime" v-if="form.needFollowUp">
        <el-date-picker
          v-model="form.followUpTime"
          type="datetime"
          placeholder="选择跟进时间"
          style="width: 200px"
        />
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitReply">提交回复</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  consultationData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['submit'])

const formRef = ref()

// 表单数据
const form = reactive({
  consultationId: null,
  replyMethod: '电话回复',
  replyContent: '',
  status: '已回复',
  needFollowUp: false,
  followUpTime: null,
  remarks: ''
})

// 表单验证规则
const rules = {
  replyMethod: [
    { required: true, message: '请选择回复方式', trigger: 'change' }
  ],
  replyContent: [
    { required: true, message: '请输入回复内容', trigger: 'blur' },
    { min: 10, message: '回复内容至少10个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择处理状态', trigger: 'change' }
  ],
  followUpTime: [
    { required: true, message: '请选择跟进时间', trigger: 'change' }
  ]
}

// 提交回复
const submitReply = async () => {
  try {
    await formRef.value.validate()
    
    const replyData = {
      ...form,
      consultationId: props.consultationData.id,
      replyTime: new Date().toLocaleString()
    }
    
    emit('submit', replyData)
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
  Object.assign(form, {
    consultationId: null,
    replyMethod: '电话回复',
    replyContent: '',
    status: '已回复',
    needFollowUp: false,
    followUpTime: null,
    remarks: ''
  })
}
</script>

<style lang="scss" scoped>
.consultation-reply {
  .consultation-summary {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;

    h4 {
      margin: 0 0 15px 0;
      color: #303133;
    }

    .content-preview {
      line-height: 1.6;
      color: #606266;
      max-height: 60px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .reply-form {
    // 样式可以根据需要添加
  }
}
</style>
