import request from '@/utils/request'

// 管理员管理API

/**
 * 管理员登录
 * @param {Object} data 登录数据
 */
export function adminLogin(data) {
  return request({
    url: '/admin/login',
    method: 'post',
    data
  })
}

/**
 * 创建管理员
 * @param {String} username 用户名
 * @param {String} password 密码
 * @param {String} role 角色
 */
export function createAdmin(username, password, role) {
  return request({
    url: '/admin/create',
    method: 'post',
    params: { username, password, role }
  })
}

/**
 * 根据用户名查询管理员
 * @param {String} username 用户名
 */
export function getAdminByUsername(username) {
  return request({
    url: '/admin/username',
    method: 'get',
    params: { username }
  })
}
