import request from '@/utils/request'

// 合作咨询API

/**
 * 创建合作咨询
 * @param {Object} data 咨询数据
 */
export function createConsultation(data) {
  return request({
    url: '/consultation/create',
    method: 'post',
    data
  })
}

/**
 * 根据ID查询合作咨询
 * @param {Number} id 咨询ID
 */
export function getConsultationById(id) {
  return request({
    url: `/consultation/${id}`,
    method: 'get'
  })
}

/**
 * 根据用户ID查询合作咨询
 * @param {Number} userId 用户ID
 */
export function getConsultationsByUserId(userId) {
  return request({
    url: '/consultation/user',
    method: 'get',
    params: { userId }
  })
}

/**
 * 根据城市查询合作咨询
 * @param {String} city 城市
 */
export function getConsultationsByCity(city) {
  return request({
    url: '/consultation/city',
    method: 'get',
    params: { city }
  })
}

/**
 * 根据姓名查询合作咨询
 * @param {String} name 姓名
 */
export function getConsultationsByName(name) {
  return request({
    url: '/consultation/name',
    method: 'get',
    params: { name }
  })
}

/**
 * 更新合作咨询
 * @param {Object} data 咨询数据
 */
export function updateConsultation(data) {
  return request({
    url: '/consultation/update',
    method: 'put',
    data
  })
}

/**
 * 删除合作咨询
 * @param {Number} id 咨询ID
 */
export function deleteConsultation(id) {
  return request({
    url: `/consultation/${id}`,
    method: 'delete'
  })
}

/**
 * 查询所有合作咨询
 */
export function getAllConsultations() {
  return request({
    url: '/consultation/all',
    method: 'get'
  })
}
