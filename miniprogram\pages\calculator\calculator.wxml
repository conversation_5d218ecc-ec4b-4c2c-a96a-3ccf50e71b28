<!--pages/calculator/calculator.wxml-->
<view class="container">

  <!-- 主要内容区域 -->
  <view class="content">
   

    <!-- 选项卡 -->
    <view class="tab-container">
      <view class="tab-item {{currentTab == 0 ? 'active' : ''}}" bindtap="switchTab" data-index="0">
        <text>按贷款总额</text>
      </view>
      <view class="tab-item {{currentTab == 1 ? 'active' : ''}}" bindtap="switchTab" data-index="1">
        <text>按房屋总价</text>
      </view>
    </view>

    <!-- 表单区域 -->
    <view class="form-container">
      <!-- 按贷款总额模式 -->
      <view wx:if="{{currentTab == 0}}">
        <!-- 贷款金额 -->
        <view class="form-item">
          <view class="form-label">贷款金额</view>
          <view class="form-input-container">
            <input class="form-input" placeholder="请输入贷款金额" value="{{loanAmount}}" bindinput="onLoanAmountInput" type="digit"/>
            <text class="form-unit">万</text>
          </view>
        </view>
      </view>

      <!-- 按房屋总价模式 -->
      <view wx:if="{{currentTab == 1}}">
        <!-- 房屋总价 -->
        <view class="form-item">
          <view class="form-label">房屋总价</view>
          <view class="form-input-container">
            <input class="form-input" placeholder="请输入房屋总价" value="{{housePrice}}" bindinput="onHousePriceInput" type="digit"/>
            <text class="form-unit">万</text>
          </view>
        </view>

        <!-- 贷款比例 -->
        <view class="form-item">
          <view class="form-label">贷款比例</view>
          <view class="form-input-container">
            <input class="form-input" placeholder="请输入贷款比例" value="{{loanRatio}}" bindinput="onLoanRatioInput" type="digit"/>
            <text class="form-unit">成</text>
          </view>
        </view>

        <!-- 贷款金额（自动计算） -->
        <view class="form-item">
          <view class="form-label">贷款金额</view>
          <view class="form-input-container">
            <input class="form-input" placeholder="请输入贷款金额" value="{{calculatedLoanAmount}}" bindinput="onCalculatedLoanAmountInput" type="digit"/>
            <text class="form-unit">万</text>
          </view>
        </view>
      </view>

      <!-- 商贷年限 -->
      <picker range="{{yearOptions}}" value="{{yearIndex}}" bindchange="onYearChange">
        <view class="form-item">
          <view class="form-label">商贷年限</view>
          <view class="form-value-container">
            <text class="form-value">{{loanYears}}年({{loanYears * 12}}期)</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </picker>

      <!-- 利率方式 -->
      <picker range="{{rateTypeOptions}}" value="{{rateTypeIndex}}" bindchange="onRateTypeChange">
        <view class="form-item">
          <view class="form-label">利率方式</view>
          <view class="form-value-container">
            <text class="form-value">{{rateTypeOptions[rateTypeIndex]}}</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </picker>

      <!-- LPR信息 -->
      <view class="lpr-info">
        <view class="lpr-tag">07.06</view>
        <text class="lpr-text">LPR：一年期3.1%，五年期3.6%</text>
      </view>

      <!-- 商贷利率 -->
      <picker range="{{rateOptions}}" value="{{rateIndex}}" bindchange="onRateChange">
        <view class="form-item">
          <view class="form-label">商贷利率</view>
          <view class="form-value-container">
            <text class="-value" style="position: relative; left: -62rpx; top: 0rpx">{{loanRate}}</text>
            <text class="form-unit" style="position: relative; left: -40rpx; top: -3rpx">%</text>
            <text class="form-select">选择</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </picker>
    </view>

    <!-- 计算按钮 -->
    <view class="calculate-btn" bindtap="calculate">
      <text>开始计算</text>
    </view>

    <!-- 常见问题 -->
    <view class="faq-link" bindtap="showFAQ">
      <text>常见问题解决方案</text>
    </view>
  </view>
</view>


