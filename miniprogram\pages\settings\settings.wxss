/* pages/settings/settings.wxss */
.container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}


/* 设置内容样式 */
.settings-content {
  padding: 20rpx 0;
  margin-top: -200rpx;
  width: 100%;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.3s ease;
}

.setting-item:active {
  background-color: #f8f8f8;
}

.setting-item:first-child {
  border-top: 1rpx solid #f5f5f5;
}

.setting-left {
  flex: 1;
}

.setting-label {
  font-size: 30rpx;
  color: #333333;
}

.setting-right {
  display: flex;
  align-items: center;
}

.avatar-preview {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
  border: 2rpx solid #f0f0f0;
}

.setting-value {
  font-size: 28rpx;
  color: #666666;
  margin-right: 20rpx;
  max-width: 300rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}


/* 退出登录按钮样式 */
.logout-section {
  position: fixed;
  bottom: 60rpx;
  left: 40rpx;
  right: 40rpx;
}

.logout-btn {
  width: 100%;
  padding: 30rpx 0;
  background-color: #F8877D;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 50rpx;
  border: none;
  transition: all 0.3s ease;
}

.logout-btn:active {
  background-color: #e67a6f;
  transform: scale(0.98);
}

/* 弹窗通用样式 */
.avatar-modal, .nickname-modal, .phone-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-modal-content, .nickname-modal-content, .phone-modal-content {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

.avatar-modal-header, .nickname-modal-header, .phone-modal-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
  position: relative;
}

.avatar-modal-title, .nickname-modal-title, .phone-modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.avatar-modal-close, .nickname-modal-close, .phone-modal-close {
  position: absolute;
  right: 40rpx;
  top: 30rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999999;
}

.avatar-modal-body, .nickname-modal-body, .phone-modal-body {
  padding: 40rpx;
}

/* 头像选择弹窗样式 */
.avatar-btn {
  width: 100%;
  padding: 30rpx 0;
  background-color: #F8877D;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 30rpx;
  border: none;
}

.avatar-btn-text {
  font-size: 28rpx;
}

/* 昵称修改弹窗样式 */
.nickname-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  line-height: 76rpx;
  text-align: left;
  display: flex;
  align-items: center;
}

.nickname-modal-footer {
  display: flex;
  padding: 30rpx 40rpx;
  border-top: 2rpx solid #f5f5f5;
  gap: 20rpx;
}

.nickname-cancel-btn {
  flex: 1;
  padding: 24rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #666666;
  background-color: #f8f8f8;
  border-radius: 30rpx;
}

.nickname-confirm-btn {
  flex: 1;
  padding: 24rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #ffffff;
  background-color: #cccccc;
  border-radius: 30rpx;
  transition: all 0.3s ease;
}

.nickname-confirm-btn.active {
  background-color: #F8877D;
}

/* 手机号授权弹窗样式 */
.phone-tip {
  margin-bottom: 40rpx;
  text-align: center;
}

.phone-tip-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

.phone-btn {
  width: 100%;
  padding: 30rpx 0;
  background-color: #F8877D;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 30rpx;
  border: none;
}

.phone-btn-text {
  font-size: 28rpx;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
