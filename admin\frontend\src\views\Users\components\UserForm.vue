<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
    class="user-form"
  >
    <el-form-item label="昵称" prop="nickname">
      <el-input v-model="form.nickname" placeholder="请输入昵称" />
    </el-form-item>

    <el-form-item label="手机号" prop="phoneNumber">
      <el-input v-model="form.phoneNumber" placeholder="请输入手机号" />
    </el-form-item>

    <el-form-item label="微信OpenID" prop="wechatOpenid">
      <el-input v-model="form.wechatOpenid" placeholder="请输入微信OpenID" />
    </el-form-item>

    <el-form-item label="头像">
      <el-upload
        class="avatar-uploader"
        action="/api/cos/upload"
        :show-file-list="false"
        :on-success="handleAvatarSuccess"
        :before-upload="beforeAvatarUpload"
      >
        <img v-if="form.avatarUrl" :src="form.avatarUrl" class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
      </el-upload>
    </el-form-item>

    <el-form-item label="用户角色" prop="role">
      <el-select v-model="form.role" placeholder="请选择用户角色">
        <el-option
          label="普通用户"
          :value="0"
        />
        <el-option
          label="有权限用户"
          :value="1"
        />
      </el-select>
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="submitForm">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
      <el-button @click="resetForm">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  userData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit'])

const formRef = ref()

// 表单数据
const form = reactive({
  id: null,
  nickname: '',
  phoneNumber: '',
  wechatOpenid: '',
  avatarUrl: '',
  role: 0
})

// 表单验证规则
const rules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' }
  ],
  phoneNumber: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.userData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(form, newData)
  }
}, { immediate: true, deep: true })

// 头像上传成功
const handleAvatarSuccess = (response, file) => {
  if (response.code === 200) {
    form.avatarUrl = response.data.url
    ElMessage.success('头像上传成功')
  } else {
    ElMessage.error('头像上传失败')
  }
}

// 头像上传前验证
const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()
    emit('submit', { ...form })
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
  Object.assign(form, {
    id: null,
    nickname: '',
    phoneNumber: '',
    wechatOpenid: '',
    avatarUrl: '',
    role: 0
  })
}

// 暴露方法给父组件
defineExpose({
  resetForm
})
</script>

<style lang="scss" scoped>
.user-form {
  .avatar-uploader {
    :deep(.el-upload) {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
    }

    :deep(.el-upload:hover) {
      border-color: var(--el-color-primary);
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 100px;
      height: 100px;
      text-align: center;
      line-height: 100px;
    }

    .avatar {
      width: 100px;
      height: 100px;
      display: block;
    }
  }
}
</style>
