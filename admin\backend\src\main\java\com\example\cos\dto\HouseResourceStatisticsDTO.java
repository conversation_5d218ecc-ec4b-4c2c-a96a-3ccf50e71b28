package com.example.cos.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 房源统计数据DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "房源统计数据")
public class HouseResourceStatisticsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "今日新增数量", example = "15")
    private Long todayNewCount;

    @ApiModelProperty(value = "正在拍卖数量", example = "25")
    private Long ongoingAuctionCount;

    @ApiModelProperty(value = "即将拍卖数量", example = "10")
    private Long upcomingAuctionCount;

    public HouseResourceStatisticsDTO() {}

    public HouseResourceStatisticsDTO(Long todayNewCount, Long ongoingAuctionCount, Long upcomingAuctionCount) {
        this.todayNewCount = todayNewCount;
        this.ongoingAuctionCount = ongoingAuctionCount;
        this.upcomingAuctionCount = upcomingAuctionCount;
    }

    public Long getTodayNewCount() {
        return todayNewCount;
    }

    public void setTodayNewCount(Long todayNewCount) {
        this.todayNewCount = todayNewCount;
    }

    public Long getOngoingAuctionCount() {
        return ongoingAuctionCount;
    }

    public void setOngoingAuctionCount(Long ongoingAuctionCount) {
        this.ongoingAuctionCount = ongoingAuctionCount;
    }

    public Long getUpcomingAuctionCount() {
        return upcomingAuctionCount;
    }

    public void setUpcomingAuctionCount(Long upcomingAuctionCount) {
        this.upcomingAuctionCount = upcomingAuctionCount;
    }

    @Override
    public String toString() {
        return "HouseResourceStatisticsDTO{" +
                "todayNewCount=" + todayNewCount +
                ", ongoingAuctionCount=" + ongoingAuctionCount +
                ", upcomingAuctionCount=" + upcomingAuctionCount +
                '}';
    }
}
