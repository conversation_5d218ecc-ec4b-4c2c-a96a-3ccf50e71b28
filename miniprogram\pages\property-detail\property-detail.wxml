<!--pages/property-detail/property-detail.wxml-->
<view class="container" style="position: relative; left: 0rpx; top: 27rpx">



  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view class="main-content" wx:elif="{{!loading && propertyInfo}}">

    <!-- 图片轮播区域 -->
    <view class="image-section">
      <swiper class="property-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" bindchange="onSwiperChange">
        <swiper-item wx:for="{{propertyInfo.images}}" wx:key="index">
          <image class="property-image" src="{{item}}" mode="aspectFill"></image>
        </swiper-item>
      </swiper>

      <!-- 图片计数器 -->
      <view class="image-counter">{{currentImageIndex + 1}}/{{propertyInfo.images.length}}</view>

      <!-- 状态标签 -->
      <view class="status-tag" wx:if="{{propertyInfo.statusText}}">{{propertyInfo.statusText}}</view>
    </view>

    <!-- 楼盘基本信息 -->
    <view class="property-info">
      <view class="property-title">{{propertyInfo.title}}</view>

      <!-- 标签区域 -->
      <view class="tags-section">
        <view class="tag tag-discount" wx:if="{{propertyInfo.discount}}">{{propertyInfo.discount}}</view>
        <view class="tag tag-new" wx:if="{{propertyInfo.newHouseText}}">{{propertyInfo.newHouseText}}</view>
        <view class="tag tag-transport" wx:if="{{propertyInfo.transportText}}">{{propertyInfo.transportText}}</view>
      </view>

      <!-- 价格信息区域 -->
      <view class="price-section">
        <view class="price-row">
          <view class="price-item">
            <view class="price-value main-price">{{propertyInfo.startPrice}}万</view>
            <view class="price-label">{{propertyInfo.startPriceLabel || '起拍价'}}</view>
          </view>
          <view class="price-item">
            <view class="price-value">{{propertyInfo.roomType}}</view>
            <view class="price-label">{{propertyInfo.roomTypeLabel || '房型'}}</view>
          </view>
          <view class="price-item" style="width: 727rpx; display: block; box-sizing: content-box">
            <view class="price-value">{{propertyInfo.area}}</view>
            <view class="price-label">{{propertyInfo.areaLabel || '建筑面积'}}</view>
          </view>
        </view>

        <view class="price-row">
          <view class="price-i">
            <view class="price-label market-price-label">{{propertyInfo.marketPriceLabel || '市场价'}}：</view>
            <view class="price-va" >{{propertyInfo.marketPrice}}万</view>
          </view>
          <view class="price-i">
            <view class="price-label saving-space-label" >{{propertyInfo.savingSpaceLabel || '拍漏空间'}}：</view>
            <view class="price-valo" >{{propertyInfo.savingSpace}}万</view>
          </view>
        </view>

        <view class="price-row">
          <view class="price-i">
            <view class="price-label market-unit-price-label" >{{propertyInfo.marketUnitPriceLabel || '市场单价'}}：</view>
            <view class="price-va" >{{propertyInfo.marketUnitPrice}}万</view>
          </view>
          <view class="price-i">
            <view class="price-label start-unit-price-label">{{propertyInfo.startUnitPriceLabel || '起拍单价'}}：</view>
            <view class="price-valo">{{propertyInfo.startUnitPrice}}天</view>
          </view>
        </view>
      </view>

      <!-- 起拍时间 -->
      <view class="auction-time">
        <text class="time-text">{{propertyInfo.auctionTimeLabel || '起拍时间'}}：{{propertyInfo.auctionTime}}</text>
      </view>

      <!-- 提示信息 -->
      <view class="notice-banner" wx:if="{{propertyInfo.noticeText}}">
        <text class="notice-text">{{propertyInfo.noticeText}}</text>
      </view>
    </view>

    <!-- 相关附件区域 -->
    <view class="attachments-section" wx:if="{{propertyInfo.evaluationReport || propertyInfo.executionOrder || propertyInfo.propertyReport}}">
      <view class="section-title">{{propertyInfo.attachmentsTitle || '相关附件'}}</view>
      <view class="attachments-list">
        <view class="attachment-item" wx:if="{{propertyInfo.evaluationReport}}" bindtap="copyAttachmentLink" data-link="{{propertyInfo.evaluationReport}}" data-name="评估报告">
          <view class="attachment-info">
            <view class="attachment-name">评估报告</view>
            <view class="attachment-desc">点击复制链接到浏览器下载</view>
          </view>
          <view class="attachment-action">复制链接</view>
        </view>
        <view class="attachment-item" wx:if="{{propertyInfo.executionOrder}}" bindtap="copyAttachmentLink" data-link="{{propertyInfo.executionOrder}}" data-name="执行裁定书">
          <view class="attachment-info">
            <view class="attachment-name">执行裁定书</view>
            <view class="attachment-desc">点击复制链接到浏览器下载</view>
          </view>
          <view class="attachment-action">复制链接</view>
        </view>
        <view class="attachment-item" wx:if="{{propertyInfo.propertyReport}}" bindtap="copyAttachmentLink" data-link="{{propertyInfo.propertyReport}}" data-name="房产报告">
          <view class="attachment-info">
            <view class="attachment-name">房产报告</view>
            <view class="attachment-desc">点击复制链接到浏览器下载</view>
          </view>
          <view class="attachment-action">复制链接</view>
        </view>
      </view>
    </view>

    <!-- 图片详情区域 -->
    <view class="image-detail-section">
      <view class="section-title">{{propertyInfo.imageDetailTitle || '图片详情(仅供选房参考)'}}</view>

      <!-- 情况调查表图 -->
      <view class="floor-plan-container" wx:if="{{propertyInfo.floorPlan}}">
        <view class="floor-plan-title">{{propertyInfo.floorPlanTitle || '情况调查表'}}</view>
        <view class="floor-plan-wrapper" bindtap="previewFloorPlan">
          <image class="floor-plan-image" src="{{propertyInfo.floorPlan}}" mode="aspectFit"></image>
          <view class="floor-plan-overlay">
            <text class="floor-plan-overlay-text">点击查看大图</text>
          </view>
        </view>
      </view>

      <!-- 实景图片 -->
      <view class="detail-images-container" wx:if="{{propertyInfo.detailImages && propertyInfo.detailImages.length > 0}}">
        <view class="detail-images-title">{{propertyInfo.detailImagesTitle || '实景图片'}}</view>
        <view class="detail-images-grid">
          <view class="detail-image-item" wx:for="{{propertyInfo.detailImages}}" wx:key="index" bindtap="previewDetailImage" data-index="{{index}}">
            <image class="detail-image" src="{{item}}" mode="aspectFill" ></image>
            <view class="detail-image-overlay">
             
            </view>
          </view>
        </view>
      </view>

      <!-- 楼盘位置 -->
      <view class="location-section">
        <view class="section-title">{{propertyInfo.locationTitle || '楼盘位置'}}</view>

        <!-- 地图区域 -->
        <view class="map-container" bindtap="previewMapImage">
          <image class="map-image" src="{{propertyInfo.mapImage || '/images/map.png'}}" mode="aspectFit"></image>
          <view class="map-overlay">
            <text class="map-overlay-text">点击查看大图</text>
          </view>
        </view>
      </view>

    </view>

  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:elif="{{!loading && error}}">
    <view class="error-icon">⚠️</view>
    <view class="error-title">加载失败</view>
    <view class="error-desc">{{errorMessage}}</view>
    <button class="error-btn" bindtap="loadPropertyDetail">重新加载</button>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar" wx:if="{{!loading && propertyInfo}}">
    <view class="bottom-left">
      <view class="contact-item {{isFollowed ? 'followed' : ''}}" bindtap="toggleFollow">
        <image class="contact-icon" src="/images/favorites.png" mode="aspectFit"></image>
        <text class="contact-text">{{isFollowed ? '已关注' : '关注'}}</text>
      </view>
      <view class="contact-item {{isFavorite ? 'favorited' : ''}}" bindtap="toggleFavorite">
        <image class="contact-icon" src="/images/follow.png" mode="aspectFit"></image>
        <text class="contact-text">{{isFavorite ? '已收藏' : '收藏'}}</text>
      </view>
    </view>
    <view class="bottom-right">
      <button class="consult-btn" bindtap="showConsultModal" style="width: 268rpx; display: block; box-sizing: border-box; left: 52rpx; top: -11rpx; position: relative">电话咨询</button>
    </view>
  </view>

</view>
