// pages/house-list/house-list.js
const api = require('../../config/api.js')
const userService = require('../../services/user.js')

Page({
  data: {
    // 房源列表数据
    houseList: [],
    // 页面类型和标题
    pageType: '',
    pageTitle: '房源列表',
    // 分页相关
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
    hasMore: true,
    loading: false,
    // 筛选面板显示状态
    showRegionPanel: false,
    showAreaPanel: false,
    showPricePanel: false,
    showMorePanel: false,
    showSortPanel: false,
    // 当前筛选条件
    regionFilter: 'all',
    areaFilter: 'all',
    priceFilter: 'all',
    auctionType: 'all',
    auctionStatus: 'all',
    sortType: 'smart',
    customMinPrice: '',
    customMaxPrice: '',
    // 临时筛选条件（用于面板中的选择）
    tempRegionFilter: 'all',
    tempAreaFilter: 'all',
    tempPriceFilter: 'all',
    tempAuctionType: 'all',
    tempAuctionStatus: 'all',
    tempSortType: 'smart',
    // 筛选文本显示
    regionFilterText: '区域',
    areaFilterText: '面积',
    priceFilterText: '价格',
    sortFilterText: '智能排序',
    // 区域选项
    currentRegionOptions: ['渝中区', '江北区', '南岸区', '九龙坡区', '沙坪坝区', '大渡口区', '渝北区', '巴南区', '北碚区', '綦江区', '大足区', '长寿区', '江津区', '合川区', '永川区', '南川区', '璧山区', '铜梁区', '潼南区', '荣昌区']
  },

  onLoad: function (options) {
    // 根据传入的类型设置页面标题
    const type = options.type || options.filterType || 'default';
    const searchKeyword = options.search || '';
    let pageTitle = '房源列表';

    // 支持新的filterType参数
    switch (type) {
      case 'house':
      case 'auction-house':
        pageTitle = '法拍住宅';
        break;
      case 'commercial':
      case 'auction-commercial':
        pageTitle = '法拍商办';
        break;
      case 'special':
      case 'special-assets':
        pageTitle = '特殊资产';
        break;
      case 'premium':
      case 'premium-house':
        pageTitle = '精选房源';
        break;
      case 'user-follows':
        pageTitle = '我的关注';
        break;
      case 'user-favorites':
        pageTitle = '我的收藏';
        break;
      default:
        pageTitle = searchKeyword ? '搜索结果' : '房源列表';
        break;
    }

    this.setData({
      pageType: type,
      pageTitle: pageTitle,
      searchKeyword: searchKeyword
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: pageTitle
    });

    // 页面加载时初始化数据
    this.loadHouseList(true);
  },

  onShow: function () {
    // 页面显示时刷新数据（从详情页返回时）
    // this.loadHouseList(true);
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.loadHouseList(true);
  },

  onReachBottom: function () {
    // 上拉加载更多
    this.loadMore();
  },

  // 加载房源列表
  loadHouseList: function (refresh = false) {
    if (this.data.loading) return;

    this.setData({
      loading: true
    });

    // 如果是刷新，重置分页
    if (refresh) {
      this.setData({
        currentPage: 1,
        houseList: [],
        hasMore: true
      });
    }

    // 根据页面类型选择不同的API
    let apiUrl = '';
    let queryParams = {};

    switch (this.data.pageType) {
      case 'house':
      case 'auction-house':
        // 法拍住宅：调用按房源类型查询API
        apiUrl = `${api.API.HOUSE_BY_TYPE}?houseType=${encodeURIComponent('住宅')}`;
        break;
      case 'commercial':
      case 'auction-commercial':
        // 法拍商办：调用按房源类型查询API
        apiUrl = `${api.API.HOUSE_BY_TYPE}?houseType=${encodeURIComponent('商办')}`;
        break;
      case 'special':
      case 'special-assets':
        // 特殊资产：调用按特殊商品查询API
        apiUrl = `${api.API.HOUSE_BY_SPECIAL}/1`;
        break;
      case 'premium':
      case 'premium-house':
        // 精选房源：调用按精选商品查询API
        apiUrl = `${api.API.HOUSE_BY_FEATURED}/1`;
        break;
      case 'user-follows':
        // 用户关注列表：调用获取关注列表API
        const userId = wx.getStorageSync('userId') || 1;
        apiUrl = `${api.API.USER_FOLLOWS}/${userId}/follows`;
        break;
      case 'user-favorites':
        // 用户收藏列表：调用获取收藏列表API
        const userIdFav = wx.getStorageSync('userId') || 1;
        apiUrl = `${api.API.USER_FAVORITES}/${userIdFav}/favorites`;
        break;
      default:
        // 默认或搜索：使用通用列表API
        queryParams = {
          pageNum: this.data.currentPage,
          pageSize: this.data.pageSize
        };

        // 添加搜索关键词
        if (this.data.searchKeyword) {
          queryParams.title = this.data.searchKeyword;
        }

        // 添加筛选条件
        if (this.data.priceFilter) {
          const priceRange = this.parsePriceFilter(this.data.priceFilter);
          if (priceRange.min) queryParams.minStartingPrice = priceRange.min;
          if (priceRange.max) queryParams.maxStartingPrice = priceRange.max;
        }

        // 构建URL查询参数
        const queryString = Object.keys(queryParams)
          .filter(key => queryParams[key] !== '' && queryParams[key] !== null && queryParams[key] !== undefined)
          .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
          .join('&');

        apiUrl = queryString ? `${api.API.HOUSE_LIST}?${queryString}` : api.API.HOUSE_LIST;
        break;
    }

    // 调用真实API
    console.log('调用API URL:', api.baseUrl + apiUrl);
    console.log('页面类型:', this.data.pageType);

    api.get(apiUrl)
      .then(res => {
        console.log('API响应完整数据:', res);
        console.log('响应数据类型:', typeof res);

        // 根据API文档，响应格式为 {code: 200, data: [], message: "操作成功", timestamp: 1640995200000}
        let houses = [];
        let total = 0;

        // 检查响应是否成功
        if (res.code === 200 && res.data) {
          console.log('API调用成功，数据类型:', typeof res.data);

          // 根据API文档，data字段直接是房源数组
          if (Array.isArray(res.data)) {
            console.log('使用标准API响应格式，房源数量:', res.data.length);
            houses = res.data;
            total = houses.length;
          }
          // 如果data是分页对象
          else if (res.data.records && Array.isArray(res.data.records)) {
            console.log('使用分页响应格式，records长度:', res.data.records.length);
            houses = res.data.records;
            total = res.data.total || houses.length;
          }
          // 如果data是单个对象
          else if (typeof res.data === 'object' && res.data.id) {
            console.log('data是单个房源对象');
            houses = [res.data];
            total = 1;
          }
          else {
            console.log('未知的data格式:', res.data);
            houses = [];
            total = 0;
          }
        }
        else {
          console.log('API响应失败，code:', res.code, 'message:', res.message);
          houses = [];
          total = 0;
        }

        console.log('最终处理的房源数据:', houses);
        console.log('房源数据数量:', houses.length);

        if (houses.length > 0) {
          const formattedHouses = houses.map(house => this.formatHouseData(house));
          const newList = refresh ? formattedHouses : [...this.data.houseList, ...formattedHouses];

          this.setData({
            houseList: newList,
            totalCount: total,
            hasMore: newList.length < total,
            loading: false,
            currentPage: this.data.currentPage + 1
          });

          console.log('房源列表更新成功，共', newList.length, '条数据');

        } else {
          // 如果没有数据，根据页面类型显示不同提示
          console.log('API返回空数据');

          if (this.data.pageType === 'user-follows') {
            wx.showToast({
              title: '暂无关注的房源',
              icon: 'none',
              duration: 2000
            });
            this.setData({
              houseList: [],
              totalCount: 0,
              hasMore: false,
              loading: false
            });
          } else if (this.data.pageType === 'user-favorites') {
            wx.showToast({
              title: '暂无收藏的房源',
              icon: 'none',
              duration: 2000
            });
            this.setData({
              houseList: [],
              totalCount: 0,
              hasMore: false,
              loading: false
            });
          } else {
            // 其他类型也显示空状态
            console.log('API返回空数据，显示空状态');
            wx.showToast({
              title: '暂无房源数据',
              icon: 'none',
              duration: 2000
            });
            this.setData({
              houseList: [],
              totalCount: 0,
              hasMore: false,
              loading: false
            });
          }
        }

        // 停止下拉刷新
        if (refresh) {
          wx.stopPullDownRefresh();
        }
      })
      .catch(err => {
        console.error('加载房源列表失败:', err);
        this.setData({
          loading: false
        });

        // API失败时显示空状态
        console.log('API调用失败，显示空状态');
        wx.showToast({
          title: 'API调用失败',
          icon: 'none',
          duration: 2000
        });
        this.setData({
          houseList: [],
          totalCount: 0,
          hasMore: false
        });

        // 停止下拉刷新
        if (refresh) {
          wx.stopPullDownRefresh();
        }
      });
  },



  // 格式化房源数据
  formatHouseData: function (house) {
    console.log('格式化房源数据:', house);

    // 安全的价格转换
    const startingPrice = house.startingPrice || 0;
    const evaluationPrice = house.evaluationPrice || 0;

    return {
      id: house.id,
      title: house.title || '房源标题',
      location: house.title || '位置信息', // 使用标题作为位置信息
      area: house.auctionType || '拍卖类型', // 使用拍卖类型替代面积
      roomType: house.houseType || '户型',
      floor: house.biddingCycle ? `${house.biddingCycle}天` : '竞价周期', // 使用竞价周期
      startPrice: (startingPrice / 10000).toFixed(1), // 转换为万元
      marketPrice: (evaluationPrice / 10000).toFixed(1), // 转换为万元
      image: this.getHouseImage(house.detailImageList),
      status: this.getHouseStatus(house),
      tags: this.getHouseTags(house),
      auctionTime: house.startTime || '',
      isFavorite: false,
      houseType: house.houseType,
      isSpecial: house.isSpecial,
      isFeatured: house.isFeatured
    };
  },

  // 获取房源图片（使用第二张图片）
  getHouseImage: function(detailImageList) {
    if (detailImageList) {
      const images = detailImageList.split(',').filter(img => img.trim());
      if (images.length > 1) {
        return images[1].trim(); // 使用第二张图片
      } else if (images.length > 0) {
        return images[0].trim(); // 如果只有一张图片，使用第一张
      }
    }
    return '/images/aaa.png';
  },

  // 获取房源状态
  getHouseStatus: function (house) {
    const now = new Date();
    const startTime = new Date(house.startTime);
    const endTime = new Date(house.endTime);

    if (now < startTime) {
      return '即将开拍';
    } else if (now >= startTime && now <= endTime) {
      return '拍卖中';
    } else {
      return '已结束';
    }
  },

  // 获取房源标签
  getHouseTags: function (house) {
    const tags = [];
    if (house.isSpecial === 1) tags.push('特殊资产');
    if (house.isFeatured === 1) tags.push('精选房源');
    if (house.houseType) tags.push(house.houseType);
    return tags;
  },

  // 解析价格筛选条件
  parsePriceFilter: function (priceFilter) {
    const result = { min: null, max: null };

    if (priceFilter.includes('-')) {
      const parts = priceFilter.split('-');
      if (parts[0] && parts[0] !== '不限') {
        result.min = parseFloat(parts[0]) * 10000; // 转换为元
      }
      if (parts[1] && parts[1] !== '不限') {
        result.max = parseFloat(parts[1]) * 10000; // 转换为元
      }
    } else if (priceFilter.includes('以上')) {
      result.min = parseFloat(priceFilter.replace('万以上', '')) * 10000;
    } else if (priceFilter.includes('以下')) {
      result.max = parseFloat(priceFilter.replace('万以下', '')) * 10000;
    }

    return result;
  },



  // 加载更多
  loadMore: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.loadHouseList(false);
    }
  },

  // 刷新列表
  refreshList: function () {
    this.loadHouseList(true);
  },

  // 排序改变
  onSortChange: function (e) {
    const sortIndex = parseInt(e.detail.value);
    this.setData({
      sortIndex: sortIndex
    });
    
    // 根据排序重新加载数据
    this.applySortAndFilter();
  },

  // 应用排序和筛选
  applySortAndFilter: function () {
    let sortedList = [...this.data.houseList];
    
    // 应用排序
    switch (this.data.sortIndex) {
      case 1: // 价格从低到高
        sortedList.sort((a, b) => parseFloat(a.startPrice) - parseFloat(b.startPrice));
        break;
      case 2: // 价格从高到低
        sortedList.sort((a, b) => parseFloat(b.startPrice) - parseFloat(a.startPrice));
        break;
      case 3: // 面积从小到大
        sortedList.sort((a, b) => a.area - b.area);
        break;
      case 4: // 面积从大到小
        sortedList.sort((a, b) => b.area - a.area);
        break;
      default: // 默认排序
        break;
    }

    this.setData({
      houseList: sortedList
    });
  },

  // 显示筛选弹窗
  showFilterModal: function () {
    this.setData({
      showFilterModal: true,
      tempPriceFilter: this.data.priceFilter,
      tempAreaFilter: this.data.areaFilter
    });
  },

  // 隐藏筛选弹窗
  hideFilterModal: function () {
    this.setData({
      showFilterModal: false,
      // 关闭筛选状态
      priceFilter: '',
      sizeFilter: ''
    });
  },

  // 选择价格筛选
  selectPriceFilter: function (e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      tempPriceFilter: value
    });
  },

  // 选择面积筛选
  selectAreaFilter: function (e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      tempAreaFilter: value
    });
  },

  // 重置筛选
  resetFilter: function () {
    this.setData({
      tempPriceFilter: '',
      tempAreaFilter: ''
    });
  },

  // 确认筛选
  confirmFilter: function () {
    this.setData({
      priceFilter: this.data.tempPriceFilter,
      areaFilter: this.data.tempAreaFilter,
      showFilterModal: false,
      // 关闭筛选状态
      sizeFilter: ''
    });

    // 应用筛选条件重新加载数据
    this.loadHouseList(true);
  },

  // 切换收藏状态
  toggleFavorite: function (e) {
    const id = e.currentTarget.dataset.id;
    const houseList = this.data.houseList.map(house => {
      if (house.id === id) {
        return { ...house, isFavorite: !house.isFavorite };
      }
      return house;
    });

    this.setData({
      houseList: houseList
    });

    // 显示提示
    const house = houseList.find(h => h.id === id);
    wx.showToast({
      title: house.isFavorite ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1500
    });
  },

  // 跳转到房源详情
  goToDetail: function (e) {
    const id = e.currentTarget.dataset.id;

    // 使用统一的登录状态检查方法（与收藏和关注保持一致）
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      // 延迟显示登录提示，避免立即弹窗
      setTimeout(() => {
        wx.showModal({
          title: '需要登录',
          content: '查看房源详情需要先登录，是否现在去登录？',
          showCancel: true,
          cancelText: '取消',
          confirmText: '去登录',
          confirmColor: '#007aff',
          success: (res) => {
            if (res.confirm) {
              // 跳转到profile页面进行登录
              wx.switchTab({
                url: '/pages/profile/profile'
              });
            }
          }
        });
      }, 100)
      return;
    }

    // 检查用户权限（从全局数据的userInfo中获取role）
    const app = getApp();
    const userInfo = app.globalData.userInfo || userService.getLocalUserInfo();
    const userRole = userInfo ? (userInfo.role || 0) : 0;
    console.log('用户权限检查:', { userRole, userInfo });

    // 已登录但role为0时，显示权限提示
    if (userRole === 0) {
      wx.showModal({
        title: '权限提示',
        content: '请联系您的法拍顾问查看房屋更多信息',
        showCancel: false,
        confirmText: '知道了',
        confirmColor: '#007aff'
      });
      return;
    }

    // 用户已登录且role为1时，正常跳转
    wx.navigateTo({
      url: `/pages/property-detail/property-detail?id=${id}`,
      success: () => {
        console.log('成功跳转到房源详情页面');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 筛选导航栏点击事件
  showAreaFilter: function () {
    this.toggleFilter('areaFilter');
    wx.showToast({
      title: '区域筛选功能开发中',
      icon: 'none'
    });
  },

  showSizeFilter: function () {
    this.toggleFilter('sizeFilter');
    this.showFilterModal();
  },

  showPriceFilter: function () {
    this.toggleFilter('priceFilter');
    this.showFilterModal();
  },

  showMoreFilter: function () {
    this.toggleFilter('moreFilter');
    wx.showToast({
      title: '更多筛选功能开发中',
      icon: 'none'
    });
  },

  showSortFilter: function () {
    this.toggleFilter('sortFilter');
    this.showSortModal();
  },

  // 切换筛选状态
  toggleFilter: function (filterType) {
    // 先关闭所有筛选状态
    this.setData({
      areaFilter: '',
      sizeFilter: '',
      priceFilter: '',
      moreFilter: '',
      sortFilter: ''
    });

    // 然后激活当前筛选
    this.setData({
      [filterType]: 'active'
    });
  },

  // 显示排序弹窗
  showSortModal: function () {
    const that = this;
    wx.showActionSheet({
      itemList: this.data.sortOptions,
      success: function (res) {
        that.setData({
          sortIndex: res.tapIndex,
          sortFilter: '' // 选择后关闭筛选状态
        });
        that.applySortAndFilter();
      },
      fail: function () {
        that.setData({
          sortFilter: '' // 取消后关闭筛选状态
        });
      }
    });
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    // 空函数，用于阻止事件冒泡
  },

  // ========== 筛选相关方法 ==========

  /**
   * 显示区域筛选面板
   */
  showRegionFilter() {
    this.setData({
      showRegionPanel: true,
      tempRegionFilter: this.data.regionFilter
    });
  },

  /**
   * 隐藏区域筛选面板
   */
  hideRegionFilter() {
    this.setData({
      showRegionPanel: false
    });
  },

  /**
   * 临时区域筛选点击
   */
  onTempRegionFilterTap(e) {
    const region = e.currentTarget.dataset.region;
    this.setData({
      tempRegionFilter: region
    });
  },

  /**
   * 重置区域筛选
   */
  resetRegionFilter() {
    this.setData({
      tempRegionFilter: 'all'
    });
  },

  /**
   * 确认区域筛选
   */
  confirmRegionFilter() {
    const regionText = this.data.tempRegionFilter === 'all' ? '区域' : this.data.tempRegionFilter;
    this.setData({
      regionFilter: this.data.tempRegionFilter,
      regionFilterText: regionText,
      showRegionPanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 显示面积筛选面板
   */
  showAreaFilter() {
    this.setData({
      showAreaPanel: true,
      tempAreaFilter: this.data.areaFilter
    });
  },

  /**
   * 隐藏面积筛选面板
   */
  hideAreaFilter() {
    this.setData({
      showAreaPanel: false
    });
  },

  /**
   * 临时面积筛选点击
   */
  onTempAreaFilterTap(e) {
    const area = e.currentTarget.dataset.area;
    this.setData({
      tempAreaFilter: area
    });
  },

  /**
   * 重置面积筛选
   */
  resetAreaFilter() {
    this.setData({
      tempAreaFilter: 'all'
    });
  },

  /**
   * 确认面积筛选
   */
  confirmAreaFilter() {
    const areaText = this.getAreaText(this.data.tempAreaFilter);
    this.setData({
      areaFilter: this.data.tempAreaFilter,
      areaFilterText: areaText,
      showAreaPanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 获取面积文本
   */
  getAreaText(areaFilter) {
    const areaMap = {
      'all': '面积',
      '0-50': '50㎡以下',
      '50-70': '50~70㎡',
      '70-90': '70~90㎡',
      '90-120': '90~120㎡',
      '120-150': '120~150㎡',
      '150-200': '150~200㎡',
      '200-300': '200~300㎡',
      '300+': '300㎡以上'
    };
    return areaMap[areaFilter] || '面积';
  },

  /**
   * 显示价格筛选面板
   */
  showPriceFilter() {
    this.setData({
      showPricePanel: true,
      tempPriceFilter: this.data.priceFilter
    });
  },

  /**
   * 隐藏价格筛选面板
   */
  hidePriceFilter() {
    this.setData({
      showPricePanel: false
    });
  },

  /**
   * 临时价格筛选点击
   */
  onTempPriceFilterTap(e) {
    const price = e.currentTarget.dataset.price;
    this.setData({
      tempPriceFilter: price
    });
  },

  /**
   * 重置价格筛选
   */
  resetPriceFilter() {
    this.setData({
      tempPriceFilter: 'all',
      customMinPrice: '',
      customMaxPrice: ''
    });
  },

  /**
   * 确认价格筛选
   */
  confirmPriceFilter() {
    let priceFilter = this.data.tempPriceFilter;

    // 如果有自定义价格，使用自定义价格
    if (this.data.customMinPrice || this.data.customMaxPrice) {
      const min = this.data.customMinPrice || '0';
      const max = this.data.customMaxPrice || '999999';
      priceFilter = `${min}-${max}`;
    }

    const priceText = this.getPriceText(priceFilter);
    this.setData({
      priceFilter: priceFilter,
      priceFilterText: priceText,
      showPricePanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 获取价格文本
   */
  getPriceText(priceFilter) {
    const priceMap = {
      'all': '价格',
      '0-50': '50万以下',
      '50-100': '50~100万',
      '100-150': '100~150万',
      '150-200': '150~200万',
      '200-300': '200~300万',
      '300-400': '300~400万',
      '400-500': '400~500万',
      '500-700': '500~700万',
      '700-1000': '700~1000万',
      '1000+': '1000万以上'
    };

    if (priceMap[priceFilter]) {
      return priceMap[priceFilter];
    }

    // 自定义价格区间
    if (priceFilter.includes('-')) {
      const [min, max] = priceFilter.split('-');
      return `${min}~${max}万`;
    }

    return '价格';
  },

  /**
   * 最低价格输入
   */
  onMinPriceInput(e) {
    this.setData({
      customMinPrice: e.detail.value
    });
  },

  /**
   * 最高价格输入
   */
  onMaxPriceInput(e) {
    this.setData({
      customMaxPrice: e.detail.value
    });
  },

  /**
   * 阻止事件冒泡
   */
  preventClose() {
    // 阻止事件冒泡，防止弹窗关闭
  },

  /**
   * 显示更多筛选面板
   */
  showMoreFilter() {
    this.setData({
      showMorePanel: true,
      tempAuctionType: this.data.auctionType,
      tempAuctionStatus: this.data.auctionStatus
    });
  },

  /**
   * 隐藏更多筛选面板
   */
  hideMoreFilter() {
    this.setData({
      showMorePanel: false
    });
  },

  /**
   * 临时拍卖方式筛选点击
   */
  onTempAuctionTypeTap(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      tempAuctionType: type
    });
  },

  /**
   * 临时拍卖状态筛选点击
   */
  onTempAuctionStatusTap(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      tempAuctionStatus: status
    });
  },

  /**
   * 重置更多筛选
   */
  resetMoreFilter() {
    this.setData({
      tempAuctionType: 'all',
      tempAuctionStatus: 'all'
    });
  },

  /**
   * 确认更多筛选
   */
  confirmMoreFilter() {
    this.setData({
      auctionType: this.data.tempAuctionType,
      auctionStatus: this.data.tempAuctionStatus,
      showMorePanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 显示排序筛选面板
   */
  showSortFilter() {
    this.setData({
      showSortPanel: true,
      tempSortType: this.data.sortType
    });
  },

  /**
   * 隐藏排序筛选面板
   */
  hideSortFilter() {
    this.setData({
      showSortPanel: false
    });
  },

  /**
   * 临时排序类型点击
   */
  onTempSortTypeTap(e) {
    const sort = e.currentTarget.dataset.sort;
    this.setData({
      tempSortType: sort
    });
  },

  /**
   * 重置排序筛选
   */
  resetSortFilter() {
    this.setData({
      tempSortType: 'smart'
    });
  },

  /**
   * 确认排序筛选
   */
  confirmSortFilter() {
    const sortText = this.getSortText(this.data.tempSortType);
    this.setData({
      sortType: this.data.tempSortType,
      sortFilterText: sortText,
      showSortPanel: false
    });
    this.loadHouseList(true);
  },

  /**
   * 获取排序文本
   */
  getSortText(sortType) {
    const sortMap = {
      'smart': '智能排序',
      'latest': '最新发布',
      'price-asc': '总价从低到高',
      'price-desc': '总价从高到低',
      'unit-price-asc': '单价从低到高',
      'unit-price-desc': '单价从高到低',
      'area-desc': '面积从大到小',
      'area-asc': '面积从小到大',
      'time-asc': '起拍时间由近到远'
    };
    return sortMap[sortType] || '智能排序';
  }
});
