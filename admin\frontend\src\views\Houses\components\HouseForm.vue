<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="120px"
    class="house-form"
  >
    <el-form-item label="房源标题" prop="title">
      <el-input v-model="form.title" placeholder="请输入房源标题" />
    </el-form-item>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="拍卖状态" prop="auctionStatus">
          <el-select v-model="form.auctionStatus" placeholder="请选择拍卖状态" style="width: 100%">
            <el-option label="未开拍" :value="0" />
            <el-option label="一拍中" :value="1" />
            <el-option label="二拍中" :value="2" />
            <el-option label="变卖中" :value="3" />
            <el-option label="已结束" :value="4" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="户型" prop="houseType">
          <el-input v-model="form.houseType" placeholder="请输入户型，如：3室2厅1卫" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="小区名称" prop="communityName">
          <el-input v-model="form.communityName" placeholder="请输入小区名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="建筑面积" prop="buildingArea">
          <el-input-number
            v-model="form.buildingArea"
            :min="0"
            :precision="2"
            placeholder="请输入建筑面积"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="起拍价" prop="startingPrice">
          <el-input-number
            v-model="form.startingPrice"
            :min="0"
            :precision="2"
            placeholder="请输入起拍价"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="评估价" prop="evaluationPrice">
          <el-input-number
            v-model="form.evaluationPrice"
            :min="0"
            :precision="2"
            placeholder="请输入评估价"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="楼层" prop="floor">
          <el-input v-model="form.floor" placeholder="请输入楼层，如：15/30" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="建筑年份" prop="constructionYear">
          <el-input-number
            v-model="form.constructionYear"
            :min="1900"
            :max="2100"
            placeholder="请输入建筑年份"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="保证金" prop="deposit">
          <el-input-number
            v-model="form.deposit"
            :min="0"
            :precision="2"
            placeholder="请输入保证金"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="加价幅度" prop="priceIncrement">
          <el-input-number
            v-model="form.priceIncrement"
            :min="0"
            :precision="2"
            placeholder="请输入加价幅度"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="拍卖次数" prop="auctionTimes">
          <el-input-number
            v-model="form.auctionTimes"
            :min="1"
            placeholder="请输入拍卖次数"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="梯部类型" prop="stairsType">
          <el-radio-group v-model="form.stairsType">
            <el-radio :label="0">楼梯</el-radio>
            <el-radio :label="1">电梯</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="物业类型" prop="propertyType">
          <el-radio-group v-model="form.propertyType">
            <el-radio :label="0">低层</el-radio>
            <el-radio :label="1">中层</el-radio>
            <el-radio :label="2">高层</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="装修情况" prop="decoration">
          <el-radio-group v-model="form.decoration">
            <el-radio :label="0">毛坯</el-radio>
            <el-radio :label="1">简装</el-radio>
            <el-radio :label="2">精装</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="房屋类型" prop="houseCategory">
          <el-radio-group v-model="form.houseCategory">
            <el-radio :label="0">住宅</el-radio>
            <el-radio :label="1">商办</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="是否精选" prop="isSelected">
          <el-radio-group v-model="form.isSelected">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="特殊房屋" prop="isSpecial">
          <el-radio-group v-model="form.isSpecial">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="起拍时间" prop="startTime">
          <el-date-picker
            v-model="form.startTime"
            type="datetime"
            placeholder="选择起拍时间"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            v-model="form.endTime"
            type="datetime"
            placeholder="选择结束时间"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="经度" prop="longitude">
          <el-input-number
            v-model="form.longitude"
            :min="-180"
            :max="180"
            :precision="6"
            placeholder="请输入经度"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="纬度" prop="latitude">
          <el-input-number
            v-model="form.latitude"
            :min="-90"
            :max="90"
            :precision="6"
            placeholder="请输入纬度"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="原链接" prop="originalUrl">
      <el-input v-model="form.originalUrl" placeholder="请输入原链接" />
    </el-form-item>

    <el-form-item label="房屋标签" prop="tags">
      <el-input v-model="form.tags" placeholder="请输入房屋标签，多个标签用逗号分隔" />
    </el-form-item>

    <el-form-item label="房屋图片" prop="imageUrls">
      <el-upload
        class="upload-demo"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :before-upload="beforeUpload"
        :file-list="fileList"
        list-type="picture-card"
        multiple
        :limit="9"
        :on-exceed="handleExceed"
      >
        <el-icon><Plus /></el-icon>
        <template #tip>
          <div class="el-upload__tip">
            支持jpg/png格式，单个文件不超过10MB，最多上传9张图片
          </div>
        </template>
      </el-upload>
      <el-input
        v-model="form.imageUrls"
        type="textarea"
        :rows="2"
        placeholder="图片链接（自动生成，也可手动输入）"
        style="margin-top: 10px;"
      />
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive, watch, defineProps, defineEmits, computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  houseData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'cancel'])

const formRef = ref()

// 上传相关
const fileList = ref([])
const uploadUrl = computed(() => {
  const baseAPI = import.meta.env.VITE_APP_BASE_API || '/api'
  return baseAPI + '/api/cos/upload'
})
const uploadHeaders = computed(() => {
  const token = localStorage.getItem('token')
  return token ? { 'Authorization': `Bearer ${token}` } : {}
})

// 表单数据
const form = reactive({
  id: null,
  title: '',
  auctionStatus: 0,
  houseType: '',
  communityName: '',
  buildingArea: null,
  floor: '',
  constructionYear: null,
  startingPrice: null,
  evaluationPrice: null,
  deposit: null,
  priceIncrement: null,
  auctionTimes: 1,
  stairsType: 0,
  propertyType: 0,
  decoration: 0,
  houseCategory: 0,
  isSelected: 0,
  isSpecial: 0,
  startTime: '',
  endTime: '',
  longitude: null,
  latitude: null,
  originalUrl: '',
  tags: '',
  imageUrls: ''
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入房源标题', trigger: 'blur' },
    { max: 255, message: '标题长度不能超过255个字符', trigger: 'blur' }
  ],
  originalUrl: [
    { required: true, message: '请输入原链接', trigger: 'blur' },
    { max: 512, message: '原链接长度不能超过512个字符', trigger: 'blur' }
  ],
  // 其他字段为可选，只做格式验证
  houseType: [
    { max: 50, message: '户型长度不能超过50个字符', trigger: 'blur' }
  ],
  communityName: [
    { max: 100, message: '小区名称长度不能超过100个字符', trigger: 'blur' }
  ],
  floor: [
    { max: 20, message: '楼层长度不能超过20个字符', trigger: 'blur' }
  ],
  tags: [
    { max: 255, message: '标签长度不能超过255个字符', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.houseData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(form, newData)
  }
}, { immediate: true, deep: true })

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    emit('submit', { ...form })
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 取消
const handleCancel = () => {
  emit('cancel')
}

// 上传相关方法
const beforeUpload = (file) => {
  const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isJPGOrPNG) {
    ElMessage.error('只能上传 JPG/PNG 格式的图片!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response, file) => {
  if (response.code === 200) {
    ElMessage.success('图片上传成功')

    // 将新上传的图片URL添加到imageUrls中
    const newImageUrl = response.data.fileUrl
    const currentUrls = form.imageUrls ? form.imageUrls.split(',').filter(url => url.trim()) : []
    currentUrls.push(newImageUrl)
    form.imageUrls = currentUrls.join(',')

    // 更新文件列表显示
    updateFileList()
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

const handleUploadError = (error) => {
  console.error('图片上传失败:', error)
  ElMessage.error('图片上传失败，请重试')
}

const handleExceed = (files, fileList) => {
  ElMessage.warning(`最多只能上传9张图片，当前选择了${files.length}张图片，已有${fileList.length}张图片`)
}

// 更新文件列表显示
const updateFileList = () => {
  if (form.imageUrls) {
    const urls = form.imageUrls.split(',').filter(url => url.trim())
    fileList.value = urls.map((url, index) => ({
      name: `image-${index + 1}`,
      url: url.trim(),
      uid: Date.now() + index
    }))
  } else {
    fileList.value = []
  }
}

// 监听imageUrls变化，更新文件列表
watch(() => form.imageUrls, () => {
  updateFileList()
}, { immediate: true })

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
  fileList.value = []
  Object.assign(form, {
    id: null,
    auctionType: '',
    title: '',
    startingPrice: null,
    evaluationPrice: null,
    priceIncrement: null,
    biddingCycle: null,
    evaluationReport: '',
    executionOrder: '',
    propertyReport: '',
    mapInfo: '',
    situationSurvey: '',
    startTime: '',
    endTime: '',
    detailImageList: '',
    houseType: '',
    sourceUrl: '',
    isSpecial: 0,
    isFeatured: 0,
    imageUrls: ''
  })
}

// 暴露方法给父组件
defineExpose({
  resetForm
})
</script>

<style lang="scss" scoped>
.house-form {
  .upload-demo {
    :deep(.el-upload--picture-card) {
      width: 100px;
      height: 100px;
    }
  }
}
</style>
