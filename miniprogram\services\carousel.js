/**
 * 轮播图服务
 */

const api = require('../config/api.js')

/**
 * 获取轮播图列表
 * @returns {Promise} 轮播图列表
 */
const getCarousels = () => {
  return new Promise((resolve, reject) => {
    console.log('获取轮播图列表')
    
    api.get(api.API.MINIPROGRAM_CAROUSELS)
      .then(response => {
        console.log('轮播图列表响应:', response)
        
        if (response.code === 200) {
          const carousels = response.data || []
          console.log('轮播图列表获取成功，数量:', carousels.length)
          resolve(carousels)
        } else {
          console.error('获取轮播图列表失败:', response.message)
          reject(new Error(response.message || '获取轮播图列表失败'))
        }
      })
      .catch(error => {
        console.error('获取轮播图列表异常:', error)
        reject(error)
      })
  })
}

/**
 * 处理轮播图点击事件
 * @param {Object} carousel 轮播图对象
 */
const handleCarouselClick = (carousel) => {
  console.log('轮播图点击:', carousel)
  
  if (!carousel || !carousel.jumpUrl) {
    console.log('轮播图无跳转链接')
    return
  }
  
  const jumpUrl = carousel.jumpUrl.trim()
  if (!jumpUrl) {
    console.log('轮播图跳转链接为空')
    return
  }
  
  try {
    // 判断是否为外部链接
    if (jumpUrl.startsWith('http://') || jumpUrl.startsWith('https://')) {
      // 外部链接，使用web-view打开
      wx.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(jumpUrl)}`
      })
    } else if (jumpUrl.startsWith('/')) {
      // 内部页面链接
      wx.navigateTo({
        url: jumpUrl,
        fail: (error) => {
          console.error('页面跳转失败:', error)
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
        }
      })
    } else {
      console.warn('不支持的跳转链接格式:', jumpUrl)
      wx.showToast({
        title: '链接格式不支持',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('处理轮播图跳转异常:', error)
    wx.showToast({
      title: '跳转失败',
      icon: 'none'
    })
  }
}

/**
 * 预加载轮播图
 * @param {Array} carousels 轮播图列表
 */
const preloadCarouselImages = (carousels) => {
  if (!Array.isArray(carousels) || carousels.length === 0) {
    return
  }
  
  console.log('预加载轮播图，数量:', carousels.length)
  
  carousels.forEach((carousel, index) => {
    if (carousel.imageUrl) {
      wx.getImageInfo({
        src: carousel.imageUrl,
        success: () => {
          console.log(`轮播图${index + 1}预加载成功:`, carousel.imageUrl)
        },
        fail: (error) => {
          console.warn(`轮播图${index + 1}预加载失败:`, carousel.imageUrl, error)
        }
      })
    }
  })
}

/**
 * 格式化轮播图数据
 * @param {Array} carousels 原始轮播图数据
 * @returns {Array} 格式化后的轮播图数据
 */
const formatCarousels = (carousels) => {
  if (!Array.isArray(carousels)) {
    return []
  }
  
  return carousels.map(carousel => ({
    id: carousel.id,
    imageUrl: carousel.imageUrl,
    jumpUrl: carousel.jumpUrl || '',
    remark: carousel.remark || '',
    sortOrder: carousel.sortOrder || 0
  }))
}

module.exports = {
  getCarousels,
  handleCarouselClick,
  preloadCarouselImages,
  formatCarousels
}
