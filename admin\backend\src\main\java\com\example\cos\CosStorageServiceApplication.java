package com.example.cos;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 腾讯云COS存储服务启动类
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *     http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * 
 * Copyright 2019-Knife4j
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication
@EnableConfigurationProperties
@MapperScan("com.example.cos.mapper")
public class CosStorageServiceApplication {

    private static final Logger logger = LoggerFactory.getLogger(CosStorageServiceApplication.class);

    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext application = SpringApplication.run(CosStorageServiceApplication.class, args);
        Environment env = application.getEnvironment();
        
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path", "");
        
        logger.info("\n----------------------------------------------------------\n\t" +
                "腾讯云COS存储服务启动成功！访问地址:\n\t" +
                "本地访问: \t\thttp://localhost:{}{}\n\t" +
                "外部访问: \t\thttp://{}:{}{}\n\t" +
                "API文档地址: \thttp://localhost:{}{}/api/doc\n\t" +
                "Swagger地址: \thttp://localhost:{}{}/swagger-ui.html\n" +
                "----------------------------------------------------------",
                port, path,
                ip, port, path,
                port, path,
                port, path);
    }
}
