# -*- coding: utf-8 -*-
"""
数据校验测试脚本
用于测试阿里资产爬虫的数据处理和校验功能
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api_client import APIClient
from ali_asset_scraper import AliAssetScraper


def test_data_validation():
    """测试数据校验功能"""
    print("🧪 开始测试数据校验功能...")
    
    # 创建API客户端
    api_client = APIClient()
    
    # 创建爬虫实例
    scraper = AliAssetScraper(api_client)
    
    # 测试数据1: stairsType超出范围
    test_data_1 = {
        'auctionStatus': 1,
        'auctionTimes': 1,
        'buildingArea': 50.18,
        'communityName': None,
        'constructionYear': None,
        'decoration': 2,
        'deposit': 20000,
        'endTime': '2025-08-01 10:00:00',
        'evaluationPrice': 176100,
        'floor': '1/7',
        'houseCategory': 0,
        'houseType': None,
        'isSpecial': False,
        'isSelected': False,
        'latitude': None,
        'longitude': None,
        'priceIncrement': 1000,
        'propertyType': 1,
        'stairsType': 2,  # 这个值超出范围，应该被修正为1
        'startTime': '2025-07-31 10:00:00',
        'startingPrice': 123270,
        'tags': None,
        'title': '一拍 重庆市沙坪坝区建设坡91-1-1号房屋',
        'imageUrls': 'https://cos.cqjxzc.com.cn/2025/08/01/test.jpg',
        'originalUrl': 'https://sf-item.taobao.com/sf_item/958511291138.htm'
    }
    
    print("📋 测试数据1 - stairsType超出范围:")
    print(f"原始stairsType: {test_data_1['stairsType']}")
    
    # 处理数据
    processed_data_1 = scraper.process_house_data(test_data_1)
    print(f"处理后stairsType: {processed_data_1['stairsType']}")
    
    if processed_data_1['stairsType'] <= 1:
        print("✅ stairsType校验通过")
    else:
        print("❌ stairsType校验失败")
    
    # 测试数据2: propertyType超出范围
    test_data_2 = {
        'auctionStatus': 1,
        'auctionTimes': 1,
        'buildingArea': 80.5,
        'communityName': '测试小区',
        'constructionYear': 2010,
        'decoration': 1,
        'deposit': 50000,
        'endTime': '2025-08-02 15:00:00',
        'evaluationPrice': 500000,
        'floor': '5/10',
        'houseCategory': 0,
        'houseType': '2室1厅',
        'isSpecial': False,
        'isSelected': False,
        'latitude': 29.5647,
        'longitude': 106.5507,
        'priceIncrement': 5000,
        'propertyType': 3,  # 这个值超出范围，应该被修正为1
        'stairsType': 1,
        'startTime': '2025-08-01 15:00:00',
        'startingPrice': 350000,
        'tags': '学区房',
        'title': '二拍 测试房源标题',
        'imageUrls': 'https://cos.cqjxzc.com.cn/2025/08/01/test2.jpg',
        'originalUrl': 'https://sf-item.taobao.com/sf_item/123456789.htm'
    }
    
    print("\n📋 测试数据2 - propertyType超出范围:")
    print(f"原始propertyType: {test_data_2['propertyType']}")
    
    # 处理数据
    processed_data_2 = scraper.process_house_data(test_data_2)
    print(f"处理后propertyType: {processed_data_2['propertyType']}")
    
    if processed_data_2['propertyType'] <= 2:
        print("✅ propertyType校验通过")
    else:
        print("❌ propertyType校验失败")
    
    # 测试数据3: 布尔值转换
    test_data_3 = {
        'auctionStatus': 1,
        'auctionTimes': 1,
        'buildingArea': 60.0,
        'communityName': '测试小区3',
        'constructionYear': 2015,
        'decoration': 2,
        'deposit': 30000,
        'endTime': '2025-08-03 10:00:00',
        'evaluationPrice': 300000,
        'floor': '3/8',
        'houseCategory': 0,
        'houseType': '1室1厅',
        'isSpecial': True,  # 布尔值，应该转换为1
        'isSelected': 'true',  # 字符串，应该转换为1
        'latitude': 29.5647,
        'longitude': 106.5507,
        'priceIncrement': 3000,
        'propertyType': 1,
        'stairsType': 0,
        'startTime': '2025-08-02 10:00:00',
        'startingPrice': 210000,
        'tags': '精装修',
        'title': '变卖 测试房源标题3',
        'imageUrls': 'https://cos.cqjxzc.com.cn/2025/08/01/test3.jpg',
        'originalUrl': 'https://sf-item.taobao.com/sf_item/987654321.htm'
    }
    
    print("\n📋 测试数据3 - 布尔值转换:")
    print(f"原始isSpecial: {test_data_3['isSpecial']} (类型: {type(test_data_3['isSpecial'])})")
    print(f"原始isSelected: {test_data_3['isSelected']} (类型: {type(test_data_3['isSelected'])})")
    
    # 处理数据
    processed_data_3 = scraper.process_house_data(test_data_3)
    print(f"处理后isSpecial: {processed_data_3['isSpecial']} (类型: {type(processed_data_3['isSpecial'])})")
    print(f"处理后isSelected: {processed_data_3['isSelected']} (类型: {type(processed_data_3['isSelected'])})")
    
    if (isinstance(processed_data_3['isSpecial'], int) and 
        isinstance(processed_data_3['isSelected'], int)):
        print("✅ 布尔值转换校验通过")
    else:
        print("❌ 布尔值转换校验失败")
    
    # 测试完整的数据处理流程
    print("\n📋 测试完整数据处理流程:")
    
    all_test_data = [test_data_1, test_data_2, test_data_3]
    
    for i, test_data in enumerate(all_test_data, 1):
        print(f"\n--- 测试数据 {i} ---")
        try:
            processed_data = scraper.process_house_data(test_data)
            
            # 检查关键字段
            checks = [
                ('stairsType', processed_data.get('stairsType', 0) <= 1),
                ('propertyType', processed_data.get('propertyType', 1) <= 2),
                ('isSpecial', isinstance(processed_data.get('isSpecial', 0), int)),
                ('isSelected', isinstance(processed_data.get('isSelected', 0), int)),
            ]
            
            all_passed = True
            for field, passed in checks:
                status = "✅" if passed else "❌"
                print(f"  {status} {field}: {processed_data.get(field)}")
                if not passed:
                    all_passed = False
            
            if all_passed:
                print(f"  ✅ 测试数据 {i} 处理成功")
            else:
                print(f"  ❌ 测试数据 {i} 处理失败")
                
        except Exception as e:
            print(f"  ❌ 测试数据 {i} 处理异常: {str(e)}")
    
    print("\n🎉 数据校验测试完成！")


def test_ai_prompt_validation():
    """测试AI提示词中的字段限制"""
    print("\n🧪 测试AI提示词字段限制...")
    
    # 创建API客户端
    api_client = APIClient()
    
    # 创建爬虫实例
    scraper = AliAssetScraper(api_client)
    
    # 构建测试提示词
    test_text = """
    房源标题: 一拍 重庆市沙坪坝区建设坡91-1-1号房屋
    基本信息: 建筑面积：50.18平方米 楼层：1/7 电梯房
    详细描述: 起拍价：123270元 评估价：176100元 保证金：20000元
    公告详情: 拍卖时间：2025年07月31日10：00时起至2025年08月01日10：00时止
    """
    
    image_urls = "https://cos.cqjxzc.com.cn/2025/08/01/test.jpg"
    original_url = "https://sf-item.taobao.com/sf_item/958511291138.htm"
    
    # 构建AI提示词
    prompt = scraper.build_ai_prompt(test_text, image_urls, original_url)
    
    print("📋 AI提示词关键部分检查:")
    
    # 检查关键限制是否在提示词中
    key_checks = [
        ('stairsType限制', 'stairsType限制' in prompt and '值必须≤1' in prompt),
        ('propertyType限制', 'propertyType限制' in prompt and '值必须≤2' in prompt),
        ('电梯房设为1', '电梯房设为1' in prompt),
        ('楼梯房设为0', '楼梯房设为0' in prompt),
    ]
    
    for check_name, passed in key_checks:
        status = "✅" if passed else "❌"
        print(f"  {status} {check_name}")
    
    print("\n✅ AI提示词验证完成")


def main():
    """主测试函数"""
    print("🚀 开始数据校验综合测试...")
    print("=" * 60)
    
    # 测试1: 数据校验功能
    test_data_validation()
    
    # 测试2: AI提示词验证
    test_ai_prompt_validation()
    
    print("\n" + "=" * 60)
    print("🎉 数据校验综合测试完成！")
    print("\n📋 测试结果说明:")
    print("- 所有字段值都应该在API允许的范围内")
    print("- stairsType 必须 ≤ 1 (0=楼梯房, 1=电梯房)")
    print("- propertyType 必须 ≤ 2 (1=商品房, 2=经济适用房)")
    print("- 布尔字段应该转换为整数 (0=否, 1=是)")


if __name__ == "__main__":
    main()
