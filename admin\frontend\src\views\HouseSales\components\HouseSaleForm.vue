<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
    class="house-sale-form"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="城市" prop="city">
          <el-input v-model="form.city" placeholder="请输入城市" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="小区名称" prop="communityName">
          <el-input v-model="form.communityName" placeholder="请输入小区名称" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="房屋类型" prop="houseType">
          <el-select v-model="form.houseType" placeholder="请选择房屋类型" style="width: 100%">
            <el-option label="住宅" value="住宅" />
            <el-option label="公寓" value="公寓" />
            <el-option label="别墅" value="别墅" />
            <el-option label="商铺" value="商铺" />
            <el-option label="写字楼" value="写字楼" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="房屋面积" prop="housingArea">
          <el-input-number
            v-model="form.housingArea"
            :min="0"
            :precision="2"
            placeholder="请输入房屋面积"
            style="width: 100%"
          />
          <span style="margin-left: 8px">㎡</span>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="期望价格" prop="expectedPrice">
          <el-input-number
            v-model="form.expectedPrice"
            :min="0"
            :precision="2"
            placeholder="请输入期望价格"
            style="width: 100%"
          />
          <span style="margin-left: 8px">元</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="form.contactPerson" placeholder="请输入联系人姓名" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="form.gender">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="联系方式" prop="contactInfo">
          <el-input v-model="form.contactInfo" placeholder="请输入联系方式" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="备注" prop="remarks">
      <el-input
        v-model="form.remarks"
        type="textarea"
        :rows="4"
        placeholder="请输入备注信息"
      />
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="submitForm">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
      <el-button @click="resetForm">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  houseSaleData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit'])

const formRef = ref()

// 表单数据
const form = reactive({
  id: null,
  userId: null,
  city: '',
  communityName: '',
  houseType: '',
  housingArea: null,
  expectedPrice: null,
  contactPerson: '',
  gender: '男',
  contactInfo: '',
  remarks: ''
})

// 表单验证规则
const rules = {
  city: [
    { required: true, message: '请输入城市', trigger: 'blur' }
  ],
  communityName: [
    { required: true, message: '请输入小区名称', trigger: 'blur' }
  ],
  houseType: [
    { required: true, message: '请选择房屋类型', trigger: 'change' }
  ],
  housingArea: [
    { required: true, message: '请输入房屋面积', trigger: 'blur' }
  ],
  expectedPrice: [
    { required: true, message: '请输入期望价格', trigger: 'blur' }
  ],
  contactPerson: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  contactInfo: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.houseSaleData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(form, newData)
  }
}, { immediate: true, deep: true })

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()
    emit('submit', { ...form })
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
  Object.assign(form, {
    id: null,
    userId: null,
    city: '',
    communityName: '',
    houseType: '',
    housingArea: null,
    expectedPrice: null,
    contactPerson: '',
    gender: '男',
    contactInfo: '',
    remarks: ''
  })
}

// 暴露方法给父组件
defineExpose({
  resetForm
})
</script>

<style lang="scss" scoped>
.house-sale-form {
  // 样式可以根据需要添加
}
</style>
