package com.example.cos.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 轮播图更新DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "轮播图更新请求")
public class CarouselUpdateDTO {

    @ApiModelProperty(value = "轮播图ID", example = "1", required = true)
    @NotNull(message = "轮播图ID不能为空")
    private Long id;

    @ApiModelProperty(value = "轮播图图片链接", example = "https://example.com/image.jpg", required = true)
    @NotBlank(message = "图片链接不能为空")
    private String imageUrl;

    @ApiModelProperty(value = "点击跳转链接", example = "https://example.com/page")
    private String jumpUrl;

    @ApiModelProperty(value = "是否启用", example = "1", required = true)
    @NotNull(message = "启用状态不能为空")
    private Integer isEnabled;

    @ApiModelProperty(value = "排序序号", example = "1", required = true)
    @NotNull(message = "排序序号不能为空")
    private Integer sortOrder;

    @ApiModelProperty(value = "备注信息", example = "首页轮播图")
    private String remark;

    public CarouselUpdateDTO() {}

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public Integer getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(Integer isEnabled) {
        this.isEnabled = isEnabled;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "CarouselUpdateDTO{" +
                "id=" + id +
                ", imageUrl='" + imageUrl + '\'' +
                ", jumpUrl='" + jumpUrl + '\'' +
                ", isEnabled=" + isEnabled +
                ", sortOrder=" + sortOrder +
                ", remark='" + remark + '\'' +
                '}';
    }
}
