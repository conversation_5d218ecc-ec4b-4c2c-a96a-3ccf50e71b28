<template>
  <div class="user-detail">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="用户ID">
        {{ userData.id }}
      </el-descriptions-item>
      <el-descriptions-item label="昵称">
        {{ userData.nickname }}
      </el-descriptions-item>
      <el-descriptions-item label="手机号">
        {{ userData.phoneNumber }}
      </el-descriptions-item>
      <el-descriptions-item label="微信OpenID">
        {{ userData.wechatOpenid }}
      </el-descriptions-item>
      <el-descriptions-item label="头像">
        <el-avatar :size="60" :src="userData.avatarUrl" />
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ userData.createTime }}
      </el-descriptions-item>
      <el-descriptions-item label="用户角色">
        <el-tag :type="userData.role === 1 ? 'success' : 'info'">
          {{ getRoleText(userData.role) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="收藏房源数量">
        <el-tag type="info">{{ getFavoriteCount(userData.favoriteHouseIds) }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="关注房源数量">
        <el-tag type="warning">{{ getFollowCount(userData.followedHouseIds) }}</el-tag>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 收藏房源列表 -->
    <div class="favorites-section" v-if="userData.favoriteHouseIds && userData.favoriteHouseIds.length">
      <h3>收藏房源</h3>
      <el-tag
        v-for="houseId in userData.favoriteHouseIds"
        :key="houseId"
        class="house-tag"
        type="info"
      >
        房源ID: {{ houseId }}
      </el-tag>
    </div>

    <!-- 关注房源列表 -->
    <div class="follows-section" v-if="userData.followedHouseIds && userData.followedHouseIds.length">
      <h3>关注房源</h3>
      <el-tag
        v-for="houseId in userData.followedHouseIds"
        :key="houseId"
        class="house-tag"
        type="warning"
      >
        房源ID: {{ houseId }}
      </el-tag>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  userData: {
    type: Object,
    default: () => ({})
  }
})

// 获取收藏数量
const getFavoriteCount = (favoriteIds) => {
  return Array.isArray(favoriteIds) ? favoriteIds.length : 0
}

// 获取关注数量
const getFollowCount = (followIds) => {
  return Array.isArray(followIds) ? followIds.length : 0
}

// 获取角色文本
const getRoleText = (role) => {
  return role === 1 ? '有权限用户' : '普通用户'
}
</script>

<style lang="scss" scoped>
.user-detail {
  .favorites-section,
  .follows-section {
    margin-top: 30px;

    h3 {
      margin-bottom: 15px;
      color: #303133;
    }

    .house-tag {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }
}
</style>
