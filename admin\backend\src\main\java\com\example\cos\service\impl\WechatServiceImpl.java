package com.example.cos.service.impl;

import com.example.cos.config.WechatProperties;
import com.example.cos.dto.WechatLoginDTO;
import com.example.cos.dto.WechatLoginResultDTO;
import com.example.cos.dto.WechatPhoneNumberDTO;
import com.example.cos.entity.User;
import com.example.cos.entity.WechatAccessToken;
import com.example.cos.entity.WechatSessionInfo;
import com.example.cos.service.UserService;
import com.example.cos.service.WechatService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 微信服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class WechatServiceImpl implements WechatService {

    private static final Logger logger = LoggerFactory.getLogger(WechatServiceImpl.class);

    @Autowired
    private WechatProperties wechatProperties;

    @Autowired
    private UserService userService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    // 简单的内存缓存，生产环境建议使用Redis
    private final Map<String, String> tokenCache = new HashMap<>();
    private final Map<String, Long> tokenExpireCache = new HashMap<>();

    @Override
    public WechatLoginResultDTO miniappLogin(WechatLoginDTO loginDTO) {
        logger.info("微信小程序登录: {}", loginDTO);

        try {
            // 1. 通过code获取openid和session_key
            WechatSessionInfo sessionInfo = getSessionInfo(loginDTO.getCode());
            if (!sessionInfo.isSuccess()) {
                throw new RuntimeException("微信登录失败: " + sessionInfo.getErrmsg());
            }

            // 2. 查找或创建用户
            User user = userService.findByWechatOpenid(sessionInfo.getOpenid());
            boolean isNewUser = false;

            if (user == null) {
                // 创建新用户
                user = new User();
                user.setWechatOpenid(sessionInfo.getOpenid());
                user.setNickname(loginDTO.getNickname());
                user.setAvatarUrl(loginDTO.getAvatarUrl());
                user.setPhoneNumber(loginDTO.getPhoneNumber());
                
                userService.save(user);
                isNewUser = true;
                logger.info("创建新用户: {}", user.getId());
            } else {
                // 更新用户信息 - 只有在用户信息为空或默认值时才更新
                boolean needUpdate = false;

                if (StringUtils.hasText(loginDTO.getNickname()) &&
                    (!StringUtils.hasText(user.getNickname()) ||
                     "微信用户".equals(user.getNickname()) ||
                     "点击登录".equals(user.getNickname()))) {
                    user.setNickname(loginDTO.getNickname());
                    needUpdate = true;
                }

                if (StringUtils.hasText(loginDTO.getAvatarUrl()) &&
                    !StringUtils.hasText(user.getAvatarUrl())) {
                    user.setAvatarUrl(loginDTO.getAvatarUrl());
                    needUpdate = true;
                }

                if (StringUtils.hasText(loginDTO.getPhoneNumber())) {
                    user.setPhoneNumber(loginDTO.getPhoneNumber());
                    needUpdate = true;
                }

                if (needUpdate) {
                    userService.updateById(user);
                    logger.info("更新用户信息: {}", user.getId());
                }
            }

            // 3. 生成访问令牌
            String accessToken = generateAccessToken(user.getId(), sessionInfo.getOpenid());
            String refreshToken = generateRefreshToken(user.getId());
            Long expiresIn = 7200L; // 2小时

            // 4. 缓存令牌
            tokenCache.put(accessToken, user.getId().toString());
            tokenExpireCache.put(accessToken, System.currentTimeMillis() + expiresIn * 1000);

            // 5. 构建返回结果
            WechatLoginResultDTO result = new WechatLoginResultDTO(user, accessToken, refreshToken, expiresIn, isNewUser);
            result.setOpenid(sessionInfo.getOpenid());
            result.setUnionid(sessionInfo.getUnionid());

            logger.info("微信登录成功: userId={}, isNewUser={}", user.getId(), isNewUser);
            return result;

        } catch (Exception e) {
            logger.error("微信登录失败: {}", e.getMessage(), e);
            throw new RuntimeException("微信登录失败: " + e.getMessage());
        }
    }

    @Override
    public WechatSessionInfo getSessionInfo(String code) {
        logger.info("获取微信会话信息: code={}", code);

        try {
            String url = wechatProperties.getApiBaseUrl() + wechatProperties.getJscode2sessionUrl() +
                    "?appid=" + wechatProperties.getAppId() +
                    "&secret=" + wechatProperties.getAppSecret() +
                    "&js_code=" + code +
                    "&grant_type=authorization_code";

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            WechatSessionInfo sessionInfo = objectMapper.readValue(response.getBody(), WechatSessionInfo.class);

            logger.info("获取微信会话信息成功: {}", sessionInfo);
            return sessionInfo;

        } catch (Exception e) {
            logger.error("获取微信会话信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取微信会话信息失败: " + e.getMessage());
        }
    }

    @Override
    public WechatAccessToken getAccessToken() {
        logger.info("获取微信访问令牌");

        try {
            String url = wechatProperties.getApiBaseUrl() + wechatProperties.getTokenUrl() +
                    "?grant_type=client_credential" +
                    "&appid=" + wechatProperties.getAppId() +
                    "&secret=" + wechatProperties.getAppSecret();

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            WechatAccessToken accessToken = objectMapper.readValue(response.getBody(), WechatAccessToken.class);

            logger.info("获取微信访问令牌成功: {}", accessToken);
            return accessToken;

        } catch (Exception e) {
            logger.error("获取微信访问令牌失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取微信访问令牌失败: " + e.getMessage());
        }
    }

    @Override
    public String getPhoneNumber(String code) {
        logger.info("获取用户手机号: code={}", code);

        try {
            // 1. 获取access_token
            WechatAccessToken accessToken = getAccessToken();
            if (!accessToken.isSuccess()) {
                throw new RuntimeException("获取access_token失败: " + accessToken.getErrmsg());
            }

            // 2. 调用获取手机号接口
            String url = wechatProperties.getApiBaseUrl() + wechatProperties.getGetPhoneNumberUrl() +
                    "?access_token=" + accessToken.getAccessToken();

            // 3. 构建请求体
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("code", code);

            // 4. 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, String>> entity = new HttpEntity<>(requestBody, headers);

            // 5. 发送POST请求
            ResponseEntity<WechatPhoneNumberDTO> response = restTemplate.postForEntity(url, entity, WechatPhoneNumberDTO.class);

            WechatPhoneNumberDTO phoneNumberDTO = response.getBody();
            logger.info("获取手机号响应: {}", phoneNumberDTO);

            // 6. 解析响应获取手机号
            if (phoneNumberDTO != null && phoneNumberDTO.isSuccess()) {
                WechatPhoneNumberDTO.PhoneInfo phoneInfo = phoneNumberDTO.getPhoneInfo();
                if (phoneInfo != null) {
                    // 优先返回纯手机号，如果没有则返回带区号的手机号
                    String phoneNumber = phoneInfo.getPurePhoneNumber();
                    if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
                        phoneNumber = phoneInfo.getPhoneNumber();
                    }

                    logger.info("成功获取手机号: {}", phoneNumber);
                    return phoneNumber;
                } else {
                    logger.error("微信返回的手机号信息为空");
                }
            } else {
                logger.error("微信获取手机号失败: errcode={}, errmsg={}",
                    phoneNumberDTO != null ? phoneNumberDTO.getErrcode() : "null",
                    phoneNumberDTO != null ? phoneNumberDTO.getErrmsg() : "null");
            }

            return null;

        } catch (Exception e) {
            logger.error("获取用户手机号失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取用户手机号失败: " + e.getMessage());
        }
    }

    @Override
    public WechatLoginResultDTO refreshToken(String refreshToken) {
        logger.info("刷新访问令牌: refreshToken={}", refreshToken);
        
        // TODO: 实现刷新令牌逻辑
        throw new RuntimeException("刷新令牌功能暂未实现");
    }

    @Override
    public boolean validateToken(String accessToken) {
        if (!StringUtils.hasText(accessToken)) {
            return false;
        }

        // 检查令牌是否存在且未过期
        if (!tokenCache.containsKey(accessToken)) {
            return false;
        }

        Long expireTime = tokenExpireCache.get(accessToken);
        if (expireTime == null || System.currentTimeMillis() > expireTime) {
            // 令牌已过期，清除缓存
            tokenCache.remove(accessToken);
            tokenExpireCache.remove(accessToken);
            return false;
        }

        return true;
    }

    @Override
    public boolean logout(String accessToken) {
        logger.info("用户登出: accessToken={}", accessToken);

        if (StringUtils.hasText(accessToken)) {
            tokenCache.remove(accessToken);
            tokenExpireCache.remove(accessToken);
            return true;
        }

        return false;
    }

    /**
     * 生成访问令牌
     */
    private String generateAccessToken(Integer userId, String openid) {
        return "access_" + UUID.randomUUID().toString().replace("-", "") + "_" + userId;
    }

    /**
     * 生成刷新令牌
     */
    private String generateRefreshToken(Integer userId) {
        return "refresh_" + UUID.randomUUID().toString().replace("-", "") + "_" + userId;
    }
}
