# -*- coding: utf-8 -*-
"""
网页爬虫核心模块 - 包含完整的反爬措施和数据提取逻辑
"""
import time
import random
import re
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Callable
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import (
    TimeoutException, NoSuchElementException, 
    ElementClickInterceptedException, StaleElementReferenceException
)
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
from config import Config
from api_client import APIClient

class HouseScraper:
    """房源爬虫类"""
    
    def __init__(self, progress_callback: Optional[Callable] = None, log_callback: Optional[Callable] = None):
        self.driver = None
        self.wait = None
        self.api_client = APIClient()
        self.progress_callback = progress_callback
        self.log_callback = log_callback
        self.is_running = False
        self.is_paused = False
        self.scraped_count = 0
        self.success_count = 0
        self.error_count = 0

        # 可配置的目标网址和模式
        self.target_url = Config.TARGET_URL
        self.scrape_mode = "auto"  # auto, direct, custom
        
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {level}: {message}"
        print(log_message)
        
        if self.log_callback:
            self.log_callback(log_message)
    
    def update_progress(self, current: int, total: int, status: str = ""):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(current, total, status)
    
    def init_driver(self) -> bool:
        """初始化WebDriver"""
        methods = [
            self._init_driver_with_manager,
            self._init_driver_local,
            self._init_driver_system_chrome
        ]

        for i, method in enumerate(methods, 1):
            try:
                self.log(f"尝试方法 {i}: {method.__name__}")
                if method():
                    return True
            except Exception as e:
                self.log(f"方法 {i} 失败: {str(e)}", "WARNING")
                continue

        self.log("所有初始化方法都失败了", "ERROR")
        return False

    def _init_driver_with_manager(self) -> bool:
        """使用WebDriverManager初始化"""
        self.log("正在使用WebDriverManager下载ChromeDriver...")

        try:
            # 获取Chrome选项
            options = Config.get_chrome_options()

            # 创建WebDriver服务
            service = Service(ChromeDriverManager().install())

            # 创建WebDriver实例
            self.driver = webdriver.Chrome(service=service, options=options)

            return self._configure_driver()
        except Exception as e:
            if "Could not reach host" in str(e) or "offline" in str(e).lower():
                self.log("网络连接问题，跳过WebDriverManager方法", "WARNING")
                raise Exception("网络连接问题")
            else:
                raise e

    def _init_driver_local(self) -> bool:
        """使用本地ChromeDriver初始化"""
        self.log("正在尝试使用本地ChromeDriver...")

        # 获取Chrome选项
        options = Config.get_chrome_options()

        # 检查本地ChromeDriver路径
        local_paths = [
            "./chromedriver.exe",  # 当前目录
            "chromedriver.exe",    # 当前目录
            "chromedriver",        # Linux/Mac
        ]

        driver_path = None
        for path in local_paths:
            if os.path.exists(path):
                driver_path = path
                self.log(f"找到本地ChromeDriver: {path}")
                break

        if driver_path:
            service = Service(driver_path)
            self.driver = webdriver.Chrome(service=service, options=options)
        else:
            # 尝试使用系统PATH中的chromedriver
            self.driver = webdriver.Chrome(options=options)

        return self._configure_driver()

    def _init_driver_system_chrome(self) -> bool:
        """使用系统Chrome浏览器初始化"""
        self.log("正在尝试使用系统Chrome浏览器...")

        # 获取Chrome选项
        options = Config.get_chrome_options()

        # 尝试指定Chrome二进制路径
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(
                os.environ.get('USERNAME', 'User')
            )
        ]

        for chrome_path in chrome_paths:
            if os.path.exists(chrome_path):
                options.binary_location = chrome_path
                self.log(f"找到Chrome浏览器: {chrome_path}")
                break

        self.driver = webdriver.Chrome(options=options)

        return self._configure_driver()

    def _configure_driver(self) -> bool:
        """配置WebDriver"""
        try:
            # 配置等待和超时
            self.driver.implicitly_wait(Config.WEBDRIVER_CONFIG['implicit_wait'])
            self.driver.set_page_load_timeout(Config.WEBDRIVER_CONFIG['page_load_timeout'])
            self.driver.set_script_timeout(Config.WEBDRIVER_CONFIG['script_timeout'])

            # 确保浏览器全屏显示
            try:
                self.driver.maximize_window()
                self.log("浏览器窗口已最大化")

                # 获取屏幕尺寸并设置窗口大小
                screen_width = self.driver.execute_script("return screen.width;")
                screen_height = self.driver.execute_script("return screen.height;")
                self.driver.set_window_size(screen_width, screen_height)
                self.log(f"浏览器窗口设置为全屏: {screen_width}x{screen_height}")

                # 将窗口移动到屏幕左上角
                self.driver.set_window_position(0, 0)

            except Exception as e:
                self.log(f"设置全屏失败，但继续执行: {str(e)}", "WARNING")

            # 创建显式等待对象
            self.wait = WebDriverWait(self.driver, 10)

            # 执行增强的反检测脚本
            self._execute_bypass_detection_script()

            # 测试浏览器是否正常工作
            self.driver.get("data:text/html,<html><body><h1>Test</h1></body></html>")

            self.log("浏览器初始化成功")
            return True

        except Exception as e:
            self.log(f"浏览器配置失败: {str(e)}", "ERROR")
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None
            return False

    def _execute_bypass_detection_script(self):
        """执行绕过webdriver检测的脚本"""
        try:
            # 读取绕过检测的JS文件
            bypass_js_path = os.path.join(os.path.dirname(__file__), 'bypass_webdriver_detection.js')

            if os.path.exists(bypass_js_path):
                with open(bypass_js_path, 'r', encoding='utf-8') as f:
                    bypass_js = f.read()

                # 通过CDP执行脚本，确保在每个新页面都执行
                self.driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
                    "source": bypass_js
                })

                # 在当前页面也执行一次
                self.driver.execute_script(bypass_js)

                self.log("绕过webdriver检测脚本已加载", "DEBUG")
            else:
                # 如果文件不存在，使用基础的绕过脚本
                basic_bypass_js = """
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                // 删除自动化相关属性
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

                // 修改chrome对象
                if (!window.chrome) {
                    window.chrome = {
                        runtime: {},
                        loadTimes: function() {},
                        csi: function() {},
                        app: {}
                    };
                }
                """

                self.driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
                    "source": basic_bypass_js
                })
                self.driver.execute_script(basic_bypass_js)

                self.log("基础绕过webdriver检测脚本已加载", "DEBUG")

        except Exception as e:
            self.log(f"执行绕过检测脚本失败: {str(e)}", "WARNING")

    def navigate_to_target(self) -> bool:
        """导航到目标页面"""
        try:
            self.log(f"正在访问目标网站: {self.target_url}")
            self.driver.get(self.target_url)

            # 在新页面执行绕过检测脚本
            self._execute_bypass_detection_script()

            # 随机延迟
            delay = Config.get_random_delay('page_load')
            self.log(f"页面加载完成，等待 {delay:.1f} 秒...")
            time.sleep(delay)

            # 尝试加载保存的cookies以保持登录状态
            self.log("尝试加载保存的登录状态...")
            cookie_loaded = self.load_cookies()

            if cookie_loaded:
                # 检查登录状态
                if self.check_login_status():
                    self.log("✓ 登录状态已恢复")
                else:
                    self.log("⚠ Cookies已加载但未检测到登录状态")
            else:
                self.log("⚠ 未找到保存的登录信息，可能需要手动登录")

            # 模拟人类行为 - 随机滚动
            self._simulate_human_behavior()

            return True

        except Exception as e:
            self.log(f"访问目标网站失败: {str(e)}", "ERROR")
            return False
    
    def click_first_element(self) -> bool:
        """点击第一个元素（房产专区入口）并确保在新标签页中打开"""
        self.log("=" * 60)
        self.log("第一步：点击房产专区入口")
        self.log("=" * 60)

        # 记录点击前的页面状态
        old_url = self.driver.current_url
        old_title = self.driver.title
        self.log(f"点击前页面URL: {old_url}")
        self.log(f"点击前页面标题: {old_title}")

        # 记录点击前的窗口句柄
        original_handles = self.driver.window_handles
        original_handle = self.driver.current_window_handle
        self.log(f"点击前窗口数量: {len(original_handles)}")
        self.log(f"当前窗口句柄: {original_handle}")

        # 尝试点击房产专区元素
        success = self._safe_click(
            Config.XPATH_SELECTORS['first_click'],
            "房产专区入口",
            wait_after=True
        )

        if success:
            self.log("✓ 房产专区入口点击成功，等待新标签页打开...")

            # 等待新标签页打开
            new_tab_opened = False
            max_wait_time = 15  # 最大等待15秒
            check_interval = 0.5  # 每0.5秒检查一次

            for attempt in range(int(max_wait_time / check_interval)):
                try:
                    current_handles = self.driver.window_handles
                    if len(current_handles) > len(original_handles):
                        self.log(f"✓ 检测到新标签页打开！标签页数量: {len(original_handles)} -> {len(current_handles)}")

                        # 切换到最新的标签页
                        latest_handle = current_handles[-1]
                        self.driver.switch_to.window(latest_handle)
                        self.log(f"✓ 已自动切换到新标签页: {latest_handle}")

                        # 等待新标签页完全加载
                        self.log("等待新标签页完全加载...")
                        time.sleep(3)

                        # 验证新标签页内容
                        try:
                            new_url = self.driver.current_url
                            new_title = self.driver.title
                            self.log(f"✓ 新标签页URL: {new_url}")
                            self.log(f"✓ 新标签页标题: {new_title}")

                            # 检查是否成功进入房产专区页面
                            if "taobao.com" in new_url and new_url != old_url:
                                self.log("✓ 成功进入房产专区页面")
                                new_tab_opened = True
                                break
                            else:
                                self.log("⚠ 新标签页URL未发生预期变化", "WARNING")
                        except Exception as e:
                            self.log(f"验证新标签页内容时出错: {str(e)}", "WARNING")

                        new_tab_opened = True
                        break

                    time.sleep(check_interval)

                except Exception as e:
                    self.log(f"检查新标签页时出错: {str(e)}", "WARNING")
                    time.sleep(check_interval)

            if new_tab_opened:
                self.log("✓ 第一步完成：房产专区入口点击成功，已切换到新标签页")

                # 等待页面完全稳定并加载关键元素
                self.log("等待页面完全稳定...")
                time.sleep(3)

                # 尝试等待房源分类相关元素出现
                try:
                    self.log("等待房源分类导航元素加载...")
                    self.wait.until(
                        EC.any_of(
                            EC.element_to_be_clickable((By.XPATH, Config.XPATH_SELECTORS['second_click_residential'])),
                            EC.element_to_be_clickable((By.XPATH, Config.XPATH_SELECTORS['second_click_commercial'])),
                            EC.presence_of_element_located((By.XPATH, "//a[contains(text(), '住宅用房')]")),
                            EC.presence_of_element_located((By.XPATH, "//a[contains(text(), '商办')]")),
                            EC.presence_of_element_located((By.XPATH, "//*[contains(@id, 'guid-')]"))
                        )
                    )
                    self.log("✓ 房源分类导航元素已加载")
                except TimeoutException:
                    self.log("⚠ 等待房源分类导航元素超时，但继续执行", "WARNING")

                return True
            else:
                self.log("✗ 第一步失败：未检测到新标签页打开", "ERROR")
                return False
        else:
            self.log("✗ 第一步失败：房产专区入口点击失败", "ERROR")
            return False

    def click_second_element(self) -> bool:
        """点击第二个元素（房源分类）并确保在新标签页中打开"""
        # 获取房源类型
        house_type = getattr(self, 'house_type', '住宅')

        self.log("=" * 60)
        self.log(f"第二步：选择{house_type}分类")
        self.log("=" * 60)

        # 确保在正确的窗口中
        if not self._ensure_correct_window():
            self.log("⚠ 窗口切换失败，但继续尝试", "WARNING")

        # 记录点击前的状态
        old_handles = self.driver.window_handles
        old_handle = self.driver.current_window_handle
        old_url = self.driver.current_url
        self.log(f"点击前窗口数量: {len(old_handles)}")
        self.log(f"点击前页面URL: {old_url}")

        # 强制刷新页面状态，确保获取最新的DOM
        self.log("刷新页面状态，确保DOM完全加载...")
        try:
            # 检查页面加载状态
            ready_state = self.driver.execute_script("return document.readyState")
            self.log(f"页面加载状态: {ready_state}")

            if ready_state != "complete":
                self.log("等待页面完全加载...")
                self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")

            # 滚动页面确保所有元素都已渲染
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/4);")
            time.sleep(1)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)

            # 验证页面上下文
            current_url = self.driver.current_url
            page_title = self.driver.title
            self.log(f"✓ 当前页面URL: {current_url}")
            self.log(f"✓ 当前页面标题: {page_title}")

        except Exception as e:
            self.log(f"刷新页面状态时出错: {str(e)}", "ERROR")

        # 根据房源类型选择对应的选择器
        if house_type == "住宅":
            primary_selector = Config.XPATH_SELECTORS['second_click_residential']
            alternative_selector = Config.XPATH_SELECTORS['second_click_residential_alternative']
            element_text = "住宅用房"
            alternative_text_selectors = [
                "//a[contains(text(), '住宅用房')]",
                "//span[contains(text(), '住宅用房')]",
                "//*[contains(@id, 'guid-')]//a[contains(text(), '住宅用房')]",
                "//*[contains(@id, 'guid-')]//span[contains(text(), '住宅用房')]",
                "//a[text()='住宅用房']",
                "//span[text()='住宅用房']"
            ]
        else:  # 商办
            primary_selector = Config.XPATH_SELECTORS['second_click_commercial']
            alternative_selector = Config.XPATH_SELECTORS['second_click_commercial_alternative']
            element_text = "商办"
            alternative_text_selectors = [
                "//a[contains(text(), '商办')]",
                "//span[contains(text(), '商办')]",
                "//*[contains(@id, 'guid-')]//a[contains(text(), '商办')]",
                "//*[contains(@id, 'guid-')]//span[contains(text(), '商办')]",
                "//a[text()='商办']",
                "//span[text()='商办']"
            ]

        # 尝试点击房源分类元素
        self.log(f"查找并点击{element_text}元素...")

        # 首先尝试原始配置的选择器
        success = self._safe_click(
            primary_selector,
            f"{element_text}元素(原始选择器)",
            wait_after=True
        )

        if success:
            self.log("✓ 原始选择器点击成功")
        else:
            self.log("原始选择器失败，尝试替代选择器...")

            # 尝试配置的替代选择器
            try:
                success = self._safe_click(
                    alternative_selector,
                    f"{element_text}元素(替代选择器)",
                    wait_after=True
                )
                if success:
                    self.log("✓ 替代选择器点击成功")
                else:
                    self.log("替代选择器也失败，尝试通用选择器...")
            except Exception as e:
                self.log(f"替代选择器失败: {str(e)}", "WARNING")

            # 如果配置的选择器都失败，尝试通用选择器
            if not success:
                for selector in alternative_text_selectors:
                    try:
                        self.log(f"尝试通用选择器: {selector}")
                        if self._safe_click(selector, f"{element_text}元素(通用选择器)", wait_after=True):
                            success = True
                            self.log(f"✓ 通用选择器点击成功: {selector}")
                            break
                    except Exception as e:
                        self.log(f"通用选择器失败: {selector} - {str(e)}", "WARNING")
                        continue

        if success:
            self.log(f"✓ {element_text}元素点击成功")

            if house_type == "住宅":
                # 住宅用房：等待新标签页打开
                self.log("等待新标签页打开...")
                new_tab_opened = False
                max_wait_time = 15
                check_interval = 0.5

                for _ in range(int(max_wait_time / check_interval)):
                    try:
                        current_handles = self.driver.window_handles
                        if len(current_handles) > len(old_handles):
                            self.log(f"✓ 检测到新标签页打开！标签页数量: {len(old_handles)} -> {len(current_handles)}")

                            # 切换到最新的标签页
                            latest_handle = current_handles[-1]
                            self.driver.switch_to.window(latest_handle)
                            self.log(f"✓ 已自动切换到房源列表标签页: {latest_handle}")

                            # 等待新标签页完全加载
                            time.sleep(3)

                            # 验证新标签页内容
                            try:
                                new_url = self.driver.current_url
                                new_title = self.driver.title
                                self.log(f"✓ 房源列表页面URL: {new_url}")
                                self.log(f"✓ 房源列表页面标题: {new_title}")

                                # 检查是否成功进入房源列表页面
                                if new_url != old_url:
                                    self.log("✓ 成功进入房源列表页面")
                                    new_tab_opened = True
                                    break
                            except Exception as e:
                                self.log(f"验证新标签页内容时出错: {str(e)}", "WARNING")

                            new_tab_opened = True
                            break

                        time.sleep(check_interval)

                    except Exception as e:
                        self.log(f"检查新标签页时出错: {str(e)}", "WARNING")
                        time.sleep(check_interval)

                if new_tab_opened:
                    self.log("✓ 第二步完成：住宅用房分类选择成功，已切换到房源列表页面")
                    success_result = True
                else:
                    self.log("✗ 第二步失败：未检测到新标签页打开", "ERROR")
                    success_result = False

            else:  # 商办
                # 商办：在当前页面刷新，等待页面内容变化
                self.log("等待当前页面刷新...")

                # 等待页面刷新完成
                time.sleep(5)

                # 验证页面内容是否变化
                try:
                    current_url = self.driver.current_url
                    current_title = self.driver.title
                    self.log(f"✓ 刷新后页面URL: {current_url}")
                    self.log(f"✓ 刷新后页面标题: {current_title}")

                    # 检查URL是否有变化（商办可能会有参数变化）
                    if current_url != old_url or "商办" in current_title:
                        self.log("✓ 检测到页面内容变化，成功进入商办列表")
                    else:
                        self.log("⚠ 页面URL未明显变化，但继续执行", "WARNING")

                except Exception as e:
                    self.log(f"验证页面变化时出错: {str(e)}", "WARNING")

                self.log("✓ 第二步完成：商办分类选择成功，页面已刷新")
                success_result = True

            # 等待房源列表页面完全加载
            self.log("等待房源列表页面完全加载...")
            time.sleep(3)

            # 尝试等待房源列表容器出现
            try:
                self.log("等待房源列表容器加载...")
                self.wait.until(
                    EC.any_of(
                        EC.presence_of_element_located((By.XPATH, "//*[@id='guid-9018433170']")),
                        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'item')]")),
                        EC.presence_of_element_located((By.XPATH, "//*[contains(@id, 'guid-')]"))
                    )
                )
                self.log("✓ 房源列表容器已加载")
            except TimeoutException:
                self.log("⚠ 等待房源列表容器超时，但继续执行", "WARNING")

            return success_result
        else:
            self.log(f"✗ 第二步失败：{element_text}元素点击失败", "ERROR")
            return False

    def _ensure_correct_window(self) -> bool:
        """确保在正确的窗口中操作"""
        try:
            current_handles = self.driver.window_handles
            current_handle = self.driver.current_window_handle

            self.log(f"窗口检查 - 总数: {len(current_handles)}, 当前: {current_handle}")

            # 如果有多个窗口，切换到最新的窗口
            if len(current_handles) > 1:
                latest_handle = current_handles[-1]
                if current_handle != latest_handle:
                    self.log(f"切换窗口: {current_handle} -> {latest_handle}")
                    self.driver.switch_to.window(latest_handle)
                    time.sleep(1)

                    # 验证切换成功
                    new_current = self.driver.current_window_handle
                    if new_current == latest_handle:
                        self.log("✓ 窗口切换成功")
                        return True
                    else:
                        self.log("✗ 窗口切换失败", "ERROR")
                        return False
                else:
                    self.log("✓ 已在最新窗口中")
                    return True
            else:
                self.log("✓ 只有一个窗口")
                return True

        except Exception as e:
            self.log(f"窗口检查失败: {str(e)}", "ERROR")
            return False

    def save_cookies(self, filename: str = "cookies.json"):
        """保存当前会话的Cookies"""
        try:
            import json
            cookies = self.driver.get_cookies()

            # 创建cookies目录
            cookies_dir = os.path.join(os.getcwd(), "cookies")
            if not os.path.exists(cookies_dir):
                os.makedirs(cookies_dir)

            cookie_file = os.path.join(cookies_dir, filename)

            with open(cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)

            self.log(f"✓ Cookies已保存到: {cookie_file}")
            return True

        except Exception as e:
            self.log(f"✗ 保存Cookies失败: {str(e)}", "ERROR")
            return False

    def load_cookies(self, filename: str = "cookies.json"):
        """加载保存的Cookies"""
        try:
            import json
            cookies_dir = os.path.join(os.getcwd(), "cookies")
            cookie_file = os.path.join(cookies_dir, filename)

            if not os.path.exists(cookie_file):
                self.log(f"Cookie文件不存在: {cookie_file}")
                return False

            with open(cookie_file, 'r', encoding='utf-8') as f:
                cookies = json.load(f)

            # 先访问目标网站，然后添加cookies
            self.driver.get(self.target_url)
            time.sleep(2)

            # 添加每个cookie
            for cookie in cookies:
                try:
                    # 移除可能导致问题的字段
                    cookie.pop('sameSite', None)
                    cookie.pop('httpOnly', None)
                    self.driver.add_cookie(cookie)
                except Exception as e:
                    self.log(f"添加Cookie失败: {str(e)}", "WARNING")
                    continue

            self.log(f"✓ 已加载 {len(cookies)} 个Cookies")

            # 刷新页面以应用cookies
            self.driver.refresh()
            time.sleep(3)

            return True

        except Exception as e:
            self.log(f"✗ 加载Cookies失败: {str(e)}", "ERROR")
            return False

    def check_login_status(self) -> bool:
        """检查登录状态"""
        try:
            # 检查是否存在登录相关的元素或cookie
            cookies = self.driver.get_cookies()
            login_cookies = [c for c in cookies if 'login' in c['name'].lower() or 'session' in c['name'].lower() or 'token' in c['name'].lower()]

            if login_cookies:
                self.log(f"✓ 检测到登录相关Cookies: {len(login_cookies)} 个")
                return True

            # 检查页面中是否有登录用户信息
            try:
                user_elements = self.driver.find_elements(By.XPATH, "//a[contains(text(), '退出') or contains(text(), '登出')]")
                if user_elements:
                    self.log("✓ 检测到登录状态（找到退出链接）")
                    return True
            except:
                pass

            self.log("⚠ 未检测到登录状态")
            return False

        except Exception as e:
            self.log(f"检查登录状态失败: {str(e)}", "WARNING")
            return False

    def get_house_items_count(self) -> int:
        """获取房源列表项的数量"""
        try:
            # 确保在正确的窗口中
            if not self._ensure_correct_window():
                self.log("窗口切换失败", "ERROR")
                return 0

            # 等待房源列表容器加载
            self.log("等待房源列表容器加载...")
            try:
                self.wait.until(
                    EC.presence_of_element_located((By.XPATH, "//*[@id='guid-9018433170']/div/div"))
                )
                self.log("✓ 房源列表容器已加载")
            except TimeoutException:
                self.log("✗ 房源列表容器加载超时", "ERROR")
                return 0

            # 查找所有房源列表项
            house_item_selector = "//*[@id='guid-9018433170']/div/div/div[1]"
            self.log(f"查找房源列表项: {house_item_selector}")

            try:
                house_items = self.driver.find_elements(By.XPATH, house_item_selector)
                count = len(house_items)
                self.log(f"找到 {count} 个房源列表项")
                return min(count, 10)  # 限制最多10个房源

            except Exception as e:
                self.log(f"查找房源列表项失败: {str(e)}", "ERROR")
                return 0

        except Exception as e:
            self.log(f"获取房源数量失败: {str(e)}", "ERROR")
            return 0

    def collect_all_house_links(self, max_pages: int = 10) -> List[str]:
        """第三步：爬取房源列表链接 - 收集所有页面的房源链接"""
        self.log("=" * 60)
        self.log("第三步：爬取房源列表链接")
        self.log("=" * 60)

        all_house_links = []
        current_page = 1

        # 记录房源列表页面的标签页句柄
        list_page_handle = self.driver.current_window_handle
        self.log(f"房源列表页面句柄: {list_page_handle}")

        while current_page <= max_pages:
            if not self.is_running:
                self.log("爬取已停止")
                break

            self.log(f"\n--- 收集第 {current_page} 页房源链接 ---")

            # 确保在房源列表页面
            try:
                self.driver.switch_to.window(list_page_handle)
                self.log(f"确认在房源列表页面")
            except Exception as e:
                self.log(f"切换到房源列表页面失败: {str(e)}", "ERROR")
                break

            # 等待页面加载完成
            time.sleep(2)

            # 获取当前页面的房源链接
            page_links = self._extract_house_links_from_current_page()

            if not page_links:
                self.log(f"第 {current_page} 页未找到任何房源链接")
                break

            self.log(f"第 {current_page} 页找到 {len(page_links)} 个房源链接")
            all_house_links.extend(page_links)

            # 尝试切换到下一页
            if current_page < max_pages:
                if not self._go_to_next_page(current_page + 1):
                    self.log(f"无法切换到第 {current_page + 1} 页，结束收集")
                    break
                current_page += 1

                # 等待新页面加载
                time.sleep(3)
            else:
                self.log(f"已达到最大页数限制 ({max_pages} 页)")
                break

        self.log(f"✓ 第三步完成：共收集到 {len(all_house_links)} 个房源链接")
        return all_house_links

    def _extract_house_links_from_current_page(self) -> List[str]:
        """从当前页面提取所有房源链接 - 从指定容器递归查找所有a标签"""
        try:
            house_links = []

            # 查找房源列表容器
            list_container_selector = "//*[@id='guid-9018433170']/div"

            try:
                # 找到房源列表容器
                list_container = self.driver.find_element(By.XPATH, list_container_selector)
                self.log(f"找到房源列表容器: {list_container_selector}")

                # 在容器中递归查找所有a标签
                all_links = list_container.find_elements(By.XPATH, ".//a")
                self.log(f"在房源列表容器中找到 {len(all_links)} 个a标签")

                for i, link_element in enumerate(all_links):
                    try:
                        house_link = link_element.get_attribute('href')

                        # 验证链接有效性
                        if house_link and house_link.startswith('http'):
                            # 进一步验证是否是房源详情链接（可以根据URL特征判断）
                            if self._is_valid_house_link(house_link):
                                house_links.append(house_link)
                                self.log(f"收集第 {len(house_links)} 个房源链接: {house_link[:80]}...")
                            else:
                                self.log(f"跳过非房源链接: {house_link[:80]}...", "DEBUG")
                        else:
                            self.log(f"跳过无效链接: {house_link}", "DEBUG")

                    except Exception as e:
                        self.log(f"处理第 {i+1} 个a标签失败: {str(e)}", "WARNING")
                        continue

            except Exception as e:
                self.log(f"查找房源列表容器失败: {str(e)}", "ERROR")
                return []

            # 去重处理
            unique_links = list(set(house_links))
            if len(unique_links) != len(house_links):
                self.log(f"去重前: {len(house_links)} 个链接，去重后: {len(unique_links)} 个链接")

            return unique_links

        except Exception as e:
            self.log(f"从当前页面提取房源链接失败: {str(e)}", "ERROR")
            return []

    def _is_valid_house_link(self, link: str) -> bool:
        """判断是否是有效的房源详情链接"""
        try:
            # 根据阿里资产房源链接的特征进行判断
            # 通常房源详情链接包含特定的路径或参数
            valid_patterns = [
                '/detail',  # 详情页路径
                'itemId=',  # 商品ID参数
                '/item/',   # 商品路径
                'auction',  # 拍卖相关
            ]

            # 排除的模式（非房源链接）
            invalid_patterns = [
                'javascript:',
                '#',
                'mailto:',
                '/help',
                '/about',
                '/contact',
                '/login',
                '/register'
            ]

            # 检查是否包含排除模式
            for pattern in invalid_patterns:
                if pattern in link.lower():
                    return False

            # 检查是否包含有效模式
            for pattern in valid_patterns:
                if pattern in link.lower():
                    return True

            # 如果是淘宝域名的链接，默认认为是有效的
            if 'taobao.com' in link.lower():
                return True

            return False

        except Exception as e:
            self.log(f"验证链接有效性失败: {str(e)}", "WARNING")
            return True  # 出错时默认认为有效

    def scrape_house_links_from_url(self, list_page_url: str, max_pages: int = 10) -> List[str]:
        """直接从房源列表页面URL爬取所有房源链接"""
        self.log("=" * 60)
        self.log("直接爬取房源列表链接功能")
        self.log("=" * 60)

        try:
            # 初始化浏览器（如果还没有初始化）
            if not self.driver:
                if not self.init_driver():
                    self.log("浏览器初始化失败", "ERROR")
                    return []

            # 直接访问房源列表页面
            self.log(f"正在访问房源列表页面: {list_page_url}")
            self.driver.get(list_page_url)

            # 等待页面加载
            delay = Config.get_random_delay('page_load')
            self.log(f"等待页面加载 {delay:.1f} 秒...")
            time.sleep(delay)

            # 验证页面是否正确加载
            try:
                current_url = self.driver.current_url
                page_title = self.driver.title
                self.log(f"✓ 页面加载完成")
                self.log(f"当前URL: {current_url}")
                self.log(f"页面标题: {page_title}")
            except Exception as e:
                self.log(f"获取页面信息失败: {str(e)}", "WARNING")

            # 等待房源列表容器加载
            self.log("等待房源列表容器加载...")
            try:
                self.wait.until(
                    EC.any_of(
                        EC.presence_of_element_located((By.XPATH, "//*[@id='guid-9018433170']")),
                        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'item')]")),
                        EC.presence_of_element_located((By.XPATH, "//*[contains(@id, 'guid-')]"))
                    )
                )
                self.log("✓ 房源列表容器已加载")
            except TimeoutException:
                self.log("⚠ 等待房源列表容器超时，但继续尝试", "WARNING")

            # 收集所有页面的房源链接
            all_house_links = []
            current_page = 1

            while current_page <= max_pages:
                if not self.is_running:
                    self.log("爬取已停止")
                    break

                self.log(f"\n--- 收集第 {current_page} 页房源链接 ---")

                # 等待页面稳定
                time.sleep(2)

                # 获取当前页面的房源链接
                page_links = self._extract_house_links_from_current_page()

                if not page_links:
                    self.log(f"第 {current_page} 页未找到任何房源链接")
                    break

                self.log(f"第 {current_page} 页找到 {len(page_links)} 个房源链接")
                all_house_links.extend(page_links)

                # 尝试切换到下一页
                if current_page < max_pages:
                    if not self._go_to_next_page(current_page + 1):
                        self.log(f"无法切换到第 {current_page + 1} 页，结束收集")
                        break
                    current_page += 1

                    # 等待新页面加载
                    time.sleep(3)
                else:
                    self.log(f"已达到最大页数限制 ({max_pages} 页)")
                    break

            self.log(f"✓ 房源链接收集完成：共收集到 {len(all_house_links)} 个房源链接")

            # 去重处理
            unique_links = list(set(all_house_links))
            if len(unique_links) != len(all_house_links):
                self.log(f"去重后剩余 {len(unique_links)} 个唯一房源链接")

            return unique_links

        except Exception as e:
            self.log(f"从房源列表页面爬取链接失败: {str(e)}", "ERROR")
            return []

    def batch_scrape_house_details_by_clicking(self, max_pages: int = 10) -> Dict[str, int]:
        """第四步：通过点击房源项批量爬取房源详情数据"""
        self.log("=" * 60)
        self.log("第四步：通过点击房源项批量爬取房源详情数据")
        self.log("=" * 60)

        success_count = 0
        error_count = 0
        total_processed = 0
        current_page = 1

        # 记录房源列表页面的标签页句柄
        list_page_handle = self.driver.current_window_handle
        self.log(f"房源列表页面句柄: {list_page_handle}")

        try:
            while current_page <= max_pages:
                if not self.is_running:
                    self.log("爬取已停止")
                    break

                self.log(f"\n--- 处理第 {current_page} 页房源 ---")

                # 确保在房源列表页面
                self.driver.switch_to.window(list_page_handle)

                # 等待页面稳定
                time.sleep(2)

                # 获取当前页面的房源项数量
                try:
                    house_items = self.driver.find_elements(By.XPATH, "//*[@id='guid-9018433170']/div/div/div[1]")
                    page_house_count = len(house_items)
                    self.log(f"第 {current_page} 页找到 {page_house_count} 个房源项")

                    if page_house_count == 0:
                        self.log(f"第 {current_page} 页没有房源项，结束处理")
                        break

                except Exception as e:
                    self.log(f"获取第 {current_page} 页房源项失败: {str(e)}", "ERROR")
                    break

                # 逐个点击当前页面的房源项
                for item_index in range(page_house_count):
                    if not self.is_running:
                        self.log("爬取已停止")
                        break

                    # 检查暂停状态
                    while self.is_paused and self.is_running:
                        time.sleep(1)

                    if not self.is_running:
                        break

                    total_processed += 1
                    self.scraped_count += 1
                    self.update_progress(total_processed, page_house_count * max_pages, f"处理第 {current_page} 页第 {item_index + 1} 个房源")

                    self.log(f"\n--- 处理第 {current_page} 页第 {item_index + 1}/{page_house_count} 个房源 (总第 {total_processed} 个) ---")

                    try:
                        # 确保在房源列表页面
                        self.driver.switch_to.window(list_page_handle)

                        # 添加反爬措施：随机滚动页面
                        if random.random() < 0.4:  # 40%概率
                            self.log("执行反爬措施：随机滚动页面...")
                            scroll_amount = random.randint(100, 500)
                            self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                            time.sleep(random.uniform(0.5, 1.5))

                        # 添加反爬措施：随机鼠标移动
                        if random.random() < 0.3:  # 30%概率
                            self.log("执行反爬措施：随机鼠标移动...")
                            actions = ActionChains(self.driver)
                            actions.move_by_offset(random.randint(-100, 100), random.randint(-100, 100))
                            try:
                                actions.perform()
                            except:
                                pass
                            time.sleep(random.uniform(0.3, 0.8))

                        # 点击房源项并获取新标签页句柄
                        new_tab_handle = self.click_house_item_and_get_tab(item_index)

                        if new_tab_handle:
                            self.log(f"✓ 成功点击第 {item_index + 1} 个房源项，新标签页句柄: {new_tab_handle}")

                            # 切换到新标签页
                            self.driver.switch_to.window(new_tab_handle)

                            # 添加反爬措施：随机等待时间
                            load_delay = random.uniform(3, 8)
                            self.log(f"等待页面加载 {load_delay:.1f} 秒...")
                            time.sleep(load_delay)

                            # 添加反爬措施：模拟人类浏览行为
                            if random.random() < 0.3:  # 30%概率
                                self.log("模拟人类浏览行为...")
                                self._simulate_human_behavior()

                            # 提取房源数据
                            house_data = self.extract_house_data_from_tab(new_tab_handle, f"page_{current_page}_item_{item_index + 1}")

                            if house_data:
                                # 上传到API
                                result = self.api_client.create_house_with_retry(house_data)

                                if result['success']:
                                    success_count += 1
                                    self.success_count += 1
                                    self.log(f"✓ 第 {current_page} 页第 {item_index + 1} 个房源上传成功: {house_data.get('title', '未知标题')}")
                                else:
                                    error_count += 1
                                    self.error_count += 1
                                    self.log(f"✗ 第 {current_page} 页第 {item_index + 1} 个房源上传失败: {result['error']}", "ERROR")
                            else:
                                error_count += 1
                                self.error_count += 1
                                self.log(f"✗ 第 {current_page} 页第 {item_index + 1} 个房源数据提取失败", "ERROR")

                            # 关闭当前房源标签页
                            try:
                                self.driver.close()
                                self.log(f"✓ 已关闭第 {current_page} 页第 {item_index + 1} 个房源标签页")
                            except Exception as e:
                                self.log(f"关闭标签页失败: {str(e)}", "WARNING")

                            # 切换回房源列表页面
                            self.driver.switch_to.window(list_page_handle)

                        else:
                            error_count += 1
                            self.error_count += 1
                            self.log(f"✗ 第 {current_page} 页第 {item_index + 1} 个房源点击失败", "ERROR")

                    except Exception as e:
                        error_count += 1
                        self.error_count += 1
                        self.log(f"✗ 处理第 {current_page} 页第 {item_index + 1} 个房源时出错: {str(e)}", "ERROR")

                        # 尝试关闭可能打开的标签页并返回列表页面
                        try:
                            if len(self.driver.window_handles) > 1:
                                # 如果有多个标签页，关闭当前标签页
                                current_handle = self.driver.current_window_handle
                                if current_handle != list_page_handle:
                                    self.driver.close()
                                self.driver.switch_to.window(list_page_handle)
                        except:
                            pass

                    # 添加增强的随机延迟和反爬虫措施
                    if item_index < page_house_count - 1 or current_page < max_pages:
                        # 基础延迟
                        delay = Config.get_random_delay('data_extraction')
                        self.log(f"等待 {delay:.1f} 秒后处理下一个房源...")
                        time.sleep(delay)

                        # 每处理3个房源后增加额外延迟
                        if total_processed % 3 == 0:
                            burst_delay = Config.get_random_delay('burst')
                            self.log(f"已处理 {total_processed} 个房源，额外等待 {burst_delay:.1f} 秒...")
                            time.sleep(burst_delay)

                        # 随机模拟人类行为和增强反爬措施
                        if random.random() < 0.25:  # 25%概率
                            self.log("执行反爬措施：模拟人类浏览行为...")
                            self._simulate_human_behavior()

                        # 增强的反爬虫措施
                        if random.random() < 0.15:  # 15%概率
                            self.log("执行增强反爬措施...")
                            self._enhanced_anti_scraping_measures()

                # 尝试切换到下一页
                if current_page < max_pages:
                    self.log(f"\n--- 尝试切换到第 {current_page + 1} 页 ---")

                    # 确保在房源列表页面
                    self.driver.switch_to.window(list_page_handle)

                    if self._go_to_next_page(current_page + 1):
                        current_page += 1
                        self.log(f"✓ 成功切换到第 {current_page} 页")

                        # 页面切换后的额外等待
                        page_switch_delay = random.uniform(3, 6)
                        self.log(f"页面切换后等待 {page_switch_delay:.1f} 秒...")
                        time.sleep(page_switch_delay)
                    else:
                        self.log(f"无法切换到第 {current_page + 1} 页，结束处理")
                        break
                else:
                    self.log(f"已达到最大页数限制 ({max_pages} 页)")
                    break

        except Exception as e:
            self.log(f"批量处理过程中发生异常: {str(e)}", "ERROR")
            error_count += 1

        self.log(f"✓ 第四步完成：点击式批量处理完成，总处理: {total_processed}, 成功: {success_count}, 失败: {error_count}")

        return {
            'total': total_processed,
            'success': success_count,
            'error': error_count
        }

    def click_house_item_and_get_tab(self, item_index: int) -> Optional[str]:
        """点击指定索引的房源列表项并返回新标签页句柄"""
        try:
            self.log(f"\n--- 点击第 {item_index + 1} 个房源列表项 ---")

            # 查找房源列表项
            house_item_selector = f"//*[@id='guid-9018433170']/div/div/div[1][{item_index + 1}]"

            try:
                house_item = self.driver.find_element(By.XPATH, house_item_selector)
            except NoSuchElementException:
                # 如果精确索引失败，尝试查找所有项目然后选择指定索引
                house_items = self.driver.find_elements(By.XPATH, "//*[@id='guid-9018433170']/div/div/div[1]")
                if item_index < len(house_items):
                    house_item = house_items[item_index]
                else:
                    self.log(f"房源索引 {item_index + 1} 超出范围", "ERROR")
                    return None

            # 滚动到元素位置
            self.driver.execute_script("arguments[0].scrollIntoView(true);", house_item)
            time.sleep(0.5)

            # 记录点击前的窗口数量
            before_click_handles = self.driver.window_handles

            # 点击房源列表项的中心点 - 使用重试机制
            success = False
            new_tab_handle = None

            for attempt in range(3):  # 重试3次
                try:
                    if attempt > 0:
                        self.log(f"重试点击第 {item_index + 1} 个房源列表项 (第 {attempt + 1} 次尝试)")
                        time.sleep(2)
                        # 重新获取元素，避免stale element
                        try:
                            house_item = self.driver.find_element(By.XPATH, house_item_selector)
                        except NoSuchElementException:
                            house_items = self.driver.find_elements(By.XPATH, "//*[@id='guid-9018433170']/div/div/div[1]")
                            if item_index < len(house_items):
                                house_item = house_items[item_index]
                            else:
                                self.log(f"重试时未找到第 {item_index + 1} 个房源列表项", "ERROR")
                                continue

                    self.log(f"点击第 {item_index + 1} 个房源列表项 (尝试 {attempt + 1}/3)...")

                    # 重新滚动到元素位置
                    self.driver.execute_script("""
                        arguments[0].scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'center'
                        });
                    """, house_item)
                    time.sleep(1)

                    # 记录点击前的窗口数量
                    before_click_handles = self.driver.window_handles

                    # 点击房源列表项
                    actions = ActionChains(self.driver)
                    actions.move_to_element(house_item).click().perform()

                    # 等待新标签页打开
                    self.log("等待新标签页打开...")
                    max_wait = 5

                    for _ in range(max_wait * 2):  # 每0.5秒检查一次
                        current_handles = self.driver.window_handles
                        if len(current_handles) > len(before_click_handles):
                            new_tab_handle = current_handles[-1]
                            self.log(f"✓ 新标签页已打开: {new_tab_handle}")
                            success = True
                            break
                        time.sleep(0.5)

                    if success:
                        break
                    else:
                        self.log(f"第 {item_index + 1} 个房源未打开新标签页 (尝试 {attempt + 1}/3)", "WARNING")

                except Exception as e:
                    self.log(f"点击第 {item_index + 1} 个房源时出错 (尝试 {attempt + 1}/3): {str(e)}", "WARNING")

            if success:
                return new_tab_handle
            else:
                self.log(f"✗ 第 {item_index + 1} 个房源最终未能打开新标签页，已重试3次", "ERROR")
                return None

        except Exception as e:
            self.log(f"点击第 {item_index + 1} 个房源时出错: {str(e)}", "ERROR")
            return None

    def _go_to_next_page(self, target_page: int) -> bool:
        """切换到下一页"""
        try:
            self.log(f"尝试切换到第 {target_page} 页...")

            # 使用您提供的下一页按钮XPath
            next_button_xpath = '//*[@id="guid-8322645760"]/div/div[2]'

            try:
                # 查找下一页按钮
                next_button = self.driver.find_element(By.XPATH, next_button_xpath)
                self.log("找到下一页按钮")

                # 获取按钮详细信息用于调试
                try:
                    button_text = next_button.text.strip()
                    button_class = next_button.get_attribute('class') or ''
                    button_location = next_button.location
                    button_size = next_button.size
                    is_displayed = next_button.is_displayed()
                    is_enabled = next_button.is_enabled()

                    self.log(f"下一页按钮信息 - 文本: '{button_text}', 位置: {button_location}, 大小: {button_size}")
                    self.log(f"下一页按钮状态 - 可见: {is_displayed}, 可用: {is_enabled}, class: '{button_class}'")

                except Exception as e:
                    self.log(f"获取按钮信息失败: {str(e)}", "WARNING")

                # 检查按钮是否可用（不是禁用状态）
                button_class = next_button.get_attribute('class') or ''
                if 'disabled' in button_class.lower() or 'inactive' in button_class.lower():
                    self.log("下一页按钮已禁用，可能已到最后一页", "WARNING")
                    return False

                # 先滚动到按钮位置，确保可见
                self.log("滚动到下一页按钮位置...")
                try:
                    # 方法1：滚动到元素位置
                    self.driver.execute_script("""
                        arguments[0].scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'center'
                        });
                    """, next_button)
                    time.sleep(3)

                    # 检查按钮是否现在可见
                    if not next_button.is_displayed():
                        self.log("按钮仍不可见，尝试强制滚动到页面底部...")
                        # 方法2：强制滚动到页面底部
                        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                        time.sleep(2)

                        # 再次检查
                        if not next_button.is_displayed():
                            self.log("按钮仍不可见，尝试滚动到按钮的Y坐标位置...")
                            # 方法3：直接滚动到按钮的Y坐标
                            button_y = next_button.location['y']
                            self.driver.execute_script(f"window.scrollTo(0, {button_y - 200});")
                            time.sleep(2)

                    # 最终检查按钮可见性（但不依赖此判断）
                    final_visible = next_button.is_displayed()
                    self.log(f"滚动后按钮可见性检查: {final_visible}")

                    # 不管is_displayed()的结果如何，都尝试点击
                    # 因为有时CSS样式会影响is_displayed()的判断，但按钮实际上是可点击的
                    self.log("准备点击下一页按钮（忽略可见性检查）")

                except Exception as e:
                    self.log(f"滚动到按钮位置失败: {str(e)}", "WARNING")

                # 使用三种点击方法尝试点击下一页按钮
                success = self._safe_click_element(next_button, "下一页按钮")

                if success:
                    self.log(f"✓ 成功点击下一页按钮")

                    # 等待页面加载
                    self.log("等待新页面加载...")
                    time.sleep(5)

                    # 等待房源列表重新加载
                    try:
                        self.wait.until(
                            EC.presence_of_element_located((By.XPATH, "//*[@id='guid-9018433170']/div"))
                        )
                        self.log("✓ 新页面房源列表已加载")
                    except TimeoutException:
                        self.log("⚠ 等待新页面房源列表超时，但继续执行", "WARNING")

                    return True
                else:
                    self.log("点击下一页按钮失败", "ERROR")
                    return False

            except NoSuchElementException:
                self.log("未找到下一页按钮，可能已到最后一页", "WARNING")
                return False

        except Exception as e:
            self.log(f"切换到下一页失败: {str(e)}", "ERROR")
            return False

    def extract_house_data_from_tab(self, tab_handle: str, tab_index: int) -> Optional[Dict[str, Any]]:
        """从指定的标签页中提取房源数据"""
        try:
            self.log(f"\n--- 提取第 {tab_index} 个标签页的房源数据 ---")
            self.log(f"切换到标签页: {tab_handle}")

            # 切换到指定的标签页
            self.driver.switch_to.window(tab_handle)

            # 固定等待10秒，不等页面完全加载
            self.log("等待10秒后开始提取数据...")
            time.sleep(10)

            # 获取当前页面信息
            try:
                current_url = self.driver.current_url
                self.log(f"当前页面URL: {current_url}")
            except Exception as e:
                self.log(f"获取URL失败: {str(e)}", "WARNING")
                current_url = "未知URL"

            # 提取房源数据
            house_data = {}

            # 提取拍卖类型 - 缩短等待时间
            try:
                # 使用较短的等待时间
                short_wait = WebDriverWait(self.driver, 5)
                auction_type_element = short_wait.until(
                    EC.presence_of_element_located((By.XPATH, Config.XPATH_SELECTORS['auction_type']))
                )
                house_data['auctionType'] = auction_type_element.text.strip()
                self.log(f"拍卖类型: {house_data['auctionType']}")
            except TimeoutException:
                house_data['auctionType'] = "未知"
                self.log("未找到拍卖类型元素（5秒超时）", "WARNING")
            except Exception as e:
                house_data['auctionType'] = "未知"
                self.log(f"提取拍卖类型失败: {str(e)}", "WARNING")

            # 提取标题 - 直接查找，不等待
            try:
                title_element = self.driver.find_element(By.XPATH, '//*[@id="page"]/div[4]/div/div/h1')
                title_text = title_element.text.strip()
                # 移除拍卖类型部分，只保留标题
                if house_data['auctionType'] != "未知" and house_data['auctionType'] in title_text:
                    title_text = title_text.replace(house_data['auctionType'], '').strip()
                house_data['title'] = title_text
                self.log(f"房源标题: {house_data['title']}")
            except NoSuchElementException:
                house_data['title'] = "未知标题"
                self.log("未找到标题元素", "WARNING")
            except Exception as e:
                house_data['title'] = "未知标题"
                self.log(f"提取标题失败: {str(e)}", "WARNING")

            # 提取结束时间 - 如果在开始时间逻辑中未设置，则使用快速提取
            if 'endTime' not in house_data:
                try:
                    house_data['endTime'] = self._extract_end_time_fast()
                except Exception as e:
                    house_data['endTime'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self.log(f"提取结束时间失败: {str(e)}", "WARNING")
            else:
                self.log(f"结束时间已在开始时间逻辑中设置: {house_data['endTime']}")

            # 提取开拍时间 (startTime) - 新规则
            try:
                import re
                from datetime import datetime, timedelta

                # 首先尝试原有逻辑：查找配置的开拍时间元素
                try:
                    start_time_element = self.driver.find_element(By.XPATH, '//*[@id="sf-countdown"]/span[3]')
                    start_time_text = start_time_element.text.strip()
                    # 提取括号中的时间，格式如：(2025-07-31 10:00开拍)
                    time_match = re.search(r'\((\d{4}-\d{2}-\d{2} \d{2}:\d{2})', start_time_text)
                    if time_match:
                        house_data['startTime'] = time_match.group(1) + ":00"  # 添加秒数
                        self.log(f"开拍时间（配置元素）: {house_data['startTime']}")

                        # 结束时间 = 开始时间 + 竞价周期
                        start_dt = datetime.strptime(house_data['startTime'], "%Y-%m-%d %H:%M:%S")
                        bidding_cycle_days = house_data.get('biddingCycle', 1)
                        end_dt = start_dt + timedelta(days=bidding_cycle_days)
                        house_data['endTime'] = end_dt.strftime("%Y-%m-%d %H:%M:%S")
                        self.log(f"结束时间（开始时间+竞价周期）: {house_data['endTime']}")

                        start_time_found = True
                    else:
                        start_time_found = False
                        self.log("开拍时间格式解析失败", "WARNING")
                except NoSuchElementException:
                    start_time_found = False
                    self.log("未找到配置的开拍时间元素", "WARNING")

                # 如果配置元素不存在，检查新规则
                if not start_time_found:
                    try:
                        # 检查 //*[@id="sf-countdown"]/span[1] 的值
                        countdown_label = self.driver.find_element(By.XPATH, '//*[@id="sf-countdown"]/span[1]')
                        countdown_label_text = countdown_label.text.strip()
                        self.log(f"倒计时标签: {countdown_label_text}")

                        if countdown_label_text == "距结束":
                            self.log("检测到'距结束'状态，使用倒计时计算开始和结束时间")

                            # 提取倒计时数据 - 使用正确的XPath
                            countdown_container = self.driver.find_element(By.XPATH, '//*[@id="sf-countdown"]/span[2]')
                            vars_elements = countdown_container.find_elements(By.XPATH, './/var')
                            ems_elements = countdown_container.find_elements(By.XPATH, './/em')

                            self.log(f"找到 {len(vars_elements)} 个var元素，{len(ems_elements)} 个em元素")

                            time_parts = []
                            for element in vars_elements + ems_elements:
                                try:
                                    text = element.text.strip()
                                    if text.replace('.', '').isdigit():  # 支持小数秒
                                        time_parts.append(float(text))
                                except:
                                    continue

                            if len(time_parts) >= 4:  # 天、时、分、秒
                                days, hours, minutes, seconds = time_parts[:4]
                                self.log(f"倒计时: {days}天 {hours}时 {minutes}分 {seconds}秒")

                                # 计算结束时间 = 当前时间 + 倒计时
                                now = datetime.now()
                                end_time = now + timedelta(days=days, hours=hours, minutes=minutes, seconds=seconds)
                                house_data['endTime'] = end_time.strftime("%Y-%m-%d %H:%M:%S")

                                # 开始时间 = 结束时间 - 竞价周期
                                bidding_cycle_days = house_data.get('biddingCycle', 1)
                                start_time = end_time - timedelta(days=bidding_cycle_days)
                                house_data['startTime'] = start_time.strftime("%Y-%m-%d %H:%M:%S")

                                self.log(f"开拍时间（倒计时计算）: {house_data['startTime']}")
                                self.log(f"结束时间（倒计时计算）: {house_data['endTime']}")
                            else:
                                # 倒计时数据不完整，使用默认值
                                house_data['startTime'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                self.log("倒计时数据不完整，使用当前时间作为开拍时间", "WARNING")
                        else:
                            # 不是"距结束"状态，使用默认值
                            house_data['startTime'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            self.log(f"倒计时标签不是'距结束'（{countdown_label_text}），使用当前时间", "WARNING")

                    except NoSuchElementException:
                        house_data['startTime'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        self.log("未找到倒计时标签元素，使用当前时间", "WARNING")

            except Exception as e:
                house_data['startTime'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.log(f"提取开拍时间失败: {str(e)}", "ERROR")

            # 提取起拍价 (startingPrice)
            try:
                starting_price_element = self.driver.find_element(By.XPATH, '//*[@id="J_COMPONENT_MAIN_BOTTOM"]/div[3]/div/table/tbody/tr[2]/td[1]/div/div/div/div/div/div/span[2]')
                starting_price_text = starting_price_element.text.strip().replace(',', '')
                house_data['startingPrice'] = float(starting_price_text) if starting_price_text.replace('.', '').isdigit() else 0.0
                self.log(f"起拍价: {house_data['startingPrice']}")
            except (NoSuchElementException, ValueError):
                house_data['startingPrice'] = 0.0
                self.log("未找到起拍价元素或格式错误", "WARNING")
            except Exception as e:
                house_data['startingPrice'] = 0.0
                self.log(f"提取起拍价失败: {str(e)}", "WARNING")

            # 提取评估价 (evaluationPrice)
            try:
                evaluation_price_element = self.driver.find_element(By.XPATH, '//*[@id="J_COMPONENT_MAIN_BOTTOM"]/div[3]/div/table/tbody/tr[2]/td[2]/div/div/div/div/div/div/span[2]')
                evaluation_price_text = evaluation_price_element.text.strip().replace(',', '')
                house_data['evaluationPrice'] = float(evaluation_price_text) if evaluation_price_text.replace('.', '').isdigit() else 0.0
                self.log(f"评估价: {house_data['evaluationPrice']}")
            except (NoSuchElementException, ValueError):
                house_data['evaluationPrice'] = 0.0
                self.log("未找到评估价元素或格式错误", "WARNING")
            except Exception as e:
                house_data['evaluationPrice'] = 0.0
                self.log(f"提取评估价失败: {str(e)}", "WARNING")

            # 提取加价幅度 (priceIncrement)
            try:
                price_increment_element = self.driver.find_element(By.XPATH, '//*[@id="J_COMPONENT_MAIN_BOTTOM"]/div[3]/div/table/tbody/tr[3]/td[1]/div/div/div/div/div/div/span[2]')
                price_increment_text = price_increment_element.text.strip().replace(',', '')
                house_data['priceIncrement'] = float(price_increment_text) if price_increment_text.replace('.', '').isdigit() else 0.0
                self.log(f"加价幅度: {house_data['priceIncrement']}")
            except (NoSuchElementException, ValueError):
                house_data['priceIncrement'] = 0.0
                self.log("未找到加价幅度元素或格式错误", "WARNING")
            except Exception as e:
                house_data['priceIncrement'] = 0.0
                self.log(f"提取加价幅度失败: {str(e)}", "WARNING")

            # 提取竞价周期 (biddingCycle)
            try:
                bidding_cycle_element = self.driver.find_element(By.XPATH, '//*[@id="J_COMPONENT_MAIN_BOTTOM"]/div[3]/div/table/tbody/tr[3]/td[2]/div/div/div/div/span')
                bidding_cycle_text = bidding_cycle_element.text.strip()
                # 提取数字部分，如"1天"中的"1"
                cycle_match = re.search(r'(\d+)', bidding_cycle_text)
                if cycle_match:
                    house_data['biddingCycle'] = int(cycle_match.group(1))
                    self.log(f"竞价周期: {house_data['biddingCycle']} 天")
                else:
                    house_data['biddingCycle'] = 1
                    self.log("竞价周期格式解析失败，默认为1天", "WARNING")
            except NoSuchElementException:
                house_data['biddingCycle'] = 1
                self.log("未找到竞价周期元素", "WARNING")
            except Exception as e:
                house_data['biddingCycle'] = 1
                self.log(f"提取竞价周期失败: {str(e)}", "WARNING")

            # 提取评估报告链接 (evaluationReport)
            try:
                evaluation_report_element = self.driver.find_element(By.XPATH, '//*[@id="J_DownLoadFirst"]/a[1]')
                evaluation_report_href = evaluation_report_element.get_attribute('href')
                house_data['evaluationReport'] = evaluation_report_href if evaluation_report_href else ""
                self.log(f"评估报告链接: {house_data['evaluationReport']}")
            except NoSuchElementException:
                house_data['evaluationReport'] = ""
                self.log("未找到评估报告链接", "WARNING")
            except Exception as e:
                house_data['evaluationReport'] = ""
                self.log(f"提取评估报告链接失败: {str(e)}", "WARNING")

            # 提取裁定书链接 (executionOrder)
            try:
                execution_order_element = self.driver.find_element(By.XPATH, '//*[@id="J_DownLoadFirst"]/a[2]')
                execution_order_href = execution_order_element.get_attribute('href')
                house_data['executionOrder'] = execution_order_href if execution_order_href else ""
                self.log(f"裁定书链接: {house_data['executionOrder']}")
            except NoSuchElementException:
                house_data['executionOrder'] = ""
                self.log("未找到裁定书链接", "WARNING")
            except Exception as e:
                house_data['executionOrder'] = ""
                self.log(f"提取裁定书链接失败: {str(e)}", "WARNING")

            # 提取财产报告链接 (propertyReport)
            try:
                property_report_element = self.driver.find_element(By.XPATH, '//*[@id="J_DownLoadFirst"]/a[3]')
                property_report_href = property_report_element.get_attribute('href')
                house_data['propertyReport'] = property_report_href if property_report_href else ""
                self.log(f"财产报告链接: {house_data['propertyReport']}")
            except NoSuchElementException:
                house_data['propertyReport'] = ""
                self.log("未找到财产报告链接", "WARNING")
            except Exception as e:
                house_data['propertyReport'] = ""
                self.log(f"提取财产报告链接失败: {str(e)}", "WARNING")

            # 提取情况调查表截图 (situationSurvey)
            try:
                self.log("开始截图情况调查表...")

                # 首先尝试截图table元素（情况调查表）
                situation_survey_url = self._screenshot_and_upload_element_full('//*[@id="J_desc"]/table', f"situation_survey_{tab_index}")

                if situation_survey_url:
                    house_data['situationSurvey'] = situation_survey_url
                    self.log(f"✓ 情况调查表截图成功: {house_data['situationSurvey']}")
                else:
                    # 如果找不到情况调查表，则截图指定的备用元素
                    self.log("未找到情况调查表，尝试截图备用元素...")
                    backup_element_xpath = '//*[@id="J_desc"]/div/div/div/div[3]/div[2]/span/p[11]/img'
                    backup_url = self._screenshot_and_upload_element_full(backup_element_xpath, f"situation_survey_backup_{tab_index}")

                    if backup_url:
                        house_data['situationSurvey'] = backup_url
                        self.log(f"✓ 备用元素截图成功: {house_data['situationSurvey']}")
                    else:
                        house_data['situationSurvey'] = ""
                        self.log("⚠ 情况调查表和备用元素都未找到", "WARNING")

            except Exception as e:
                house_data['situationSurvey'] = ""
                self.log(f"提取情况调查表截图失败: {str(e)}", "WARNING")

            # 提取详情图片列表 (detailImageList)
            try:
                self.log("开始截图详情图片...")
                detail_image_urls = []

                # 查找第一个图片容器中的所有img元素
                try:
                    container1_xpath = '//*[@id="J_desc"]/div'
                    urls1 = self._screenshot_images_in_container(container1_xpath, f"detail_images_1_{tab_index}")
                    detail_image_urls.extend(urls1)
                except Exception as e:
                    self.log(f"截图第一个图片容器中的图片失败: {str(e)}", "WARNING")

                # 查找第二个图片容器中的所有img元素
                try:
                    container2_xpath = '//*[@id="J_ItemDetailContent"]/div[5]'
                    urls2 = self._screenshot_images_in_container(container2_xpath, f"detail_images_2_{tab_index}")
                    detail_image_urls.extend(urls2)
                except Exception as e:
                    self.log(f"截图第二个图片容器中的图片失败: {str(e)}", "WARNING")

                # 构建逗号分隔的URL列表
                house_data['detailImageList'] = ",".join(detail_image_urls) if detail_image_urls else ""
                self.log(f"详情图片列表 (共{len(detail_image_urls)}张): {house_data['detailImageList']}")
            except Exception as e:
                house_data['detailImageList'] = ""
                self.log(f"提取详情图片列表失败: {str(e)}", "WARNING")

            # 提取地图信息截图 (mapInfo)
            try:
                self.log("开始截图地图信息...")
                # 使用精确的地图canvas元素XPath
                map_info_url = self._screenshot_and_upload_element_full('//*[@id="J_Map"]/div[1]/div/div/div[1]/div/div[1]/canvas[2]', f"map_info_{tab_index}")
                house_data['mapInfo'] = map_info_url if map_info_url else ""
                self.log(f"地图信息截图: {house_data['mapInfo']}")
            except Exception as e:
                house_data['mapInfo'] = ""
                self.log(f"提取地图信息截图失败: {str(e)}", "WARNING")

            # 设置房屋类型（根据当前爬取的类型）
            current_house_type = getattr(self, 'house_type', '住宅')
            house_data['houseType'] = current_house_type
            self.log(f"房屋类型设置为: {current_house_type}")

            # 记录当前URL
            house_data['sourceUrl'] = current_url

            self.log(f"✓ 第 {tab_index} 个房源数据提取完成")
            return house_data

        except Exception as e:
            self.log(f"✗ 提取第 {tab_index} 个标签页房源数据失败: {str(e)}", "ERROR")
            return None

    def extract_house_data(self, url: str) -> Optional[Dict[str, Any]]:
        """从房源详情页提取数据"""
        try:
            self.log(f"正在提取房源数据: {url}")

            # 访问房源详情页
            self.driver.get(url)

            # 随机延迟
            delay = Config.get_random_delay('page_load')
            time.sleep(delay)

            # 提取数据
            house_data = {}

            # 提取拍卖类型
            try:
                auction_type_element = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, Config.XPATH_SELECTORS['auction_type']))
                )
                house_data['auctionType'] = auction_type_element.text.strip()
            except TimeoutException:
                house_data['auctionType'] = "未知"
                self.log("未找到拍卖类型元素", "WARNING")

            # 提取标题
            try:
                title_element = self.driver.find_element(By.XPATH, '//*[@id="page"]/div[4]/div/div/h1')
                title_text = title_element.text.strip()
                # 移除拍卖类型部分，只保留标题
                if house_data['auctionType'] in title_text:
                    title_text = title_text.replace(house_data['auctionType'], '').strip()
                house_data['title'] = title_text
            except NoSuchElementException:
                house_data['title'] = "未知标题"
                self.log("未找到标题元素", "WARNING")

            # 提取结束时间
            house_data['endTime'] = self._extract_end_time()

            # 设置房屋类型（根据当前爬取的类型）
            current_house_type = getattr(self, 'house_type', '住宅')
            house_data['houseType'] = current_house_type
            self.log(f"房屋类型设置为: {current_house_type}")

            self.log(f"数据提取完成: {house_data['title']}")
            return house_data

        except Exception as e:
            self.log(f"提取房源数据失败: {str(e)}", "ERROR")
            return None

    def _extract_end_time(self) -> str:
        """提取结束时间"""
        try:
            # 查找倒计时容器
            countdown_container = self.driver.find_element(
                By.XPATH, Config.XPATH_SELECTORS['end_time_container']
            )

            # 提取所有时间数字
            vars_elements = countdown_container.find_elements(By.XPATH, Config.XPATH_SELECTORS['end_time_vars'])
            ems_elements = countdown_container.find_elements(By.XPATH, Config.XPATH_SELECTORS['end_time_ems'])

            time_parts = []
            for element in vars_elements + ems_elements:
                text = element.text.strip()
                if text.isdigit():
                    time_parts.append(int(text))

            if len(time_parts) >= 4:  # 天、时、分、秒
                days, hours, minutes, seconds = time_parts[:4]

                # 计算结束时间
                now = datetime.now()
                end_time = now + timedelta(days=days, hours=hours, minutes=minutes, seconds=seconds)

                return end_time.strftime("%Y-%m-%d %H:%M:%S")
            else:
                return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        except Exception as e:
            self.log(f"提取结束时间失败: {str(e)}", "WARNING")
            return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _extract_end_time_fast(self) -> str:
        """快速提取结束时间 - 不等待元素加载"""
        try:
            # 直接查找倒计时容器，不等待
            countdown_container = self.driver.find_element(
                By.XPATH, Config.XPATH_SELECTORS['end_time_container']
            )

            # 提取所有时间数字
            vars_elements = countdown_container.find_elements(By.XPATH, Config.XPATH_SELECTORS['end_time_vars'])
            ems_elements = countdown_container.find_elements(By.XPATH, Config.XPATH_SELECTORS['end_time_ems'])

            time_parts = []
            for element in vars_elements + ems_elements:
                try:
                    text = element.text.strip()
                    if text.isdigit():
                        time_parts.append(int(text))
                except:
                    continue

            if len(time_parts) >= 4:  # 天、时、分、秒
                days, hours, minutes, seconds = time_parts[:4]

                # 计算结束时间
                now = datetime.now()
                end_time = now + timedelta(days=days, hours=hours, minutes=minutes, seconds=seconds)

                return end_time.strftime("%Y-%m-%d %H:%M:%S")
            else:
                return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        except Exception as e:
            self.log(f"快速提取结束时间失败: {str(e)}", "WARNING")
            return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _screenshot_and_upload_element(self, xpath: str, filename_prefix: str) -> str:
        """截图指定元素并上传到服务器"""
        try:
            import requests
            import tempfile
            import os
            from image_watermark import add_watermark_to_image_data

            # 查找元素
            try:
                element = self.driver.find_element(By.XPATH, xpath)
                self.log(f"找到截图元素: {xpath}")
            except NoSuchElementException:
                self.log(f"未找到截图元素: {xpath}", "WARNING")
                return ""

            # 滚动到元素位置 - 将元素放在页面中心
            self.driver.execute_script("""
                arguments[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'center'
                });
            """, element)
            time.sleep(2)

            # 截图元素
            screenshot_data = element.screenshot_as_png

            # 添加水印
            try:
                watermarked_data = add_watermark_to_image_data(screenshot_data)
                self.log(f"✓ 成功为截图添加水印: {filename_prefix}")
            except Exception as e:
                self.log(f"添加水印失败，使用原图: {str(e)}", "WARNING")
                watermarked_data = screenshot_data

            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png', prefix=f'{filename_prefix}_') as temp_file:
                temp_file.write(watermarked_data)
                temp_file_path = temp_file.name

            try:
                # 上传到服务器
                upload_url = f"{self.api_client.base_url}/api/cos/upload"

                with open(temp_file_path, 'rb') as f:
                    files = {'file': (f'{filename_prefix}.png', f, 'image/png')}

                    # 为文件上传创建专门的headers，移除Content-Type让requests自动设置
                    upload_headers = {}
                    for key, value in self.api_client.session.headers.items():
                        if key.lower() != 'content-type':  # 不包含Content-Type，让requests自动设置为multipart/form-data
                            upload_headers[key] = value

                    response = requests.post(
                        upload_url,
                        files=files,
                        headers=upload_headers,
                        timeout=30
                    )

                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == 200 and result.get('data'):
                        file_url = result['data'].get('fileUrl', '')
                        self.log(f"✓ 截图上传成功: {file_url}")
                        return file_url
                    else:
                        self.log(f"上传响应错误: {result}", "ERROR")
                        return ""
                else:
                    self.log(f"上传请求失败: {response.status_code} - {response.text}", "ERROR")
                    return ""

            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

        except Exception as e:
            self.log(f"截图和上传失败: {str(e)}", "ERROR")
            return ""

    def _screenshot_and_upload_element_full(self, xpath: str, filename_prefix: str) -> str:
        """截图指定元素的完整内容并上传到服务器（处理大元素）"""
        try:
            import requests
            import tempfile
            import os
            from image_watermark import add_watermark_to_image_data

            # 查找元素
            try:
                element = self.driver.find_element(By.XPATH, xpath)
                self.log(f"找到截图元素: {xpath}")
            except NoSuchElementException:
                self.log(f"未找到截图元素: {xpath}", "WARNING")
                return ""

            # 获取元素的位置和大小
            element_location = element.location
            element_size = element.size
            self.log(f"元素位置: {element_location}, 大小: {element_size}")

            # 滚动到元素中心位置
            self.driver.execute_script("""
                arguments[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'center'
                });
            """, element)
            time.sleep(2)

            # 如果元素很高，需要特殊处理
            viewport_height = self.driver.execute_script("return window.innerHeight")
            element_height = element_size['height']

            if element_height > viewport_height:
                self.log(f"元素高度 {element_height}px 超过视窗高度 {viewport_height}px，使用完整页面截图方式")

                # 滚动到元素中心位置
                self.driver.execute_script("""
                    arguments[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'center'
                    });
                """, element)
                time.sleep(2)

                # 获取完整页面截图
                full_screenshot = self.driver.get_screenshot_as_png()

                # 使用PIL裁剪出元素区域
                try:
                    from PIL import Image
                    import io

                    # 打开完整截图
                    img = Image.open(io.BytesIO(full_screenshot))

                    # 获取元素在页面中的实际位置
                    element_rect = self.driver.execute_script("""
                        var rect = arguments[0].getBoundingClientRect();
                        return {
                            x: rect.left,
                            y: rect.top,
                            width: rect.width,
                            height: rect.height
                        };
                    """, element)

                    # 裁剪元素区域
                    left = max(0, int(element_rect['x']))
                    top = max(0, int(element_rect['y']))
                    right = min(img.width, int(element_rect['x'] + element_rect['width']))
                    bottom = min(img.height, int(element_rect['y'] + element_rect['height']))

                    cropped_img = img.crop((left, top, right, bottom))

                    # 保存裁剪后的图片
                    screenshot_buffer = io.BytesIO()
                    cropped_img.save(screenshot_buffer, format='PNG')
                    screenshot_data = screenshot_buffer.getvalue()

                except ImportError:
                    self.log("PIL库未安装，使用元素直接截图", "WARNING")
                    screenshot_data = element.screenshot_as_png
                except Exception as e:
                    self.log(f"图片裁剪失败，使用元素直接截图: {str(e)}", "WARNING")
                    screenshot_data = element.screenshot_as_png
            else:
                # 元素不太大，直接截图
                screenshot_data = element.screenshot_as_png

            # 添加水印
            try:
                watermarked_data = add_watermark_to_image_data(screenshot_data)
                self.log(f"✓ 成功为完整截图添加水印: {filename_prefix}")
            except Exception as e:
                self.log(f"添加水印失败，使用原图: {str(e)}", "WARNING")
                watermarked_data = screenshot_data

            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png', prefix=f'{filename_prefix}_') as temp_file:
                temp_file.write(watermarked_data)
                temp_file_path = temp_file.name

            try:
                # 上传到服务器
                upload_url = f"{self.api_client.base_url}/api/cos/upload"

                with open(temp_file_path, 'rb') as f:
                    files = {'file': (f'{filename_prefix}.png', f, 'image/png')}

                    # 为文件上传创建专门的headers
                    upload_headers = {}
                    for key, value in self.api_client.session.headers.items():
                        if key.lower() != 'content-type':
                            upload_headers[key] = value

                    response = requests.post(
                        upload_url,
                        files=files,
                        headers=upload_headers,
                        timeout=30
                    )

                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == 200 and result.get('data'):
                        file_url = result['data'].get('fileUrl', '')
                        self.log(f"✓ 完整元素截图上传成功: {file_url}")
                        return file_url
                    else:
                        self.log(f"上传响应错误: {result}", "ERROR")
                        return ""
                else:
                    self.log(f"上传请求失败: {response.status_code} - {response.text}", "ERROR")
                    return ""

            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

        except Exception as e:
            self.log(f"完整元素截图和上传失败: {str(e)}", "ERROR")
            return ""

    def _screenshot_images_in_container(self, container_xpath: str, filename_prefix: str) -> list:
        """递归查找容器中的所有img元素并截图上传"""
        try:
            from image_watermark import add_watermark_to_image_data
            image_urls = []

            # 查找容器元素
            try:
                container = self.driver.find_element(By.XPATH, container_xpath)
                self.log(f"找到图片容器: {container_xpath}")
            except NoSuchElementException:
                self.log(f"未找到图片容器: {container_xpath}", "WARNING")
                return []

            # 在容器中递归查找所有img元素
            img_elements = container.find_elements(By.XPATH, ".//img")
            self.log(f"在容器中找到 {len(img_elements)} 个img元素")

            for i, img_element in enumerate(img_elements):
                try:
                    # 检查img元素是否可见和有效
                    if not img_element.is_displayed():
                        self.log(f"跳过不可见的img元素 {i+1}", "WARNING")
                        continue

                    # 获取img的src属性
                    img_src = img_element.get_attribute('src')
                    if not img_src or img_src.startswith('data:'):
                        self.log(f"跳过无效的img元素 {i+1} (src: {img_src[:50] if img_src else 'None'})", "WARNING")
                        continue

                    self.log(f"截图第 {i+1} 个img元素: {img_src[:100]}...")

                    # 滚动到img元素中心位置
                    self.driver.execute_script("""
                        arguments[0].scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'center'
                        });
                    """, img_element)
                    time.sleep(1)

                    # 截图img元素
                    screenshot_data = img_element.screenshot_as_png

                    # 添加水印
                    try:
                        watermarked_data = add_watermark_to_image_data(screenshot_data)
                        self.log(f"✓ 成功为第 {i+1} 个img添加水印: {filename_prefix}")
                    except Exception as e:
                        self.log(f"为第 {i+1} 个img添加水印失败，使用原图: {str(e)}", "WARNING")
                        watermarked_data = screenshot_data

                    # 创建临时文件
                    import tempfile
                    import os
                    import requests
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.png', prefix=f'{filename_prefix}_img_{i+1}_') as temp_file:
                        temp_file.write(watermarked_data)
                        temp_file_path = temp_file.name

                    try:
                        # 上传到服务器
                        upload_url = f"{self.api_client.base_url}/api/cos/upload"

                        with open(temp_file_path, 'rb') as f:
                            files = {'file': (f'{filename_prefix}_img_{i+1}.png', f, 'image/png')}

                            # 为文件上传创建专门的headers
                            upload_headers = {}
                            for key, value in self.api_client.session.headers.items():
                                if key.lower() != 'content-type':
                                    upload_headers[key] = value

                            response = requests.post(
                                upload_url,
                                files=files,
                                headers=upload_headers,
                                timeout=30
                            )

                        if response.status_code == 200:
                            result = response.json()
                            if result.get('code') == 200 and result.get('data'):
                                file_url = result['data'].get('fileUrl', '')
                                self.log(f"✓ 第 {i+1} 个img截图上传成功: {file_url}")
                                image_urls.append(file_url)
                            else:
                                self.log(f"第 {i+1} 个img上传响应错误: {result}", "ERROR")
                        else:
                            self.log(f"第 {i+1} 个img上传请求失败: {response.status_code} - {response.text}", "ERROR")

                    finally:
                        # 清理临时文件
                        try:
                            os.unlink(temp_file_path)
                        except:
                            pass

                except Exception as e:
                    self.log(f"截图第 {i+1} 个img元素失败: {str(e)}", "ERROR")
                    continue

            self.log(f"容器 {container_xpath} 中成功截图 {len(image_urls)} 个img元素")
            return image_urls

        except Exception as e:
            self.log(f"截图容器中的img元素失败: {str(e)}", "ERROR")
            return []

    def _safe_click(self, xpath: str, element_name: str, wait_after: bool = True, max_retries: int = 3) -> bool:
        """安全点击元素 - 支持重试"""
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    self.log(f"重试点击 {element_name} (第 {attempt + 1} 次尝试)")
                    time.sleep(2)  # 重试前等待2秒

                # 等待元素存在（不要求可见）
                element = self.wait.until(EC.presence_of_element_located((By.XPATH, xpath)))

                # 检查元素是否可用（但不要求可见）
                if not element.is_enabled():
                    self.log(f"元素 {element_name} 不可用，但继续尝试", "WARNING")

                # 尝试3种点击方法
                click_methods = [
                    ("selenium自带click()", lambda: element.click()),
                    ("ActionChains鼠标操作", lambda: ActionChains(self.driver).move_to_element(element).click().perform()),
                    ("JavaScript操作", lambda: self.driver.execute_script("arguments[0].click();", element))
                ]

                click_success = False
                for method_name, click_method in click_methods:
                    try:
                        self.log(f"尝试 {method_name}...")
                        click_method()
                        self.log(f"✓ {method_name} 成功点击 {element_name}")
                        click_success = True
                        break
                    except Exception as click_e:
                        self.log(f"{method_name} 失败: {str(click_e)}", "WARNING")
                        continue

                if not click_success:
                    raise Exception("所有点击方法都失败了")

                if wait_after:
                    delay = Config.get_random_delay('page_load')
                    time.sleep(delay)

                return True

            except (TimeoutException, ElementClickInterceptedException, NoSuchElementException) as e:
                log_level = "WARNING" if attempt < max_retries - 1 else "ERROR"
                self.log(f"点击 {element_name} 失败: {str(e)} (尝试 {attempt + 1}/{max_retries})", log_level)

                if attempt < max_retries - 1:
                    # 不是最后一次尝试，继续重试
                    continue

        self.log(f"✗ 点击 {element_name} 最终失败，已重试 {max_retries} 次", "ERROR")
        return False

    def _safe_click_element(self, element, element_name: str) -> bool:
        """安全点击已找到的元素 - 尝试3种点击方法"""
        try:
            # 等待元素可交互（只检查是否enabled，不检查displayed）
            try:
                WebDriverWait(self.driver, 5).until(
                    lambda _: element.is_enabled()
                )
            except TimeoutException:
                self.log(f"元素 {element_name} 等待超时，但继续尝试点击", "WARNING")

            # 尝试3种点击方法
            click_methods = [
                ("selenium自带click()", lambda: element.click()),
                ("ActionChains鼠标操作", lambda: ActionChains(self.driver).move_to_element(element).click().perform()),
                ("JavaScript操作", lambda: self.driver.execute_script("arguments[0].click();", element))
            ]

            for method_name, click_method in click_methods:
                try:
                    self.log(f"尝试 {method_name}...")
                    click_method()
                    self.log(f"✓ {method_name} 成功点击 {element_name}")

                    # 点击后等待
                    delay = Config.get_random_delay('click')
                    time.sleep(delay)

                    return True

                except Exception as e:
                    self.log(f"{method_name} 失败: {str(e)}", "WARNING")
                    continue

            self.log(f"所有点击方法都失败了: {element_name}", "ERROR")
            return False

        except Exception as e:
            self.log(f"点击 {element_name} 失败: {str(e)}", "ERROR")
            return False

    def _simulate_human_behavior(self):
        """模拟人类浏览行为 - 增强版"""
        try:
            # 随机滚动 - 增加概率控制
            if random.random() < Config.ANTI_SCRAPING['scroll_probability']:
                scroll_count = random.randint(2, 5)
                for _ in range(scroll_count):
                    # 随机选择滚动方向和距离
                    scroll_direction = random.choice([1, -1])
                    scroll_distance = random.randint(200, 800)
                    scroll_y = scroll_direction * scroll_distance

                    self.driver.execute_script(f"window.scrollBy(0, {scroll_y});")
                    time.sleep(random.uniform(0.8, 2.0))

                # 滚动回顶部
                self.driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(random.uniform(1.0, 2.0))

            # 随机鼠标移动 - 增加概率控制
            if random.random() < Config.ANTI_SCRAPING['mouse_move_probability']:
                actions = ActionChains(self.driver)
                move_count = random.randint(2, 4)

                for _ in range(move_count):
                    x_offset = random.randint(-200, 200)
                    y_offset = random.randint(-200, 200)
                    actions.move_by_offset(x_offset, y_offset)
                    time.sleep(random.uniform(0.3, 0.8))

                # 执行鼠标移动
                try:
                    actions.perform()
                except Exception:
                    pass  # 忽略鼠标移动失败

            # 随机页面交互 - 新增
            if random.random() < Config.ANTI_SCRAPING['random_click_probability']:
                try:
                    # 查找一些安全的可点击元素（不会导航离开页面）
                    safe_elements = self.driver.find_elements(By.XPATH, "//div | //span | //p")
                    if safe_elements:
                        element = random.choice(safe_elements[:10])  # 只从前10个元素中选择
                        if element.is_displayed() and element.is_enabled():
                            # 滚动到元素位置
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                            time.sleep(random.uniform(0.5, 1.0))

                            # 模拟鼠标悬停
                            ActionChains(self.driver).move_to_element(element).perform()
                            time.sleep(random.uniform(0.5, 1.5))
                except Exception:
                    pass  # 忽略随机交互失败

            # 随机等待 - 模拟阅读时间
            reading_time = random.uniform(2.0, 5.0)
            time.sleep(reading_time)

        except Exception as e:
            self.log(f"模拟人类行为失败: {str(e)}", "WARNING")

    def _enhanced_anti_scraping_measures(self):
        """增强的反爬虫措施"""
        try:
            # 随机User-Agent轮换
            if random.random() < 0.1:  # 10%概率更换User-Agent
                self._rotate_user_agent()

            # 随机窗口大小调整
            if random.random() < 0.05:  # 5%概率调整窗口大小
                self._random_window_resize()

            # 模拟键盘操作
            if random.random() < 0.15:  # 15%概率模拟键盘操作
                self._simulate_keyboard_activity()

            # 随机页面交互
            if random.random() < 0.2:  # 20%概率进行页面交互
                self._random_page_interaction()

        except Exception as e:
            self.log(f"执行增强反爬措施失败: {str(e)}", "WARNING")

    def _rotate_user_agent(self):
        """轮换User-Agent"""
        try:
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
            new_ua = random.choice(user_agents)
            self.driver.execute_cdp_cmd('Network.setUserAgentOverride', {"userAgent": new_ua})
            self.log(f"已更换User-Agent: {new_ua[:50]}...", "DEBUG")
        except Exception as e:
            self.log(f"更换User-Agent失败: {str(e)}", "WARNING")

    def _random_window_resize(self):
        """随机调整窗口大小"""
        try:
            widths = [1366, 1440, 1536, 1600, 1920]
            heights = [768, 900, 1024, 1080, 1200]
            width = random.choice(widths)
            height = random.choice(heights)
            self.driver.set_window_size(width, height)
            self.log(f"已调整窗口大小: {width}x{height}", "DEBUG")
            time.sleep(random.uniform(0.5, 1.0))
        except Exception as e:
            self.log(f"调整窗口大小失败: {str(e)}", "WARNING")

    def _simulate_keyboard_activity(self):
        """模拟键盘活动"""
        try:
            from selenium.webdriver.common.keys import Keys
            # 模拟按键操作
            keys = [Keys.PAGE_DOWN, Keys.PAGE_UP, Keys.HOME, Keys.END]
            key = random.choice(keys)
            actions = ActionChains(self.driver)
            actions.send_keys(key).perform()
            self.log(f"模拟按键操作", "DEBUG")
            time.sleep(random.uniform(0.3, 0.8))
        except Exception as e:
            self.log(f"模拟键盘活动失败: {str(e)}", "WARNING")

    def _random_page_interaction(self):
        """随机页面交互"""
        try:
            # 随机点击页面空白区域
            body = self.driver.find_element(By.TAG_NAME, "body")
            actions = ActionChains(self.driver)
            actions.move_to_element_with_offset(body, random.randint(100, 500), random.randint(100, 300))
            actions.click().perform()
            self.log("执行随机页面交互", "DEBUG")
            time.sleep(random.uniform(0.2, 0.6))
        except Exception as e:
            self.log(f"随机页面交互失败: {str(e)}", "WARNING")

    def _auto_navigate(self) -> bool:
        """自动导航到房源列表页面"""
        try:
            # 检查是否是阿里资产网站
            if "taobao.com" in self.target_url:
                self.log("检测到淘宝网站，尝试自动导航...")

                # 第一步：点击第一个导航元素
                self.log("第一步：点击第一个导航元素...")
                first_click_success = self.click_first_element()

                if not first_click_success:
                    self.log("第一个导航元素点击失败", "ERROR")
                    return True  # 继续尝试在当前页面搜索

                # 第二步：验证页面跳转并点击住宅用房元素
                self.log("第一步成功，准备第二步：查找住宅用房元素...")

                # 额外的页面状态验证
                try:
                    current_url = self.driver.current_url
                    current_title = self.driver.title
                    self.log(f"执行第二步前的页面状态 - URL: {current_url}, 标题: {current_title}")
                except Exception as e:
                    self.log(f"获取页面状态失败: {str(e)}", "WARNING")

                second_click_success = self.click_second_element()

                if first_click_success and second_click_success:
                    self.log("✓ 自动导航完全成功")
                    return True
                else:
                    self.log(f"自动导航部分失败 - 第一步:{first_click_success}, 第二步:{second_click_success}", "WARNING")
                    return True  # 继续尝试在当前页面搜索
            else:
                self.log("非淘宝网站，跳过自动导航")
                return True

        except Exception as e:
            self.log(f"自动导航异常: {str(e)}", "ERROR")
            return True  # 继续尝试在当前页面搜索

    def scrape_all_houses(self) -> Dict[str, int]:
        """爬取所有房源数据 - 支持住宅和商办两种类型"""
        self.is_running = True
        self.scraped_count = 0
        self.success_count = 0
        self.error_count = 0

        try:
            self.log("=" * 80)
            self.log("开始执行改进的四步自动化爬虫流程")
            self.log("=" * 80)

            # 获取页面数量设置
            residential_pages = getattr(self, 'residential_pages', 10)
            commercial_pages = getattr(self, 'commercial_pages', 10)

            self.log(f"住宅页面数量: {residential_pages}")
            self.log(f"商办页面数量: {commercial_pages}")

            # 检查是否有需要爬取的类型
            if residential_pages == 0 and commercial_pages == 0:
                self.log("住宅和商办页面数量都为0，无需爬取", "WARNING")
                return {'total': 0, 'success': 0, 'error': 0}

            # 初始化浏览器
            self.log("初始化浏览器...")
            if not self.init_driver():
                return {'total': 0, 'success': 0, 'error': 1}

            # 导航到目标页面
            self.log("导航到目标页面...")
            if not self.navigate_to_target():
                return {'total': 0, 'success': 0, 'error': 1}

            # 第一步：点击房产专区入口
            self.log("\n开始执行第一步...")
            if not self.click_first_element():
                self.log("第一步失败，无法继续", "ERROR")
                return {'total': 0, 'success': 0, 'error': 1}

            # 分别处理住宅和商办
            total_results = {'total': 0, 'success': 0, 'error': 0}

            # 处理住宅
            if residential_pages > 0:
                self.log("\n" + "=" * 60)
                self.log("开始爬取住宅房源")
                self.log("=" * 60)
                residential_result = self._scrape_house_type("住宅", residential_pages)
                total_results['total'] += residential_result['total']
                total_results['success'] += residential_result['success']
                total_results['error'] += residential_result['error']
            else:
                self.log("\n住宅页面数量为0，跳过住宅房源爬取")

            # 处理商办
            if commercial_pages > 0:
                self.log("\n" + "=" * 60)
                self.log("开始爬取商办房源")
                self.log("=" * 60)
                commercial_result = self._scrape_house_type("商办", commercial_pages)
                total_results['total'] += commercial_result['total']
                total_results['success'] += commercial_result['success']
                total_results['error'] += commercial_result['error']
            else:
                self.log("\n商办页面数量为0，跳过商办房源爬取")

            # 更新统计数据
            self.scraped_count = total_results['total']
            self.success_count = total_results['success']
            self.error_count = total_results['error']

            self.log("=" * 80)
            self.log(f"所有房源类型爬取完成! 总计: {self.scraped_count}, 成功: {self.success_count}, 失败: {self.error_count}")
            self.log("=" * 80)

            return total_results

        except Exception as e:
            self.log(f"爬取过程异常: {str(e)}", "ERROR")
            return {
                'total': self.scraped_count,
                'success': self.success_count,
                'error': self.error_count + 1
            }
        finally:
            self.is_running = False
            self.cleanup()

    def _scrape_house_type(self, house_type: str, max_pages: int) -> Dict[str, int]:
        """爬取指定类型的房源"""
        try:
            # 设置当前房源类型（固定）
            self.house_type = house_type
            self.log(f"设置房源类型为: {house_type}")

            # 第二步：选择房源分类
            self.log(f"\n开始执行第二步：选择{house_type}分类...")
            if not self.click_second_element():
                self.log(f"第二步失败：无法选择{house_type}分类", "ERROR")
                return {'total': 0, 'success': 0, 'error': 1}

            # 第三步和第四步合并：通过点击房源项批量爬取房源详情数据
            self.log(f"\n开始执行第三、四步：通过点击{house_type}房源项批量爬取详情数据...")
            self.log(f"{house_type}页面数量限制: {max_pages} 页")
            result = self.batch_scrape_house_details_by_clicking(max_pages)

            self.log(f"✓ {house_type}房源爬取完成: 总计: {result['total']}, 成功: {result['success']}, 失败: {result['error']}")

            return result

        except Exception as e:
            self.log(f"爬取{house_type}房源异常: {str(e)}", "ERROR")
            return {'total': 0, 'success': 0, 'error': 1}

    def start_scraping(self):
        """开始爬取"""
        if not self.is_running:
            self.log("开始爬取任务...")
            return self.scrape_all_houses()
        else:
            self.log("爬取任务已在运行中", "WARNING")
            return None

    def stop_scraping(self):
        """停止爬取"""
        if self.is_running:
            self.log("正在停止爬取任务...")
            self.is_running = False
            self.is_paused = False
        else:
            self.log("没有正在运行的爬取任务", "WARNING")

    def pause_scraping(self):
        """暂停爬取"""
        if self.is_running and not self.is_paused:
            self.log("暂停爬取任务")
            self.is_paused = True
        else:
            self.log("无法暂停：任务未运行或已暂停", "WARNING")

    def resume_scraping(self):
        """恢复爬取"""
        if self.is_running and self.is_paused:
            self.log("恢复爬取任务")
            self.is_paused = False
        else:
            self.log("无法恢复：任务未运行或未暂停", "WARNING")

    def cleanup(self):
        """清理资源"""
        try:
            if self.driver:
                # 在关闭浏览器前保存cookies
                try:
                    self.log("保存当前登录状态...")
                    self.save_cookies()
                except Exception as e:
                    self.log(f"保存Cookies失败: {str(e)}", "WARNING")

                self.driver.quit()
                self.driver = None
                self.log("浏览器已关闭")

            if self.api_client:
                self.api_client.close()
                self.log("API客户端已关闭")

        except Exception as e:
            self.log(f"清理资源时出错: {str(e)}", "WARNING")

    def get_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        return {
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'scraped_count': self.scraped_count,
            'success_count': self.success_count,
            'error_count': self.error_count
        }
