/**
 * API 配置文件
 */

// 开发环境和生产环境的API地址配置
const config = {
  // 开发环境
  development: {
    baseUrl: 'http://localhost:8080',
    timeout: 10000
  },
  // 生产环境
  production: {
    baseUrl: 'https://cqjxzc.com.cn',
    timeout: 10000
  }
}

// 当前环境（可以根据实际情况修改）
const currentEnv = 'production'

// 当前配置
const currentConfig = config[currentEnv]

/**
 * API 接口地址
 */
const API = {
  // 用户相关
  USER_LOGIN: '/api/user/login',
  USER_INFO: '/api/user/info',
  USER_UPDATE: '/api/user/update',
  USER_FOLLOW: '/api/user',  // 关注房源 POST /api/user/{userId}/follow
  USER_FAVORITE: '/api/user', // 收藏房源 POST /api/user/{userId}/favorite
  USER_FOLLOWS: '/api/user', // 获取关注列表 GET /api/user/{userId}/follows
  USER_FAVORITES: '/api/user', // 获取收藏列表 GET /api/user/{userId}/favorites
  WECHAT_LOGIN: '/api/wechat/miniapp/login',
  WECHAT_PHONE: '/api/wechat/phone-number',

  // 小程序API
  MINIPROGRAM_CAROUSELS: '/api/miniprogram/carousels',

  // 房源相关
  HOUSE_LIST: '/api/house/page',
  HOUSE_DETAIL: '/api/house',
  HOUSE_SEARCH: '/api/house/search',
  HOUSE_BY_TYPE: '/api/house/house-type',
  HOUSE_BY_SPECIAL: '/api/house/special',
  HOUSE_BY_FEATURED: '/api/house/featured',
  HOUSE_SALE_CREATE: '/api/house-sale/create',
  HOUSE_SALE_USER: '/api/house-sale/user',

  // 楼盘详情相关
  PROPERTY_DETAIL: '/api/property/detail',
  PROPERTY_IMAGES: '/api/property/images', // 获取楼盘图片详情
  PROPERTY_FLOOR_PLAN: '/api/property/floor-plan', // 获取户型图
  CHECK_FAVORITE: '/api/property/favorite/check',
  FAVORITE: '/api/property/favorite',
  UNFAVORITE: '/api/property/unfavorite',

  // 楼盘位置相关
  PROPERTY_LOCATION: '/api/property/location', // 获取楼盘位置信息
  NEARBY_TRANSPORT: '/api/property/nearby/transport', // 获取附近交通站点
  NEARBY_FACILITIES: '/api/property/nearby/facilities', // 获取附近配套设施
  NEARBY_PROPERTIES: '/api/property/nearby/properties', // 获取附近房源推荐
  NEARBY_NEW_PROPERTIES: '/api/property/nearby/new', // 获取附近新盘
  NEARBY_SECOND_HAND: '/api/property/nearby/second-hand', // 获取附近二手房

  // 咨询相关
  CONSULTATION_CREATE: '/api/consultation/create',
  CONSULTATION_USER: '/api/consultation/user',
  
  // 拍卖相关
  AUCTION_LIST: '/api/auction/list',
  AUCTION_DETAIL: '/api/auction/detail',
  AUCTION_STATS: '/api/auction/stats',
  
  // 地图相关
  MAP_HOUSES: '/api/map/houses',
  LOCATION_INFO: '/api/location/info',
  
  // 统计数据
  STATS_TODAY: '/api/stats/today',
  STATS_OVERVIEW: '/api/stats/overview',
  
  // 其他服务
  CALCULATOR: '/api/tools/calculator',
  UPLOAD_IMAGE: '/api/upload/image'
}

/**
 * HTTP 请求封装
 */
const request = (options) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: currentConfig.baseUrl + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      timeout: currentConfig.timeout,
      success: (res) => {
        if (res.statusCode === 200) {
          // 兼容不同的响应格式
          if (res.data.code === 200 || res.data.code === 0) {
            resolve(res.data)
          } else {
            reject(new Error(res.data.message || '请求失败'))
          }
        } else {
          reject(new Error('网络请求失败'))
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

/**
 * GET 请求
 */
const get = (url, data = {}, header = {}) => {
  return request({
    url: url,
    method: 'GET',
    data: data,
    header: header
  })
}

/**
 * POST 请求
 */
const post = (url, data = {}, header = {}) => {
  return request({
    url: url,
    method: 'POST',
    data: data,
    header: header
  })
}

/**
 * PUT 请求
 */
const put = (url, data = {}, header = {}) => {
  return request({
    url: url,
    method: 'PUT',
    data: data,
    header: header
  })
}

/**
 * DELETE 请求
 */
const del = (url, data = {}) => {
  return request({
    url: url,
    method: 'DELETE',
    data: data
  })
}

/**
 * 上传文件
 */
const uploadFile = (filePath, name = 'file') => {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: currentConfig.baseUrl + API.UPLOAD_IMAGE,
      filePath: filePath,
      name: name,
      success: (res) => {
        const data = JSON.parse(res.data)
        if (data.code === 0) {
          resolve(data.data)
        } else {
          reject(new Error(data.message || '上传失败'))
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

module.exports = {
  API,
  request,
  get,
  post,
  put,
  del,
  uploadFile,
  baseUrl: currentConfig.baseUrl
}
