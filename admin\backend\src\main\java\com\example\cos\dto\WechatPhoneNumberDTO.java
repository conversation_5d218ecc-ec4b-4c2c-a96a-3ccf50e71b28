package com.example.cos.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 微信获取手机号响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class WechatPhoneNumberDTO {

    /**
     * 错误码
     */
    @JsonProperty("errcode")
    private Integer errcode;

    /**
     * 错误信息
     */
    @JsonProperty("errmsg")
    private String errmsg;

    /**
     * 手机号信息
     */
    @JsonProperty("phone_info")
    private PhoneInfo phoneInfo;

    /**
     * 手机号信息内部类
     */
    public static class PhoneInfo {
        /**
         * 用户绑定的手机号（国外手机号会有区号）
         */
        @JsonProperty("phoneNumber")
        private String phoneNumber;

        /**
         * 没有区号的手机号
         */
        @JsonProperty("purePhoneNumber")
        private String purePhoneNumber;

        /**
         * 区号
         */
        @JsonProperty("countryCode")
        private String countryCode;

        /**
         * 数据水印
         */
        @JsonProperty("watermark")
        private Watermark watermark;

        public PhoneInfo() {}

        public String getPhoneNumber() {
            return phoneNumber;
        }

        public void setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
        }

        public String getPurePhoneNumber() {
            return purePhoneNumber;
        }

        public void setPurePhoneNumber(String purePhoneNumber) {
            this.purePhoneNumber = purePhoneNumber;
        }

        public String getCountryCode() {
            return countryCode;
        }

        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }

        public Watermark getWatermark() {
            return watermark;
        }

        public void setWatermark(Watermark watermark) {
            this.watermark = watermark;
        }

        @Override
        public String toString() {
            return "PhoneInfo{" +
                    "phoneNumber='" + phoneNumber + '\'' +
                    ", purePhoneNumber='" + purePhoneNumber + '\'' +
                    ", countryCode='" + countryCode + '\'' +
                    ", watermark=" + watermark +
                    '}';
        }
    }

    /**
     * 数据水印内部类
     */
    public static class Watermark {
        @JsonProperty("timestamp")
        private Long timestamp;

        @JsonProperty("appid")
        private String appid;

        public Watermark() {}

        public Long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }

        public String getAppid() {
            return appid;
        }

        public void setAppid(String appid) {
            this.appid = appid;
        }

        @Override
        public String toString() {
            return "Watermark{" +
                    "timestamp=" + timestamp +
                    ", appid='" + appid + '\'' +
                    '}';
        }
    }

    public WechatPhoneNumberDTO() {}

    public Integer getErrcode() {
        return errcode;
    }

    public void setErrcode(Integer errcode) {
        this.errcode = errcode;
    }

    public String getErrmsg() {
        return errmsg;
    }

    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }

    public PhoneInfo getPhoneInfo() {
        return phoneInfo;
    }

    public void setPhoneInfo(PhoneInfo phoneInfo) {
        this.phoneInfo = phoneInfo;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return errcode != null && errcode == 0;
    }

    @Override
    public String toString() {
        return "WechatPhoneNumberDTO{" +
                "errcode=" + errcode +
                ", errmsg='" + errmsg + '\'' +
                ", phoneInfo=" + phoneInfo +
                '}';
    }
}
