/**
 * 权限状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { permissionManager, USER_ROLES, PERMISSIONS } from '@/utils/permission'

export const usePermissionStore = defineStore('permission', () => {
  // 状态
  const currentUser = ref(null)
  const userRole = ref('')
  const userPermissions = ref([])
  const isInitialized = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => {
    return !!currentUser.value && !!userRole.value
  })

  const isSuperAdmin = computed(() => {
    return userRole.value === USER_ROLES.SUPER_ADMIN
  })

  const isAdmin = computed(() => {
    return userRole.value === USER_ROLES.ADMIN || userRole.value === USER_ROLES.SUPER_ADMIN
  })

  const isNormalAdmin = computed(() => {
    return userRole.value === USER_ROLES.NORMAL_ADMIN
  })

  const canCreate = computed(() => {
    return userPermissions.value.includes(PERMISSIONS.CREATE)
  })

  const canUpdate = computed(() => {
    return userPermissions.value.includes(PERMISSIONS.UPDATE)
  })

  const canDelete = computed(() => {
    return userPermissions.value.includes(PERMISSIONS.DELETE)
  })

  const canRead = computed(() => {
    return userPermissions.value.includes(PERMISSIONS.READ)
  })

  // 方法
  const initPermissions = () => {
    permissionManager.init()
    currentUser.value = permissionManager.getCurrentUser()
    userRole.value = permissionManager.getUserRole()
    userPermissions.value = permissionManager.getUserPermissions()
    isInitialized.value = true
  }

  const setUserInfo = (userInfo) => {
    currentUser.value = userInfo
    userRole.value = userInfo.role

    // 重新初始化权限管理器
    permissionManager.init()
    userPermissions.value = permissionManager.getUserPermissions()
    isInitialized.value = true

    // 同步到localStorage
    localStorage.setItem('adminId', userInfo.id)
    localStorage.setItem('username', userInfo.username)
    localStorage.setItem('role', userInfo.role)

    console.log('权限信息已设置:', {
      user: userInfo,
      role: userRole.value,
      permissions: userPermissions.value
    })
  }

  const hasPermission = (permission) => {
    return userPermissions.value.includes(permission)
  }

  const hasAnyPermission = (permissions) => {
    return permissions.some(permission => userPermissions.value.includes(permission))
  }

  const hasAllPermissions = (permissions) => {
    return permissions.every(permission => userPermissions.value.includes(permission))
  }

  const hasDataPermission = (operation) => {
    return hasPermission(operation)
  }

  const hasModulePermission = (module) => {
    return hasPermission(module)
  }

  const clearPermissions = () => {
    currentUser.value = null
    userRole.value = ''
    userPermissions.value = []
    isInitialized.value = false
    permissionManager.clear()
    
    // 清除localStorage
    localStorage.removeItem('token')
    localStorage.removeItem('adminId')
    localStorage.removeItem('username')
    localStorage.removeItem('role')
  }

  const getRoleDisplayName = (role) => {
    return permissionManager.getRoleDisplayName(role || userRole.value)
  }

  // 权限检查方法（用于组件中）
  const checkPermission = (permission) => {
    if (!isInitialized.value) {
      initPermissions()
    }
    return hasPermission(permission)
  }

  const checkDataPermission = (operation) => {
    if (!isInitialized.value) {
      initPermissions()
    }
    return hasDataPermission(operation)
  }

  const checkModulePermission = (module) => {
    if (!isInitialized.value) {
      initPermissions()
    }
    return hasModulePermission(module)
  }

  // 初始化
  if (!isInitialized.value) {
    initPermissions()
  }

  return {
    // 状态
    currentUser,
    userRole,
    userPermissions,
    isInitialized,
    
    // 计算属性
    isLoggedIn,
    isSuperAdmin,
    isAdmin,
    isNormalAdmin,
    canCreate,
    canUpdate,
    canDelete,
    canRead,
    
    // 方法
    initPermissions,
    setUserInfo,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasDataPermission,
    hasModulePermission,
    clearPermissions,
    getRoleDisplayName,
    checkPermission,
    checkDataPermission,
    checkModulePermission
  }
})
