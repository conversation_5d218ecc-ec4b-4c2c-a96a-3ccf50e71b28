package com.example.cos.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.cos.dto.CooperationConsultationCreateDTO;
import com.example.cos.entity.CooperationConsultation;
import com.example.cos.mapper.CooperationConsultationMapper;
import com.example.cos.service.CooperationConsultationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 合作咨询服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class CooperationConsultationServiceImpl extends ServiceImpl<CooperationConsultationMapper, CooperationConsultation> implements CooperationConsultationService {

    private static final Logger logger = LoggerFactory.getLogger(CooperationConsultationServiceImpl.class);

    @Override
    public CooperationConsultation createConsultation(CooperationConsultationCreateDTO createDTO) {
        logger.info("创建合作咨询: {}", createDTO);
        
        CooperationConsultation consultation = new CooperationConsultation();
        BeanUtils.copyProperties(createDTO, consultation);
        
        boolean saved = save(consultation);
        if (!saved) {
            throw new RuntimeException("合作咨询创建失败");
        }
        
        logger.info("合作咨询创建成功: {}", consultation.getId());
        return consultation;
    }

    @Override
    public List<CooperationConsultation> findByUserId(Integer userId) {
        return baseMapper.findByUserId(userId);
    }

    @Override
    public List<CooperationConsultation> findByCity(String city) {
        return baseMapper.findByCity(city);
    }

    @Override
    public List<CooperationConsultation> findByName(String name) {
        return baseMapper.findByName(name);
    }

    @Override
    public boolean updateConsultation(CooperationConsultation consultation) {
        logger.info("更新合作咨询: {}", consultation);
        return updateById(consultation);
    }

    @Override
    public boolean deleteConsultation(Integer id) {
        logger.info("删除合作咨询: {}", id);
        return removeById(id);
    }
}
