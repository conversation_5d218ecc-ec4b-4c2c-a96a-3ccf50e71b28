# -*- coding: utf-8 -*-
"""
时间字段校验测试脚本
用于测试阿里资产爬虫的时间字段处理功能
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api_client import APIClient
from ali_asset_scraper import AliAssetScraper


def test_time_field_validation():
    """测试时间字段校验功能"""
    print("🧪 开始测试时间字段校验功能...")
    
    # 创建API客户端
    api_client = APIClient()
    
    # 创建爬虫实例
    scraper = AliAssetScraper(api_client)
    
    # 测试数据1: 时间字段为None
    test_data_1 = {
        'auctionStatus': 2,
        'auctionTimes': 2,
        'buildingArea': 99.57,
        'communityName': '保利大国璟天麓',
        'constructionYear': 2022,
        'decoration': 0,
        'deposit': 44609,
        'endTime': None,  # ❌ 时间为空
        'evaluationPrice': 796600,
        'floor': '10/11',
        'houseCategory': 0,
        'houseType': '3室2厅1厨2卫',
        'isSpecial': False,
        'isSelected': False,
        'latitude': None,
        'longitude': None,
        'priceIncrement': 1000,
        'propertyType': 1,
        'stairsType': 1,
        'startTime': None,  # ❌ 时间为空
        'startingPrice': 446096,
        'tags': '清水房',
        'title': '二拍 重庆市巴南区尚文大道666号5幢二单元10-4房屋',
        'imageUrls': 'https://cos.cqjxzc.com.cn/2025/08/01/test.jpg',
        'originalUrl': 'https://sf-item.taobao.com/sf_item/959093985181.htm'
    }
    
    print("📋 测试数据1 - 时间字段为None:")
    print(f"原始startTime: {test_data_1['startTime']}")
    print(f"原始endTime: {test_data_1['endTime']}")
    
    # 处理数据
    processed_data_1 = scraper.process_house_data(test_data_1)
    print(f"处理后startTime: {processed_data_1['startTime']}")
    print(f"处理后endTime: {processed_data_1['endTime']}")
    
    # 验证时间格式
    try:
        start_time = datetime.strptime(processed_data_1['startTime'], "%Y-%m-%d %H:%M:%S")
        end_time = datetime.strptime(processed_data_1['endTime'], "%Y-%m-%d %H:%M:%S")
        print("✅ 时间格式校验通过")
        
        # 验证结束时间是否在开始时间之后
        if end_time > start_time:
            print("✅ 结束时间在开始时间之后")
        else:
            print("❌ 结束时间不在开始时间之后")
            
    except ValueError as e:
        print(f"❌ 时间格式校验失败: {str(e)}")
    
    # 测试数据2: 只有startTime为None
    test_data_2 = {
        'auctionStatus': 1,
        'auctionTimes': 1,
        'buildingArea': 50.18,
        'communityName': '测试小区',
        'constructionYear': 2020,
        'decoration': 1,
        'deposit': 20000,
        'endTime': '2025-08-10 15:00:00',  # ✅ 有效时间
        'evaluationPrice': 300000,
        'floor': '5/10',
        'houseCategory': 0,
        'houseType': '2室1厅',
        'isSpecial': False,
        'isSelected': False,
        'latitude': None,
        'longitude': None,
        'priceIncrement': 1000,
        'propertyType': 1,
        'stairsType': 1,
        'startTime': None,  # ❌ 时间为空
        'startingPrice': 200000,
        'tags': '学区房',
        'title': '一拍 测试房源',
        'imageUrls': 'https://cos.cqjxzc.com.cn/2025/08/01/test2.jpg',
        'originalUrl': 'https://sf-item.taobao.com/sf_item/123456789.htm'
    }
    
    print("\n📋 测试数据2 - 只有startTime为None:")
    print(f"原始startTime: {test_data_2['startTime']}")
    print(f"原始endTime: {test_data_2['endTime']}")
    
    # 处理数据
    processed_data_2 = scraper.process_house_data(test_data_2)
    print(f"处理后startTime: {processed_data_2['startTime']}")
    print(f"处理后endTime: {processed_data_2['endTime']}")
    
    # 验证时间
    try:
        start_time = datetime.strptime(processed_data_2['startTime'], "%Y-%m-%d %H:%M:%S")
        end_time = datetime.strptime(processed_data_2['endTime'], "%Y-%m-%d %H:%M:%S")
        print("✅ 时间格式校验通过")
        
        if end_time > start_time:
            print("✅ 结束时间在开始时间之后")
        else:
            print("❌ 结束时间不在开始时间之后")
            
    except ValueError as e:
        print(f"❌ 时间格式校验失败: {str(e)}")
    
    # 测试数据3: 只有endTime为None
    test_data_3 = {
        'auctionStatus': 3,
        'auctionTimes': 3,
        'buildingArea': 75.0,
        'communityName': '测试小区3',
        'constructionYear': 2018,
        'decoration': 2,
        'deposit': 35000,
        'endTime': None,  # ❌ 时间为空
        'evaluationPrice': 450000,
        'floor': '8/15',
        'houseCategory': 0,
        'houseType': '3室2厅',
        'isSpecial': False,
        'isSelected': False,
        'latitude': None,
        'longitude': None,
        'priceIncrement': 2000,
        'propertyType': 1,
        'stairsType': 1,
        'startTime': '2025-08-05 10:00:00',  # ✅ 有效时间
        'startingPrice': 315000,
        'tags': '精装修',
        'title': '变卖 测试房源3',
        'imageUrls': 'https://cos.cqjxzc.com.cn/2025/08/01/test3.jpg',
        'originalUrl': 'https://sf-item.taobao.com/sf_item/987654321.htm'
    }
    
    print("\n📋 测试数据3 - 只有endTime为None:")
    print(f"原始startTime: {test_data_3['startTime']}")
    print(f"原始endTime: {test_data_3['endTime']}")
    
    # 处理数据
    processed_data_3 = scraper.process_house_data(test_data_3)
    print(f"处理后startTime: {processed_data_3['startTime']}")
    print(f"处理后endTime: {processed_data_3['endTime']}")
    
    # 验证时间
    try:
        start_time = datetime.strptime(processed_data_3['startTime'], "%Y-%m-%d %H:%M:%S")
        end_time = datetime.strptime(processed_data_3['endTime'], "%Y-%m-%d %H:%M:%S")
        print("✅ 时间格式校验通过")
        
        # 验证结束时间是否为开始时间后7天
        expected_end_time = start_time + timedelta(days=7)
        if abs((end_time - expected_end_time).total_seconds()) < 60:  # 允许1分钟误差
            print("✅ 结束时间设置为开始时间后7天")
        else:
            print(f"⚠️ 结束时间不是开始时间后7天，差异: {end_time - expected_end_time}")
            
    except ValueError as e:
        print(f"❌ 时间格式校验失败: {str(e)}")
    
    print("\n🎉 时间字段校验测试完成！")


def test_time_format_validation():
    """测试时间格式校验"""
    print("\n🧪 测试时间格式校验...")
    
    # 测试各种时间格式
    test_times = [
        "2025-08-01 10:00:00",  # ✅ 正确格式
        "2025-8-1 10:00:00",    # ❌ 月日不补零
        "2025/08/01 10:00:00",  # ❌ 使用斜杠
        "2025-08-01T10:00:00",  # ❌ ISO格式
        "2025-08-01 10:00",     # ❌ 缺少秒
        "2025-08-01",           # ❌ 只有日期
        None,                   # ❌ 空值
        "",                     # ❌ 空字符串
    ]
    
    for i, test_time in enumerate(test_times, 1):
        print(f"\n测试时间 {i}: {test_time}")
        
        try:
            if test_time:
                parsed_time = datetime.strptime(test_time, "%Y-%m-%d %H:%M:%S")
                print(f"✅ 格式正确: {parsed_time}")
            else:
                print("❌ 时间为空")
        except ValueError as e:
            print(f"❌ 格式错误: {str(e)}")
    
    print("\n✅ 时间格式校验测试完成")


def main():
    """主测试函数"""
    print("🚀 开始时间字段综合测试...")
    print("=" * 60)
    
    # 测试1: 时间字段校验功能
    test_time_field_validation()
    
    # 测试2: 时间格式校验
    test_time_format_validation()
    
    print("\n" + "=" * 60)
    print("🎉 时间字段综合测试完成！")
    print("\n📋 测试结果说明:")
    print("- startTime 和 endTime 字段不能为 None")
    print("- 时间格式必须为 YYYY-MM-DD HH:MM:SS")
    print("- 如果时间为空，会自动设置默认值")
    print("- endTime 默认为 startTime 后7天")


if __name__ == "__main__":
    main()
