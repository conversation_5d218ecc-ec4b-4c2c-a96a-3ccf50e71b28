# -*- coding: utf-8 -*-
"""
配置文件 - 包含所有应用程序设置
"""
import random
from fake_useragent import UserAgent

class Config:
    """应用程序配置类"""
    
    # API配置
    API_BASE_URL = "https://cqjxzc.com.cn"
    API_ENDPOINT = "/api/house/create"
    API_TIMEOUT = 30

    # AI API配置（K2模型）
    K2_API_KEY = "sk-hxdgkb5UQxLGquJemWM2X6jemRfBjhQ889XnT4J1Sh9gigGo"  # 请替换为您的K2 API密钥
    K2_API_URL = "https://api.moonshot.cn"  # 请替换为K2的实际API地址
    K2_MODEL = "kimi-k2-0711-preview"  # 请替换为K2的实际模型名称
    
    # 目标网站配置
    TARGET_URL = "https://z.taobao.com/"

    # 平台配置
    PLATFORMS = {
        'ali': {
            'name': '阿里拍卖',
            'url': 'https://z.taobao.com/',
            'scraper_class': 'HouseScraper'
        },
        'jd': {
            'name': '京东拍卖',
            'url': 'https://pmsearch.jd.com/',
            'scraper_class': 'JDScraper'
        },
        'zhongpai': {
            'name': '中拍平台',
            'url': 'https://www.caa123.org.cn/',
            'scraper_class': 'ZhongPaiScraper'
        },
        'gpai': {
            'name': '公拍网',
            'url': 'https://m.gpai.net/',
            'scraper_class': 'GPaiScraper'
        },
        'bjcq': {
            'name': '北京产权交易所',
            'url': 'https://www.chex.com.cn/',
            'scraper_class': 'BJCQScraper'
        },
        'ronge': {
            'name': '融e购',
            'url': 'https://trade.icbc.com.cn/',
            'scraper_class': 'RongEScraper'
        },
        'rmfy': {
            'name': '人民法院诉讼资产网',
            'url': 'https://www.rmfysszc.gov.cn/',
            'scraper_class': 'RMFYScraper'
        }
    }
    
    # XPath选择器
    XPATH_SELECTORS = {
        # 第一次点击：房产专区入口
        'first_click': '//*[@id="guid-2860107850"]/div/div[1]/div/ul/li[1]/div/a[1]/span',

        # 第二次点击：住宅用房
        'second_click_residential': '//*[@id="guid-5143927030"]/div/div[2]/a[2]/span',
        'second_click_residential_alternative': '//*[@id="guid-5143927030"]/div/div[2]/a[2]',

        # 第二次点击：商办
        'second_click_commercial': '//*[@id="guid-5143927030"]/div/div[2]/a[3]/span',
        'second_click_commercial_alternative': '//*[@id="guid-5143927030"]/div/div[2]/a[3]',

        # 房源相关
        'house_container': '//*[@id="guid-9018433170"]/div/div',
        'house_links': '//*[@id="guid-9018433170"]/div/div/div[1]/a',
        'auction_type': '//*[@id="page"]/div[4]/div/div/h1/span',
        'title': '//*[@id="page"]/div[4]/div/div/h1/text()',
        'end_time_container': '//*[@id="sf-countdown"]/span[2]',
        'end_time_vars': './/var',
        'end_time_ems': './/em'
    }

    # 京东拍卖XPath选择器
    JD_XPATH_SELECTORS = {
        # 司法拍卖页面导航元素
        'sifa_navigation': '//*[@id="root"]/div/div[1]/div/div[1]/div[1]/dl[1]/dd/a[2]',

        # 房源列表（更新后的页面结构）
        'house_items': '//*[@id="root"]/div/div/div[4]/ul/li',
        'house_link': './/a',

        # 房源详情页
        'title_container': '//*[@id="pageContainer"]/div[2]/div[1]/div[2]/div[1]',  # 标题容器，需要遍历内部span元素
        'auction_type': '//*[@id="pageContainer"]/div[2]/div[1]/div[2]/div[3]/div/div[1]/div',
        'end_time': '//*[@id="pageContainer"]/div[2]/div[1]/div[2]/div[3]/div[1]/div[2]/div[1]/div[1]/div',
        'starting_price': '//*[@id="right-content"]/div/span[2]',
        'price_increment': '//*[@id="right-content"]/div/span[2]',
        'bidding_cycle': '//*[@id="right-content"]/em',

        # 房源图片
        'house_images_base': '//*[@id="pageContainer"]/div[2]/div[1]/div[1]/div/div[3]/div[2]/div[{}]/img',

        # 竞买公告XPath列表（请在这里添加您提供的多种XPath）
        'announcement_xpaths': [
            '//*[@id="pmMainFloor"]/ul/li[1]/div[2]/div',
            '//*[@id="pmMainFloor"]/ul/li[1]/div',
            '//*[@id="pmMainFloor"]/ul/li[1]'
        ],

        # 验证关键词（用于验证XPath是否正确匹配）
        'announcement_validation_keywords': ["标的", "拍品", "竞买公告"],

        # 文档链接
        'attachment': '//*[@id="openAttachmentTag"]'
    }

    # 中拍平台XPath选择器
    ZHONGPAI_XPATH_SELECTORS = {
        # 分类选择
        'house_category': '//*[@id="house-category"]',  # 房产分类
        'residential_category': '//*[@id="residential"]',  # 住宅分类
        'commercial_category': '//*[@id="commercial"]',  # 商业分类

        # 房源列表
        'house_items': '//*[@class="auction-list"]/div[@class="auction-item"]',
        'house_link': './/a[@class="auction-link"]',

        # 房源详情页
        'title': '//*[@class="auction-title"]',
        'auction_type': '//*[@class="auction-status"]',
        'end_time': '//*[@class="end-time"]',
        'starting_price': '//*[@class="starting-price"]',
        'current_price': '//*[@class="current-price"]',
        'price_increment': '//*[@class="price-increment"]',
        'bidding_cycle': '//*[@class="bidding-cycle"]',

        # 房源图片
        'house_images': '//*[@class="house-images"]//img',

        # 公告和文档
        'announcement': '//*[@class="auction-announcement"]',
        'evaluation_report': '//*[@class="evaluation-report"]//a',
        'execution_order': '//*[@class="execution-order"]//a',
        'property_report': '//*[@class="property-report"]//a',

        # 分页
        'next_page': '//*[@class="pagination"]//a[@class="next"]',
        'page_numbers': '//*[@class="pagination"]//a[@class="page-num"]'
    }
    
    # 反爬措施配置 - 增强版
    ANTI_SCRAPING = {
        # 随机延迟范围（秒）- 增加延迟时间
        'min_delay': 3,
        'max_delay': 8,
        'page_load_delay': (5, 12),
        'click_delay': (2, 5),
        'tab_switch_delay': (1, 3),
        'data_extraction_delay': (3, 6),

        # 重试配置
        'max_retries': 3,
        'retry_delay': (8, 15),

        # 浏览器配置
        'window_size': [(1366, 768), (1920, 1080), (1440, 900), (1536, 864), (1600, 900), (1280, 720)],
        'headless': False,  # 设为True可无头运行

        # 请求头配置
        'accept_language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'accept_encoding': 'gzip, deflate, br',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',

        # 人类行为模拟
        'mouse_move_probability': 0.3,  # 30%概率进行鼠标移动
        'scroll_probability': 0.4,      # 40%概率进行页面滚动
        'random_click_probability': 0.1, # 10%概率进行随机点击

        # 请求频率控制
        'requests_per_minute': 10,       # 每分钟最多请求数
        'burst_delay': (30, 60),         # 突发请求后的延迟
    }
    
    # WebDriver配置
    WEBDRIVER_CONFIG = {
        'implicit_wait': 10,
        'page_load_timeout': 30,
        'script_timeout': 30,
        'explicit_wait': 20,
    }
    
    # GUI配置
    GUI_CONFIG = {
        'window_title': '房源拍卖爬虫 - 支持阿里拍卖、京东拍卖、中拍平台',
        'window_size': '1000x700',
        'log_max_lines': 1000,
    }
    
    @staticmethod
    def get_random_user_agent():
        """获取随机User-Agent（桌面版）- 增强反爬虫"""
        # 扩展的桌面版User-Agent列表，包含更多浏览器和版本
        desktop_user_agents = [
            # Chrome 各版本
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',

            # Firefox 各版本
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/121.0',

            # Edge 各版本
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',

            # Safari 各版本
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15'
        ]
        import random
        return random.choice(desktop_user_agents)
    
    @staticmethod
    def get_random_delay(delay_type='default'):
        """获取随机延迟时间 - 增强版"""
        if delay_type == 'page_load':
            min_delay, max_delay = Config.ANTI_SCRAPING['page_load_delay']
        elif delay_type == 'click':
            min_delay, max_delay = Config.ANTI_SCRAPING['click_delay']
        elif delay_type == 'retry':
            min_delay, max_delay = Config.ANTI_SCRAPING['retry_delay']
        elif delay_type == 'tab_switch':
            min_delay, max_delay = Config.ANTI_SCRAPING['tab_switch_delay']
        elif delay_type == 'data_extraction':
            min_delay, max_delay = Config.ANTI_SCRAPING['data_extraction_delay']
        elif delay_type == 'burst':
            min_delay, max_delay = Config.ANTI_SCRAPING['burst_delay']
        else:
            min_delay = Config.ANTI_SCRAPING['min_delay']
            max_delay = Config.ANTI_SCRAPING['max_delay']

        return random.uniform(min_delay, max_delay)
    
    @staticmethod
    def get_random_window_size():
        """获取随机窗口大小"""
        return random.choice(Config.ANTI_SCRAPING['window_size'])
    
    @staticmethod
    def get_chrome_options():
        """获取Chrome浏览器选项"""
        from selenium.webdriver.chrome.options import Options
        import os

        options = Options()

        # 用户数据目录 - 保留登录态的关键
        user_data_dir = os.path.join(os.getcwd(), "chrome_user_data")
        if not os.path.exists(user_data_dir):
            os.makedirs(user_data_dir)

        options.add_argument('--user-data-dir={}'.format(user_data_dir))
        options.add_argument('--profile-directory=Default')  # 使用默认配置文件

        # 反爬措施 - 增强版
        user_agent = Config.get_random_user_agent()
        options.add_argument('--user-agent={}'.format(user_agent))

        # 核心反检测参数
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)

        # 额外的反检测参数
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-sync')
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-logging')
        options.add_argument('--disable-plugins-discovery')
        options.add_argument('--disable-preconnect')

        # 隐藏自动化特征
        options.add_experimental_option("prefs", {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1
        })

        # 强制桌面版
        options.add_argument('--disable-mobile-emulation')
        options.add_argument('--force-device-scale-factor=1')

        # 网络和连接优化
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        # 注意：不要禁用扩展，因为可能影响登录状态保存
        # options.add_argument('--disable-extensions')
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-ssl-errors')
        options.add_argument('--ignore-certificate-errors-spki-list')
        options.add_argument('--disable-features=VizDisplayCompositor')

        # 解决网络连接问题（移除可能冲突的端口设置）
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-ipc-flooding-protection')

        # 保留登录态相关设置
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--enable-features=NetworkService,NetworkServiceLogging')
        options.add_argument('--disable-background-networking')

        # 窗口大小 - 设置为全屏
        options.add_argument('--start-maximized')  # 启动时最大化窗口
        options.add_argument('--window-size=1920,1080')  # 设置较大的窗口尺寸作为备选

        # 无头模式（可选）- 注意：无头模式可能影响登录状态保存
        if Config.ANTI_SCRAPING['headless']:
            options.add_argument('--headless')

        return options
