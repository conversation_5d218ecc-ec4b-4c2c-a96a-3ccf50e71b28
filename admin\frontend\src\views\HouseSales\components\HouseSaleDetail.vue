<template>
  <div class="house-sale-detail">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="登记ID">
        {{ houseSaleData.id }}
      </el-descriptions-item>
      <el-descriptions-item label="用户ID">
        {{ houseSaleData.userId }}
      </el-descriptions-item>
      <el-descriptions-item label="城市">
        {{ houseSaleData.city }}
      </el-descriptions-item>
      <el-descriptions-item label="小区名称">
        {{ houseSaleData.communityName }}
      </el-descriptions-item>
      <el-descriptions-item label="房屋类型">
        {{ houseSaleData.houseType }}
      </el-descriptions-item>
      <el-descriptions-item label="房屋面积">
        {{ houseSaleData.housingArea }}㎡
      </el-descriptions-item>
      <el-descriptions-item label="期望价格">
        <span class="price">¥{{ formatPrice(houseSaleData.expectedPrice) }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="联系人">
        {{ houseSaleData.contactPerson }}
      </el-descriptions-item>
      <el-descriptions-item label="性别">
        {{ houseSaleData.gender }}
      </el-descriptions-item>
      <el-descriptions-item label="联系方式">
        {{ houseSaleData.contactInfo }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ houseSaleData.createTime }}
      </el-descriptions-item>
      <el-descriptions-item label="备注" :span="2">
        {{ houseSaleData.remarks || '无' }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  houseSaleData: {
    type: Object,
    default: () => ({})
  }
})

// 格式化价格
const formatPrice = (price) => {
  return price ? Number(price).toLocaleString() : '0'
}
</script>

<style lang="scss" scoped>
.house-sale-detail {
  .price {
    color: #e6a23c;
    font-weight: 600;
    font-size: 16px;
  }
}
</style>
