package com.example.cos.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 合作咨询创建DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "合作咨询创建请求")
public class CooperationConsultationCreateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", example = "1", required = true)
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    @ApiModelProperty(value = "城市", example = "北京")
    private String city;

    @ApiModelProperty(value = "姓名", example = "张三")
    private String name;

    @ApiModelProperty(value = "性别", example = "男")
    private String gender;

    @ApiModelProperty(value = "联系方式", example = "13800138000")
    private String contactInfo;

    @ApiModelProperty(value = "咨询内容", example = "希望了解房产投资相关信息")
    private String consultationContent;

    @ApiModelProperty(value = "备注", example = "客户比较关注学区房")
    private String remarks;

    public CooperationConsultationCreateDTO() {}

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getContactInfo() {
        return contactInfo;
    }

    public void setContactInfo(String contactInfo) {
        this.contactInfo = contactInfo;
    }

    public String getConsultationContent() {
        return consultationContent;
    }

    public void setConsultationContent(String consultationContent) {
        this.consultationContent = consultationContent;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Override
    public String toString() {
        return "CooperationConsultationCreateDTO{" +
                "userId=" + userId +
                ", city='" + city + '\'' +
                ", name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                ", contactInfo='" + contactInfo + '\'' +
                ", consultationContent='" + consultationContent + '\'' +
                ", remarks='" + remarks + '\'' +
                '}';
    }
}
