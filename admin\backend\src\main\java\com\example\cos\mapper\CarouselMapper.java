package com.example.cos.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.cos.entity.Carousel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 轮播图Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface CarouselMapper extends BaseMapper<Carousel> {

    /**
     * 获取启用的轮播图列表（按排序序号升序）
     * @return 启用的轮播图列表
     */
    @Select("SELECT * FROM carousel WHERE is_enabled = 1 ORDER BY sort_order ASC, id ASC")
    List<Carousel> selectEnabledCarousels();

    /**
     * 获取所有轮播图列表（按排序序号升序）
     * @return 所有轮播图列表
     */
    @Select("SELECT * FROM carousel ORDER BY sort_order ASC, id ASC")
    List<Carousel> selectAllCarouselsOrderBySort();
}
