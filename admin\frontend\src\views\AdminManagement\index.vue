<template>
  <div class="app-container">
    <div class="page-header">
      <h2>管理员管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增管理员
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :model="searchForm" inline>
        <el-form-item label="账号状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="searchForm.role" placeholder="请选择角色" clearable>
            <el-option label="全部" value="" />
            <el-option label="超级管理员" value="super_admin" />
            <el-option label="管理员" value="admin" />
            <el-option label="普通管理员" value="normal_admin" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadAdmins">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button
          v-if="permissionStore.isSuperAdmin"
          type="danger"
          @click="handleBatchDelete"
          :disabled="!multipleSelection.length"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-button @click="loadAdmins">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 管理员列表 -->
    <el-table
      :data="admins"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      row-key="id"
      class="admin-table"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column prop="id" label="ID" width="80" />
      
      <el-table-column prop="username" label="用户名" width="auto" />

      <el-table-column prop="role" label="角色" width="120">
        <template #default="scope">
          <el-tag :type="getRoleTagType(scope.row.role)">
            {{ getRoleDisplayName(scope.row.role) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="createdTime" label="创建时间" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.createdTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" width="350">
        <template #default="scope">
          <div class="action-buttons">
            <div class="button-row">
              <el-button type="primary" size="small" @click="handleView(scope.row)">
                查看
              </el-button>
              <el-button type="success" size="small" @click="handleUpdate(scope.row)">
                编辑
              </el-button>
              <el-button
                :type="scope.row.status === 1 ? 'warning' : 'success'"
                size="small"
                @click="handleToggleStatus(scope.row)"
                :disabled="scope.row.role === 'super_admin' && scope.row.status === 1"
              >
                {{ scope.row.status === 1 ? '禁用' : '启用' }}
              </el-button>
            </div>
            <div class="button-row">
              <el-button type="info" size="small" @click="handleResetPassword(scope.row)">
                重置密码
              </el-button>
              <el-button
                v-if="permissionStore.isSuperAdmin"
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
                :disabled="scope.row.role === 'super_admin'"
              >
                删除
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 管理员表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="adminForm"
        :rules="adminRules"
        ref="adminFormRef"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="adminForm.username"
            placeholder="请输入用户名"
            :disabled="dialogMode === 'view' || dialogMode === 'edit'"
          />
          <div class="form-tip">用户名只能包含字母、数字和下划线，3-20个字符</div>
        </el-form-item>

        <el-form-item label="密码" prop="password" v-if="dialogMode === 'add'">
          <el-input
            v-model="adminForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
            :disabled="dialogMode === 'view'"
          />
          <div class="form-tip">密码长度6-20个字符</div>
        </el-form-item>

        <el-form-item label="角色" prop="role">
          <el-select v-model="adminForm.role" placeholder="请选择角色" :disabled="dialogMode === 'view'">
            <el-option label="管理员" value="admin" />
            <el-option label="普通管理员" value="normal_admin" />
          </el-select>
          <div class="form-tip">超级管理员角色不能通过此页面创建</div>
        </el-form-item>

        <el-form-item label="账号状态" prop="status">
          <el-radio-group v-model="adminForm.status" :disabled="dialogMode === 'view'">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button 
            v-if="dialogMode !== 'view'" 
            type="primary" 
            @click="handleSubmit"
            :loading="submitting"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      title="重置密码"
      v-model="resetPasswordVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="resetPasswordForm"
        :rules="resetPasswordRules"
        ref="resetPasswordFormRef"
        label-width="100px"
      >
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="resetPasswordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="resetPasswordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetPasswordVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleResetPasswordSubmit"
            :loading="resettingPassword"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { usePermissionStore } from '@/stores/permission'
import {
  getAdmins,
  createAdmin,
  updateAdmin,
  deleteAdmin,
  batchDeleteAdmins,
  toggleAdminStatus,
  resetAdminPassword,
  checkUsername
} from '@/api/admin-management'

// 权限store
const permissionStore = usePermissionStore()

// 响应式数据
const loading = ref(false)
const admins = ref([])
const multipleSelection = ref([])

// 搜索表单
const searchForm = reactive({
  status: '',
  role: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogMode = ref('add') // add, edit, view
const submitting = ref(false)

// 重置密码对话框
const resetPasswordVisible = ref(false)
const resettingPassword = ref(false)
const currentResetAdmin = ref(null)

// 管理员表单
const adminForm = reactive({
  id: null,
  username: '',
  password: '',
  role: 'normal_admin',
  status: 1
})

const adminFormRef = ref()

// 重置密码表单
const resetPasswordForm = reactive({
  newPassword: '',
  confirmPassword: ''
})

const resetPasswordFormRef = ref()

// 表单验证规则
const adminRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在6到20个字符', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择账号状态', trigger: 'change' }
  ]
}

const resetPasswordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在6到20个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value !== resetPasswordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// 方法
const loadAdmins = async () => {
  loading.value = true
  try {
    const response = await getAdmins()
    if (response.code === 200) {
      let data = response.data || []
      
      // 根据搜索条件过滤
      if (searchForm.status !== '') {
        data = data.filter(item => item.status === searchForm.status)
      }
      if (searchForm.role !== '') {
        data = data.filter(item => item.role === searchForm.role)
      }
      
      admins.value = data
    } else {
      ElMessage.error(response.message || '获取管理员列表失败')
    }
  } catch (error) {
    console.error('获取管理员列表失败:', error)
    ElMessage.error('获取管理员列表失败')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  searchForm.status = ''
  searchForm.role = ''
  loadAdmins()
}

const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

const handleAdd = () => {
  dialogTitle.value = '新增管理员'
  dialogMode.value = 'add'
  resetAdminForm()
  dialogVisible.value = true
}

const handleView = (row) => {
  dialogTitle.value = '查看管理员'
  dialogMode.value = 'view'
  fillAdminForm(row)
  dialogVisible.value = true
}

const handleUpdate = (row) => {
  dialogTitle.value = '编辑管理员'
  dialogMode.value = 'edit'
  fillAdminForm(row)
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  if (row.role === 'super_admin') {
    ElMessage.warning('不能删除超级管理员')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除管理员"${row.username}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteAdmin(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      loadAdmins()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除管理员失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleBatchDelete = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请选择要删除的管理员')
    return
  }

  // 检查是否包含超级管理员
  const hasSuperAdmin = multipleSelection.value.some(item => item.role === 'super_admin')
  if (hasSuperAdmin) {
    ElMessage.warning('选中的管理员中包含超级管理员，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${multipleSelection.value.length} 个管理员吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = multipleSelection.value.map(item => item.id)
    const response = await batchDeleteAdmins(ids)
    
    if (response.code === 200) {
      ElMessage.success('批量删除成功')
      loadAdmins()
    } else {
      ElMessage.error(response.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除管理员失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const handleToggleStatus = async (row) => {
  if (row.role === 'super_admin' && row.status === 1) {
    ElMessage.warning('不能禁用超级管理员')
    return
  }

  try {
    const action = row.status === 1 ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}管理员"${row.username}"吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await toggleAdminStatus(row.id)
    if (response.code === 200) {
      ElMessage.success(`${action}成功`)
      loadAdmins()
    } else {
      ElMessage.error(response.message || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const handleResetPassword = (row) => {
  currentResetAdmin.value = row
  resetPasswordForm.newPassword = ''
  resetPasswordForm.confirmPassword = ''
  resetPasswordVisible.value = true
}

const handleResetPasswordSubmit = async () => {
  if (!resetPasswordFormRef.value) return

  try {
    await resetPasswordFormRef.value.validate()
  } catch (error) {
    return
  }

  resettingPassword.value = true
  try {
    const response = await resetAdminPassword(currentResetAdmin.value.id, resetPasswordForm.newPassword)
    if (response.code === 200) {
      ElMessage.success('密码重置成功')
      resetPasswordVisible.value = false
    } else {
      ElMessage.error(response.message || '密码重置失败')
    }
  } catch (error) {
    console.error('重置密码失败:', error)
    ElMessage.error('密码重置失败')
  } finally {
    resettingPassword.value = false
  }
}

const handleSubmit = async () => {
  if (!adminFormRef.value) return

  try {
    await adminFormRef.value.validate()
  } catch (error) {
    return
  }

  // 检查用户名是否已存在
  if (dialogMode.value === 'add') {
    try {
      const checkResponse = await checkUsername(adminForm.username)
      if (checkResponse.code === 200 && checkResponse.data === true) {
        ElMessage.error('用户名已存在')
        return
      }
    } catch (error) {
      console.error('检查用户名失败:', error)
    }
  }

  submitting.value = true
  try {
    let response
    if (dialogMode.value === 'add') {
      response = await createAdmin(adminForm)
    } else {
      response = await updateAdmin(adminForm)
    }

    if (response.code === 200) {
      ElMessage.success(dialogMode.value === 'add' ? '创建成功' : '更新成功')
      dialogVisible.value = false
      loadAdmins()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

const resetAdminForm = () => {
  Object.assign(adminForm, {
    id: null,
    username: '',
    password: '',
    role: 'normal_admin',
    status: 1
  })
  
  if (adminFormRef.value) {
    adminFormRef.value.clearValidate()
  }
}

const fillAdminForm = (row) => {
  Object.assign(adminForm, {
    id: row.id,
    username: row.username,
    password: '', // 不显示密码
    role: row.role,
    status: row.status
  })
}

const getRoleDisplayName = (role) => {
  const roleMap = {
    'super_admin': '超级管理员',
    'admin': '管理员',
    'normal_admin': '普通管理员'
  }
  return roleMap[role] || role
}

const getRoleTagType = (role) => {
  const typeMap = {
    'super_admin': 'danger',
    'admin': 'warning',
    'normal_admin': 'info'
  }
  return typeMap[role] || 'info'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  // 调试权限信息
  console.log('管理员管理页面权限调试:', {
    localStorage_role: localStorage.getItem('role'),
    localStorage_username: localStorage.getItem('username'),
    localStorage_adminId: localStorage.getItem('adminId'),
    localStorage_token: localStorage.getItem('token') ? '已设置' : '未设置'
  })

  loadAdmins()
})
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    color: #303133;
  }
}

.search-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.admin-table {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.button-row {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.button-row .el-button {
  margin: 0;
}

.dialog-footer {
  text-align: right;
}
</style>
