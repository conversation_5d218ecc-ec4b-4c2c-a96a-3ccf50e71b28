package com.example.cos.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.cos.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户数据访问层
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据手机号查询用户
     */
    @Select("SELECT * FROM user_table WHERE phone_number = #{phoneNumber}")
    User findByPhoneNumber(@Param("phoneNumber") String phoneNumber);

    /**
     * 根据微信OpenID查询用户
     */
    @Select("SELECT * FROM user_table WHERE wechat_openid = #{wechatOpenid}")
    User findByWechatOpenid(@Param("wechatOpenid") String wechatOpenid);
}
