/* pages/profile/profile.wxss */
.container {
  width: 750rpx;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.nav-bar {
  width: 750rpx;
  height: 88rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid #f0f0f0;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.nav-icon {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666666;
}

/* 用户信息区域 */
.user-section {
  width: 750rpx;
  padding: 0rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  margin-top: -200rpx;
}


.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  background-color: #f0f0f0;
}

.user-details {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.user-name {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 10rpx;
}

.bind-phone {
  font-size: 28rpx;
  color: #f50e0e;
  font-weight: 400;
  background-color: rgba(255, 68, 68, 0.1);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0rpx 20rpx;
}

/* 绑定手机号按钮样式 */
.phone-bind-button {
  background: none;
  border: none;
  margin: 0;
  font-size: 28rpx;
  color: #f50e0e;
  font-weight: 400;
  line-height: normal;
}

.phone-bind-button::after {
  border: none;
}

.qr-code {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.qr-icon {
  width: 44rpx;
  height: 44rpx;
}

/* 菜单容器 */
.menu-container {
  width: 750rpx;
  background-color: #ffffff;
  border-top: 20rpx solid #f5f5f5;
}

.menu-item {
  width: 750rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-sizing: border-box;
  border-bottom: 20rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.menu-item:active {
  background-color: #f8f8f8;
}

.menu-item:last-child {
  border-bottom: none;
}

/* 菜单图标 */
.menu-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  position: relative;
}

/* 菜单图标图片 */
.menu-icon-img {
  width: 32rpx;
  height: 32rpx;
}

.favorites-icon {
  background-color: #FF4444;
}

.follow-icon {
  background-color: #4CAF50;
}

.browse-icon {
  background-color: #2196F3;
}

.about-icon {
  background-color: #FF9800;
}

.phone-icon {
  background-color: #9C27B0;
}

/* 菜单文字 */
.menu-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 400;
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.menu-subtitle {
  font-size: 24rpx;
  color: #999999;
  margin-top: 4rpx;
}

/* 绑定手机号按钮 */
.phone-button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  width: 100%;
  text-align: left;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.phone-button::after {
  border: none;
}

/* 菜单箭头 */
.menu-arrow {
  font-size: 28rpx;
  color: #cccccc;
  margin-left: auto;
}

/* 退出登录按钮样式 */
.logout-item {
  margin-top: 40rpx;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 30rpx;
}

.logout-icon {
  background: #ff3b30;
  border-radius: 50%;
  padding: 10rpx;
}

.logout-text {
  color: #ff3b30;
  font-weight: 500;
}

.logout-item:active {
  background-color: #fff0f0;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .user-name {
    font-size: 32rpx;
  }

  .menu-text {
    font-size: 30rpx;
  }

  .menu-subtitle {
    font-size: 22rpx;
  }
}

/* 新用户授权弹窗样式 */
.auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-modal-content {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

.auth-modal-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
  position: relative;
}

.auth-modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.auth-modal-close {
  position: absolute;
  right: 40rpx;
  top: 40rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999999;
}

.auth-modal-body {
  padding: 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.auth-tip {
  margin-bottom: 40rpx;
  text-align: center;
}

.auth-tip-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

.auth-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: all 0.3s ease;
}

.auth-item:last-child {
  border-bottom: none;
}

.auth-item.completed {
  opacity: 0.6;
}

.auth-item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.auth-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.auth-text {
  font-size: 28rpx;
  color: #333333;
}

.auth-btn {
  padding: 16rpx 32rpx;
  background-color: #F8877D;
  color: #ffffff;
  border-radius: 30rpx;
  font-size: 24rpx;
  border: none;
  line-height: 1;
}

.auth-btn-text {
  font-size: 24rpx;
}

.auth-input {
  flex: 1;
  padding: 16rpx 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 10rpx;
  font-size: 26rpx;
  margin-left: 20rpx;
}

.completed-info {
  margin-top: 40rpx;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
}

.completed-title {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 20rpx;
}

.completed-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.completed-item:last-child {
  margin-bottom: 0;
}

.completed-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.completed-text {
  font-size: 26rpx;
  color: #52c41a;
}

.auth-modal-footer {
  display: flex;
  padding: 30rpx 40rpx;
  border-top: 2rpx solid #f5f5f5;
  gap: 20rpx;
}

.auth-skip-btn {
  flex: 1;
  padding: 24rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #666666;
  background-color: #f8f8f8;
  border-radius: 30rpx;
}

.auth-complete-btn {
  flex: 2;
  padding: 24rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #ffffff;
  background-color: #cccccc;
  border-radius: 30rpx;
  transition: all 0.3s ease;
}

.auth-complete-btn.active {
  background-color: #F8877D;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
