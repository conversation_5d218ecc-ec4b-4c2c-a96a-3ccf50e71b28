package com.example.cos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.cos.dto.HouseSaleRegistrationCreateDTO;
import com.example.cos.entity.HouseSaleRegistration;

import java.util.List;

/**
 * 房源出售登记服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface HouseSaleRegistrationService extends IService<HouseSaleRegistration> {

    /**
     * 创建房源出售登记
     * 
     * @param createDTO 创建DTO
     * @return 创建的登记信息
     */
    HouseSaleRegistration createRegistration(HouseSaleRegistrationCreateDTO createDTO);

    /**
     * 根据用户ID查询房源出售登记
     * 
     * @param userId 用户ID
     * @return 登记列表
     */
    List<HouseSaleRegistration> findByUserId(Integer userId);

    /**
     * 根据城市查询房源出售登记
     * 
     * @param city 城市
     * @return 登记列表
     */
    List<HouseSaleRegistration> findByCity(String city);

    /**
     * 根据小区名称查询房源出售登记
     * 
     * @param communityName 小区名称
     * @return 登记列表
     */
    List<HouseSaleRegistration> findByCommunityName(String communityName);

    /**
     * 根据房屋类型查询房源出售登记
     * 
     * @param houseType 房屋类型
     * @return 登记列表
     */
    List<HouseSaleRegistration> findByHouseType(String houseType);

    /**
     * 更新房源出售登记
     * 
     * @param registration 登记信息
     * @return 是否更新成功
     */
    boolean updateRegistration(HouseSaleRegistration registration);

    /**
     * 删除房源出售登记
     * 
     * @param id 登记ID
     * @return 是否删除成功
     */
    boolean deleteRegistration(Integer id);
}
