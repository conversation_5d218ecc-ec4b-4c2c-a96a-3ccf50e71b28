// pages/profile/profile.js
const userService = require('../../services/user.js')
const util = require('../../utils/util.js')
const api = require('../../config/api.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {
      nickname: '点击登录',
      avatarUrl: ''
    },
    hasUserInfo: false,
    isPhoneBound: false,
    isLoggedIn: false,
    // 新用户授权弹窗相关
    showAuthModal: false,
    authStatus: {
      avatar: false,
      nickname: false,
      phone: false
    },
    tempNickname: '',
    tempAvatarUrl: '',
    tempPhoneNumber: '',
    completedCount: 0,
    canComplete: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkLoginStatus()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkLoginStatus()
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const isLoggedIn = userService.checkLoginStatus()
    if (isLoggedIn) {
      const userInfo = userService.getLocalUserInfo()
      if (userInfo) {
        this.setData({
          userInfo: {
            nickname: userInfo.nickname || '微信用户',
            avatarUrl: userInfo.avatarUrl || ''
          },
          hasUserInfo: true,
          isLoggedIn: true,
          isPhoneBound: !!userInfo.phoneNumber
        })
      }
    } else {
      this.setData({
        userInfo: {
          nickname: '点击登录',
          avatarUrl: ''
        },
        hasUserInfo: false,
        isLoggedIn: false,
        isPhoneBound: false
      })
    }
  },

  /**
   * 登录处理
   */
  onLogin() {
    if (this.data.isLoggedIn) {
      // 已登录，显示用户操作菜单
      this.showUserMenu()
      return
    }

    // 必须在用户点击事件中直接调用getUserProfile
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        console.log('获取用户信息成功', res)
        // 获取用户信息成功后，继续登录流程
        this.performLogin(res.userInfo)
      },
      fail: (err) => {
        console.error('获取用户信息失败', err)
        util.showError('需要授权才能登录')
      }
    })
  },

  /**
   * 执行登录流程
   */
  async performLogin(userProfile) {
    try {
      util.showLoading('登录中...')

      // 1. 获取微信授权码
      const code = await userService.getWechatCode()

      // 2. 调用微信登录API
      // 检查是否已有用户信息，如果有则不传递头像和昵称，避免覆盖用户设置
      const existingUserInfo = userService.getLocalUserInfo()
      const loginData = {
        code: code,
        phoneNumber: '' // 提供默认空值，避免数据库错误
      }

      // 只有在没有现有用户信息或用户信息为默认值时才传递微信的头像和昵称
      if (!existingUserInfo ||
          !existingUserInfo.nickname ||
          existingUserInfo.nickname === '微信用户' ||
          existingUserInfo.nickname === '点击登录') {
        loginData.nickname = userProfile.nickName
      }

      if (!existingUserInfo ||
          !existingUserInfo.avatarUrl ||
          existingUserInfo.avatarUrl === '') {
        loginData.avatarUrl = userProfile.avatarUrl
      }

      const result = await userService.wechatLogin(loginData)

      util.hideLoading()

      // 3. 更新页面状态 - 优先使用服务器保存的用户信息
      this.setData({
        userInfo: {
          nickname: result.user.nickname || '微信用户',
          avatarUrl: result.user.avatarUrl || userProfile.avatarUrl,
          phoneNumber: result.user.phoneNumber || ''
        },
        hasUserInfo: true,
        isLoggedIn: true,
        isPhoneBound: !!result.user.phoneNumber
      })

      // 4. 检查是否为新用户，显示授权弹窗
      if (result.isNewUser) {
        util.showSuccess('登录成功')
        // 显示新用户授权弹窗
        this.showNewUserAuthModal(result.user)
      } else {
        util.showSuccess('登录成功')
      }

    } catch (error) {
      util.hideLoading()
      console.error('登录失败:', error)

      // 修复错误处理
      const errorMessage = error.message || error.errMsg || '登录失败'
      if (errorMessage.includes('getUserProfile') || errorMessage.includes('login')) {
        util.showError('登录失败，请重试')
      } else {
        util.showError('网络错误，请检查网络连接')
      }
    }
  },

  /**
   * 显示用户菜单
   */
  showUserMenu() {
    wx.showActionSheet({
      itemList: ['退出登录'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.onLogout()
        }
      }
    })
  },

  /**
   * 退出登录
   */
  async onLogout() {
    try {
      await userService.logout()
      this.setData({
        userInfo: {
          nickname: '点击登录',
          avatarUrl: ''
        },
        hasUserInfo: false,
        isLoggedIn: false,
        isPhoneBound: false
      })
      util.showSuccess('已退出登录')
    } catch (error) {
      console.error('退出登录失败:', error)
      util.showError('退出失败')
    }
  },

  /**
   * 更多选项
   */
  onMoreOptions() {
    wx.showActionSheet({
      itemList: ['分享', '举报', '设置'],
      success: (res) => {
        console.log('选择了第' + (res.tapIndex + 1) + '个选项');
        switch(res.tapIndex) {
          case 0:
            this.onShare();
            break;
          case 1:
            this.onReport();
            break;
          case 2:
            this.onSettings();
            break;
        }
      }
    });
  },

  /**
   * 设置
   */
  onSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings',
      success: () => {
        console.log('成功跳转到设置页面')
      },
      fail: (err) => {
        console.error('跳转设置页面失败:', err)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  },



  /**
   * 处理手机号获取
   */
  async handlePhoneNumber(e) {
    try {
      util.showLoading('绑定中...')

      // 1. 获取解密后的手机号
      const phoneNumber = await userService.getUserPhoneNumber(e)

      // 2. 绑定手机号到用户账户
      await userService.bindPhoneNumber(phoneNumber)

      util.hideLoading()

      // 3. 更新页面状态
      this.setData({
        isPhoneBound: true
      })

      // 4. 更新用户信息显示
      const userInfo = userService.getLocalUserInfo()
      if (userInfo) {
        this.setData({
          userInfo: {
            nickname: userInfo.nickname || '微信用户',
            avatarUrl: userInfo.avatarUrl || this.data.userInfo.avatarUrl
          }
        })
      }

      util.showSuccess('手机号绑定成功')

    } catch (error) {
      util.hideLoading()
      console.error('绑定手机号失败:', error)

      const errorMessage = error.message || error.errMsg || '绑定失败'
      if (errorMessage.includes('拒绝')) {
        util.showError('需要授权手机号才能绑定')
      } else if (errorMessage.includes('登录')) {
        util.showError('请先登录')
      } else {
        util.showError('绑定失败，请重试')
      }
    }
  },

  /**
   * 显示二维码
   */
  onShowQRCode() {
    wx.showModal({
      title: '个人二维码',
      content: '二维码功能开发中',
      showCancel: false
    });
  },

  /**
   * 分享
   */
  onShare() {
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    });
  },

  /**
   * 举报
   */
  onReport() {
    wx.showToast({
      title: '举报功能开发中',
      icon: 'none'
    });
  },

  /**
   * 页面导航处理
   */
  navigateTo(e) {
    const page = e.currentTarget.dataset.page;
    console.log('导航到页面:', page);

    // 检查需要登录的页面
    const loginRequiredPages = ['favorites', 'follow'];

    if (loginRequiredPages.includes(page) && !this.data.isLoggedIn) {
      // 未登录时显示登录提示
      wx.showModal({
        title: '需要登录',
        content: '请先登录后再查看相关内容',
        showCancel: true,
        cancelText: '取消',
        confirmText: '去登录',
        confirmColor: '#007aff',
        success: (res) => {
          if (res.confirm) {
            // 用户选择去登录，触发登录流程
            this.onLogin();
          }
        }
      });
      return;
    }

    switch(page) {
      case 'favorites':
        // 跳转到房源列表页面，显示收藏的房源
        wx.navigateTo({
          url: '/pages/house-list/house-list?type=user-favorites',
          success: () => {
            console.log('成功跳转到我的收藏列表');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      case 'follow':
        // 跳转到房源列表页面，显示关注的房源
        wx.navigateTo({
          url: '/pages/house-list/house-list?type=user-follows',
          success: () => {
            console.log('成功跳转到我的关注列表');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        break;
      case 'browse':
        wx.showToast({
          title: '直播看房功能开发中',
          icon: 'none'
        });
        break;
      case 'about':
        wx.showModal({
          title: '关于我们',
          content: '锦绣资产 - 专业的房产服务平台\n\n获取最新政策、专业咨询服务\n\n联系我们获取更多信息',
          showCancel: false,
          confirmText: '知道了'
        });
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 退出登录
   */
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定',
      confirmColor: '#ff3b30',
      success: (res) => {
        if (res.confirm) {
          this.performLogout();
        }
      }
    });
  },

  /**
   * 执行退出登录操作
   */
  performLogout() {
    wx.showLoading({
      title: '退出中...'
    });

    try {
      // 清除所有用户相关的本地存储数据
      this.clearUserData();

      // 重置页面数据为未登录状态
      this.setData({
        userInfo: {
          nickname: '点击登录',
          avatarUrl: ''
        },
        hasUserInfo: false,
        isPhoneBound: false,
        isLoggedIn: false
      });

      wx.hideLoading();

      wx.showToast({
        title: '已退出登录',
        icon: 'success',
        duration: 2000
      });

      console.log('用户已退出登录');

    } catch (error) {
      console.error('退出登录失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '退出失败',
        icon: 'none'
      });
    }
  },

  /**
   * 清除所有用户数据
   */
  clearUserData() {
    try {
      // 清除用户基本信息
      wx.removeStorageSync('userInfo');
      wx.removeStorageSync('userId');
      wx.removeStorageSync('userRole');
      wx.removeStorageSync('token');
      wx.removeStorageSync('openid');
      wx.removeStorageSync('sessionKey');

      // 清除用户关注和收藏的本地缓存
      const storage = wx.getStorageInfoSync();
      const keys = storage.keys;

      keys.forEach(key => {
        // 清除关注相关的缓存 (follow_userId_propertyId)
        if (key.startsWith('follow_')) {
          wx.removeStorageSync(key);
        }
        // 清除收藏相关的缓存 (favorite_userId_propertyId)
        if (key.startsWith('favorite_')) {
          wx.removeStorageSync(key);
        }
        // 清除其他用户相关的缓存
        if (key.startsWith('user_') || key.includes('login') || key.includes('auth')) {
          wx.removeStorageSync(key);
        }
      });

      console.log('用户数据清除完成');

    } catch (error) {
      console.error('清除用户数据失败:', error);
      throw error;
    }
  },

  /**
   * 显示新用户授权弹窗
   */
  showNewUserAuthModal(userInfo) {
    // 新用户默认都需要完善信息，不管微信是否有头像
    const authStatus = {
      avatar: false, // 新用户头像默认未完成，需要主动选择上传
      nickname: false, // 新用户昵称默认未完成，需要主动输入
      phone: false // 新用户手机号默认未完成，需要主动授权
    }

    const completedCount = 0 // 新用户默认都是0

    this.setData({
      showAuthModal: true,
      authStatus: authStatus,
      completedCount: completedCount,
      canComplete: false, // 新用户需要至少完成一项
      tempNickname: userInfo.nickname || '',
      tempAvatarUrl: userInfo.avatarUrl || ''
    })
  },

  /**
   * 隐藏授权弹窗
   */
  hideAuthModal() {
    this.setData({
      showAuthModal: false
    })
  },

  /**
   * 阻止弹窗关闭
   */
  preventClose() {
    // 阻止事件冒泡，防止点击内容区域关闭弹窗
  },

  /**
   * 选择头像
   */
  onChooseAvatar(e) {
    const avatarUrl = e.detail.avatarUrl
    console.log('选择头像:', avatarUrl)

    // 上传头像到服务器
    this.uploadAvatar(avatarUrl)
  },

  /**
   * 上传头像到服务器
   */
  uploadAvatar(localPath) {
    wx.showLoading({
      title: '上传头像中...'
    })

    wx.uploadFile({
      url: `${api.baseUrl}/api/cos/upload`,
      filePath: localPath,
      name: 'file',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (res) => {
        wx.hideLoading()

        try {
          const response = JSON.parse(res.data)
          console.log('头像上传响应:', response)

          if (response.code === 200) {
            // 上传成功，保存服务器返回的URL
            this.setData({
              tempAvatarUrl: response.data.fileUrl,
              'authStatus.avatar': true
            })

            this.updateCompletedCount()

            wx.showToast({
              title: '头像上传成功',
              icon: 'success'
            })
          } else {
            throw new Error(response.message || '头像上传失败')
          }
        } catch (error) {
          console.error('解析上传响应失败:', error)
          wx.showToast({
            title: '头像上传失败',
            icon: 'none'
          })
        }
      },
      fail: (error) => {
        wx.hideLoading()
        console.error('头像上传失败:', error)

        wx.showToast({
          title: '头像上传失败，请重试',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 昵称输入
   */
  onNicknameInput(e) {
    const nickname = e.detail.value.trim()
    console.log('输入昵称:', nickname)

    this.setData({
      tempNickname: nickname,
      'authStatus.nickname': nickname.length > 0
    })

    this.updateCompletedCount()
  },

  /**
   * 获取手机号
   */
  onGetPhoneNumber(e) {
    console.log('获取手机号:', e)

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 处理手机号获取
      this.handlePhoneNumber(e.detail.code)
    } else {
      // 用户拒绝授权
      wx.showToast({
        title: '需要授权手机号',
        icon: 'none'
      })
    }
  },

  /**
   * 处理手机号获取
   */
  async handlePhoneNumber(code) {
    try {
      wx.showLoading({
        title: '获取手机号中...'
      })

      // 调用获取手机号API
      const response = await api.get(`${api.API.WECHAT_PHONE}?code=${code}`)
      console.log('手机号API完整响应:', response)

      if (response.code === 200) {
        // 详细检查返回的数据结构
        console.log('response.data类型:', typeof response.data)
        console.log('response.data内容:', JSON.stringify(response.data))

        let phoneNumber = null

        if (response.data) {
          // 尝试不同的数据结构
          phoneNumber = response.data.phoneNumber ||
                       response.data.phone_number ||
                       response.data.mobile ||
                       response.data.phone ||
                       response.data.purePhoneNumber ||
                       response.data.countryCode ||
                       (typeof response.data === 'string' ? response.data : null)
        }

        console.log('解析到的手机号:', phoneNumber)

        if (phoneNumber) {
          // 更新授权状态
          this.setData({
            'authStatus.phone': true,
            tempPhoneNumber: phoneNumber
          })

          this.updateCompletedCount()

          wx.hideLoading()
          wx.showToast({
            title: '手机号获取成功',
            icon: 'success'
          })
        } else {
          // 后端API问题，显示详细信息给开发者
          wx.hideLoading()

          wx.showModal({
            title: '后端API问题',
            content: `后端API返回成功但未包含手机号数据。\n\n返回数据：${JSON.stringify(response)}\n\n请联系后端开发者修复 /api/wechat/phone-number 接口`,
            showCancel: true,
            cancelText: '取消',
            confirmText: '模拟成功',
            success: (res) => {
              if (res.confirm) {
                // 开发阶段的临时解决方案：模拟手机号获取成功
                const mockPhoneNumber = '138****' + Math.floor(Math.random() * 10000).toString().padStart(4, '0')

                this.setData({
                  'authStatus.phone': true,
                  tempPhoneNumber: mockPhoneNumber
                })

                this.updateCompletedCount()

                wx.showToast({
                  title: '模拟手机号获取成功',
                  icon: 'success'
                })
              }
            }
          })
          return
        }
      } else {
        throw new Error(response.message || '获取手机号失败')
      }
    } catch (error) {
      wx.hideLoading()
      console.error('获取手机号失败:', error)

      // 显示更详细的错误信息
      let errorMessage = '获取手机号失败'
      if (error.message) {
        errorMessage = error.message
      }

      wx.showModal({
        title: '获取手机号失败',
        content: `错误信息：${errorMessage}\n\n请检查网络连接或联系客服`,
        showCancel: false,
        confirmText: '确定'
      })
    }
  },

  /**
   * 更新完成数量
   */
  updateCompletedCount() {
    const completedCount = Object.values(this.data.authStatus).filter(Boolean).length

    this.setData({
      completedCount: completedCount,
      canComplete: completedCount >= 1
    })
  },

  /**
   * 跳过授权
   */
  skipAuth() {
    wx.showModal({
      title: '确认跳过',
      content: '跳过后可在设置中重新完善信息，确定要跳过吗？',
      showCancel: true,
      cancelText: '取消',
      confirmText: '跳过',
      success: (res) => {
        if (res.confirm) {
          this.hideAuthModal()
        }
      }
    })
  },

  /**
   * 完成授权
   */
  async completeAuth() {
    if (!this.data.canComplete) {
      wx.showToast({
        title: '请至少完成一项授权',
        icon: 'none'
      })
      return
    }

    try {
      wx.showLoading({
        title: '保存中...'
      })

      // 获取当前用户信息
      const currentUserInfo = userService.getLocalUserInfo() || {}

      // 构建更新数据，使用完整的用户对象
      const updateData = {
        id: currentUserInfo.id,
        wechatOpenid: currentUserInfo.wechatOpenid,
        role: currentUserInfo.role || 0,
        createTime: currentUserInfo.createTime,
        favoriteHouseIds: currentUserInfo.favoriteHouseIds || [],
        followedHouseIds: currentUserInfo.followedHouseIds || []
      }

      // 添加需要更新的字段
      if (this.data.authStatus.avatar && this.data.tempAvatarUrl) {
        updateData.avatarUrl = this.data.tempAvatarUrl
      } else if (currentUserInfo.avatarUrl) {
        updateData.avatarUrl = currentUserInfo.avatarUrl
      }

      if (this.data.authStatus.nickname && this.data.tempNickname) {
        updateData.nickname = this.data.tempNickname
      } else if (currentUserInfo.nickname) {
        updateData.nickname = currentUserInfo.nickname
      }

      if (this.data.authStatus.phone && this.data.tempPhoneNumber) {
        updateData.phoneNumber = this.data.tempPhoneNumber
      } else if (currentUserInfo.phoneNumber) {
        updateData.phoneNumber = currentUserInfo.phoneNumber
      }

      console.log('更新用户信息数据:', updateData)

      // 调用用户信息更新API
      const response = await api.put(api.API.USER_UPDATE, updateData)

      if (response.code === 200) {
        // 更新本地用户信息
        const newUserInfo = { ...currentUserInfo, ...updateData }
        userService.saveLocalUserInfo(newUserInfo)

        // 更新页面显示
        this.setData({
          userInfo: {
            nickname: newUserInfo.nickname || '微信用户',
            avatarUrl: newUserInfo.avatarUrl || ''
          },
          isPhoneBound: !!newUserInfo.phoneNumber
        })

        wx.hideLoading()

        // 显示完成提示
        wx.showToast({
          title: '信息保存成功',
          icon: 'success'
        })

        // 关闭弹窗
        this.hideAuthModal()
      } else {
        throw new Error(response.message || '保存用户信息失败')
      }

    } catch (error) {
      wx.hideLoading()
      console.error('保存用户信息失败:', error)

      wx.showToast({
        title: error.message || '保存失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
