package com.example.cos.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.cos.entity.HouseSaleRegistration;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 房源出售登记数据访问层
 */
@Mapper
public interface HouseSaleRegistrationMapper extends BaseMapper<HouseSaleRegistration> {

    @Select("SELECT * FROM house_sale_registration_table WHERE user_id = #{userId}")
    List<HouseSaleRegistration> findByUserId(@Param("userId") Integer userId);

    @Select("SELECT * FROM house_sale_registration_table WHERE city = #{city}")
    List<HouseSaleRegistration> findByCity(@Param("city") String city);

    @Select("SELECT * FROM house_sale_registration_table WHERE community_name LIKE CONCAT('%', #{communityName}, '%')")
    List<HouseSaleRegistration> findByCommunityName(@Param("communityName") String communityName);

    @Select("SELECT * FROM house_sale_registration_table WHERE house_type = #{houseType}")
    List<HouseSaleRegistration> findByHouseType(@Param("houseType") String houseType);
}
