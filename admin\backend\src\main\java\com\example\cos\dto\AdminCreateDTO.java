package com.example.cos.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 管理员创建DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "管理员创建请求")
public class AdminCreateDTO {

    @ApiModelProperty(value = "管理员账号", example = "admin001", required = true)
    @NotBlank(message = "管理员账号不能为空")
    @Size(min = 3, max = 20, message = "管理员账号长度必须在3-20个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "管理员账号只能包含字母、数字和下划线")
    private String username;

    @ApiModelProperty(value = "密码", example = "admin123", required = true)
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    @ApiModelProperty(value = "角色", example = "admin", required = true)
    @NotBlank(message = "角色不能为空")
    @Pattern(regexp = "^(super_admin|admin|normal_admin)$", message = "角色只能是super_admin、admin或normal_admin")
    private String role;

    @ApiModelProperty(value = "账号状态", example = "1", required = true)
    @NotNull(message = "账号状态不能为空")
    @Pattern(regexp = "^[01]$", message = "账号状态只能是0（禁用）或1（启用）")
    private Integer status;

    public AdminCreateDTO() {}

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "AdminCreateDTO{" +
                "username='" + username + '\'' +
                ", password='[PROTECTED]'" +
                ", role='" + role + '\'' +
                ", status=" + status +
                '}';
    }
}
