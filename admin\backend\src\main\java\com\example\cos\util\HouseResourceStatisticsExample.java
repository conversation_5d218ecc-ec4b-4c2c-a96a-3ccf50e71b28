package com.example.cos.util;

import com.example.cos.dto.HouseResourceStatisticsDTO;
import com.example.cos.entity.HouseResource;
import com.example.cos.service.HouseResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 房源统计功能使用示例
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class HouseResourceStatisticsExample {

    @Autowired
    private HouseResourceService houseResourceService;

    /**
     * 示例：创建房源时自动设置创建时间
     */
    public void createHouseWithCreateTimeExample() {
        HouseResource newHouse = new HouseResource();
        newHouse.setTitle("统计测试房源");
        newHouse.setStartingPrice(new BigDecimal("1000000.00"));
        newHouse.setEvaluationPrice(new BigDecimal("1200000.00"));
        newHouse.setStartTime(LocalDateTime.now().plusDays(1));
        newHouse.setEndTime(LocalDateTime.now().plusDays(8));
        newHouse.setBuildingArea(new BigDecimal("100.00"));
        newHouse.setOriginalUrl("https://example.com/house/test");
        
        // create_time字段会自动填充，无需手动设置
        try {
            HouseResource created = houseResourceService.createHouseResource(newHouse);
            System.out.println("房源创建成功，创建时间: " + created.getCreateTime());
        } catch (Exception e) {
            System.out.println("房源创建失败: " + e.getMessage());
        }
    }

    /**
     * 示例：获取房源统计数据
     */
    public void getStatisticsExample() {
        try {
            HouseResourceStatisticsDTO statistics = houseResourceService.getHouseResourceStatistics();
            
            System.out.println("=== 房源统计数据 ===");
            System.out.println("今日新增数量: " + statistics.getTodayNewCount());
            System.out.println("正在拍卖数量: " + statistics.getOngoingAuctionCount());
            System.out.println("即将拍卖数量: " + statistics.getUpcomingAuctionCount());
            
        } catch (Exception e) {
            System.out.println("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 示例：创建不同时间状态的房源用于测试统计功能
     */
    public void createTestHousesForStatistics() {
        // 创建正在拍卖的房源
        HouseResource ongoingHouse = new HouseResource();
        ongoingHouse.setTitle("正在拍卖的房源");
        ongoingHouse.setStartingPrice(new BigDecimal("800000.00"));
        ongoingHouse.setEvaluationPrice(new BigDecimal("1000000.00"));
        ongoingHouse.setStartTime(LocalDateTime.now().minusDays(1)); // 昨天开始
        ongoingHouse.setEndTime(LocalDateTime.now().plusDays(6)); // 6天后结束
        ongoingHouse.setBuildingArea(new BigDecimal("90.00"));
        ongoingHouse.setOriginalUrl("https://example.com/house/ongoing");

        // 创建即将拍卖的房源
        HouseResource upcomingHouse = new HouseResource();
        upcomingHouse.setTitle("即将拍卖的房源");
        upcomingHouse.setStartingPrice(new BigDecimal("1500000.00"));
        upcomingHouse.setEvaluationPrice(new BigDecimal("1800000.00"));
        upcomingHouse.setStartTime(LocalDateTime.now().plusDays(3)); // 3天后开始
        upcomingHouse.setEndTime(LocalDateTime.now().plusDays(10)); // 10天后结束
        upcomingHouse.setBuildingArea(new BigDecimal("120.00"));
        upcomingHouse.setOriginalUrl("https://example.com/house/upcoming");

        try {
            houseResourceService.createHouseResource(ongoingHouse);
            houseResourceService.createHouseResource(upcomingHouse);
            System.out.println("测试房源创建成功");
            
            // 获取统计数据验证
            getStatisticsExample();
            
        } catch (Exception e) {
            System.out.println("创建测试房源失败: " + e.getMessage());
        }
    }

    /**
     * 示例：演示API调用方式
     */
    public void apiCallExample() {
        System.out.println("=== API调用示例 ===");
        System.out.println("GET /api/house/statistics/overview");
        System.out.println("返回格式:");
        System.out.println("{");
        System.out.println("  \"code\": 200,");
        System.out.println("  \"message\": \"获取成功\",");
        System.out.println("  \"data\": {");
        System.out.println("    \"todayNewCount\": 15,");
        System.out.println("    \"ongoingAuctionCount\": 25,");
        System.out.println("    \"upcomingAuctionCount\": 10");
        System.out.println("  }");
        System.out.println("}");
    }
}
