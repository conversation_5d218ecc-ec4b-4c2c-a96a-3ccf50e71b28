package com.example.cos.controller;

import com.example.cos.common.Result;
import com.example.cos.common.ResultCode;
import com.example.cos.entity.Carousel;
import com.example.cos.service.CarouselService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 小程序API控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Api(tags = "小程序API")
@RestController
@RequestMapping("/api/miniprogram")
public class MiniProgramController {

    private static final Logger logger = LoggerFactory.getLogger(MiniProgramController.class);

    @Autowired
    private CarouselService carouselService;

    /**
     * 获取轮播图列表（小程序首页）
     */
    @ApiOperation(value = "获取轮播图列表", notes = "获取启用状态的轮播图，用于小程序首页展示")
    @GetMapping("/carousels")
    public Result<List<Carousel>> getCarousels() {
        try {
            logger.info("小程序获取轮播图列表");
            
            List<Carousel> carousels = carouselService.getEnabledCarousels();
            
            logger.info("小程序获取轮播图列表成功，数量: {}", carousels.size());
            return Result.success("获取轮播图列表成功", carousels);
        } catch (Exception e) {
            logger.error("小程序获取轮播图列表失败: {}", e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "获取轮播图列表失败: " + e.getMessage());
        }
    }
}
