// pages/calculation-result/calculation-result.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 当前选中的标签页 (0: 等额本息, 1: 等额本金)
    currentTab: 0,

    // 计算参数
    loanAmount: 0,      // 贷款总额(万元)
    loanYears: 0,       // 贷款年限
    interestRate: 0,    // 年利率

    // 计算结果
    monthlyPayment: '0.00',     // 每月应还金额
    totalInterest: '0.00',      // 利息总额
    totalPayment: '0.00',       // 还款总额
    monthlyDecrease: '0.00',    // 月供递减金额（等额本金模式）

    // 还款明细
    paymentSchedule: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('房贷计算结果页面加载', options);
    
    // 获取传递的计算参数
    if (options.loanAmount && options.loanYears && options.interestRate) {
      const loanAmount = parseFloat(options.loanAmount);
      const loanYears = parseInt(options.loanYears);
      const interestRate = parseFloat(options.interestRate);
      
      this.setData({
        loanAmount: loanAmount,
        loanYears: loanYears,
        interestRate: interestRate
      });
      
      // 执行计算
      this.calculateLoan();
    } else {
      wx.showToast({
        title: '计算参数错误',
        icon: 'none'
      });
    }
  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const tab = parseInt(e.currentTarget.dataset.tab);

    // 如果是同一个标签，不需要切换
    if (tab === this.data.currentTab) {
      return;
    }

    console.log('切换标签页:', tab === 0 ? '等额本息' : '等额本金');

    // 先更新标签状态
    this.setData({
      currentTab: tab
    });

    // 延迟一帧后重新计算，确保UI更新流畅
    wx.nextTick(() => {
      this.calculateLoan();
    });
  },

  /**
   * 房贷计算主函数
   */
  calculateLoan() {
    const { loanAmount, loanYears, interestRate, currentTab } = this.data;
    const totalMonths = loanYears * 12;

    console.log('开始计算房贷 - 模式:', currentTab === 0 ? '等额本息' : '等额本金', '总期数:', totalMonths);

    // 先清空表格数据，避免闪烁
    this.setData({
      paymentSchedule: []
    });

    // 如果期数较多，显示加载提示
    if (totalMonths > 120) { // 10年以上
      wx.showLoading({
        title: '计算中...',
        mask: true
      });
    }

    // 延迟计算，确保UI更新流畅
    setTimeout(() => {
      try {
        if (currentTab === 0) {
          // 等额本息计算
          this.calculateEqualPayment();
        } else {
          // 等额本金计算
          this.calculateEqualPrincipal();
        }
      } finally {
        // 隐藏加载提示
        if (totalMonths > 120) {
          wx.hideLoading();
        }
      }
    }, 50);
  },

  /**
   * 等额本息计算
   */
  calculateEqualPayment() {
    const { loanAmount, loanYears, interestRate } = this.data;
    
    const principal = loanAmount * 10000; // 转换为元
    const monthlyRate = interestRate / 100 / 12; // 月利率
    const totalMonths = loanYears * 12; // 总月数
    
    // 计算每月还款额
    const monthlyPayment = principal * monthlyRate * Math.pow(1 + monthlyRate, totalMonths) / 
                          (Math.pow(1 + monthlyRate, totalMonths) - 1);
    
    // 计算总利息
    const totalPayment = monthlyPayment * totalMonths;
    const totalInterest = totalPayment - principal;
    
    // 生成还款明细（显示完整期数）
    const schedule = [];
    let remainingPrincipal = principal;

    console.log('等额本息计算 - 总期数:', totalMonths);

    for (let i = 1; i <= totalMonths; i++) {
      const interestPayment = remainingPrincipal * monthlyRate;
      const principalPayment = monthlyPayment - interestPayment;
      remainingPrincipal -= principalPayment;

      schedule.push({
        period: i,
        monthlyPayment: monthlyPayment.toFixed(2),
        principal: principalPayment.toFixed(2),
        interest: interestPayment.toFixed(2),
        remainingPrincipal: Math.max(0, remainingPrincipal).toFixed(2) // 避免负数
      });
    }

    console.log('等额本息 - 生成还款明细条数:', schedule.length);
    
    this.setData({
      monthlyPayment: monthlyPayment.toFixed(2),
      totalInterest: (totalInterest / 10000).toFixed(2),
      totalPayment: (totalPayment / 10000).toFixed(2),
      monthlyDecrease: '0.00', // 等额本息模式下递减金额为0
      paymentSchedule: schedule
    });
  },

  /**
   * 等额本金计算
   */
  calculateEqualPrincipal() {
    const { loanAmount, loanYears, interestRate } = this.data;
    
    const principal = loanAmount * 10000; // 转换为元
    const monthlyRate = interestRate / 100 / 12; // 月利率
    const totalMonths = loanYears * 12; // 总月数
    
    // 每月应还本金
    const monthlyPrincipal = principal / totalMonths;
    
    // 生成还款明细（显示完整期数）
    const schedule = [];
    let remainingPrincipal = principal;
    let totalInterestSum = 0;

    console.log('等额本金计算 - 总期数:', totalMonths);

    for (let i = 1; i <= totalMonths; i++) {
      const interestPayment = remainingPrincipal * monthlyRate;
      const monthlyPayment = monthlyPrincipal + interestPayment;
      remainingPrincipal -= monthlyPrincipal;
      totalInterestSum += interestPayment;

      schedule.push({
        period: i,
        monthlyPayment: monthlyPayment.toFixed(2),
        principal: monthlyPrincipal.toFixed(2),
        interest: interestPayment.toFixed(2),
        remainingPrincipal: Math.max(0, remainingPrincipal).toFixed(2) // 避免负数
      });
    }

    console.log('等额本金 - 生成还款明细条数:', schedule.length);

    // 首月还款金额
    const firstMonthPayment = monthlyPrincipal + principal * monthlyRate;

    // 计算月供递减金额（第一期 - 第二期）
    let monthlyDecrease = 0;
    if (schedule.length >= 2) {
      const firstPayment = parseFloat(schedule[0].monthlyPayment);
      const secondPayment = parseFloat(schedule[1].monthlyPayment);
      monthlyDecrease = firstPayment - secondPayment;
    }

    console.log('等额本金 - 月供递减金额:', monthlyDecrease.toFixed(2));

    // 使用已计算的总利息
    this.setData({
      monthlyPayment: firstMonthPayment.toFixed(2),
      totalInterest: (totalInterestSum / 10000).toFixed(2),
      totalPayment: ((principal + totalInterestSum) / 10000).toFixed(2),
      monthlyDecrease: monthlyDecrease.toFixed(2),
      paymentSchedule: schedule
    });
  },

  /**
   * 在线咨询
   */
  onlineConsult() {
    wx.showToast({
      title: '在线咨询功能开发中',
      icon: 'none'
    });
  },

  /**
   * 电话咨询
   */
  phoneConsult() {
    wx.showModal({
      title: '电话咨询',
      content: '是否拨打客服电话：************？',
      confirmText: '拨打',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '4001234567',
            success: () => {
              console.log('拨打电话成功');
            },
            fail: (err) => {
              console.error('拨打电话失败:', err);
              wx.showToast({
                title: '拨打失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 显示帮助页面
   */
  showHelpModal() {
    console.log('跳转到帮助页面');
    wx.navigateTo({
      url: '/pages/help/help',
      success: () => {
        console.log('跳转帮助页面成功');
      },
      fail: (err) => {
        console.error('跳转帮助页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '锦绣资产 - 房贷计算结果',
      path: '/pages/calculation-result/calculation-result'
    };
  }
});
