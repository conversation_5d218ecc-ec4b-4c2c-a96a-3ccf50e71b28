# -*- coding: utf-8 -*-
"""
K2模型测试脚本
用于测试K2模型的API连接和房源解析功能
"""

import sys
import os
import json
import requests
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from api_client import APIClient
from ali_asset_scraper import AliAssetScraper


def test_k2_api_connection():
    """测试K2 API连接"""
    print("🧪 开始测试K2 API连接...")
    
    try:
        # 检查配置
        if not hasattr(Config, 'K2_API_KEY') or not Config.K2_API_KEY:
            print("❌ K2_API_KEY未配置，请在config.py中设置")
            return False
            
        if not hasattr(Config, 'K2_API_URL') or not Config.K2_API_URL:
            print("❌ K2_API_URL未配置，请在config.py中设置")
            return False
            
        if not hasattr(Config, 'K2_MODEL') or not Config.K2_MODEL:
            print("❌ K2_MODEL未配置，请在config.py中设置")
            return False
        
        print(f"📋 配置信息:")
        print(f"  API地址: {Config.K2_API_URL}")
        print(f"  模型名称: {Config.K2_MODEL}")
        print(f"  API密钥: {Config.K2_API_KEY[:20]}...")
        
        # 构建测试请求
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {Config.K2_API_KEY}'
        }
        
        payload = {
            "model": Config.K2_MODEL,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个AI助手，请简洁回答问题。"
                },
                {
                    "role": "user",
                    "content": "请回答：1+1等于几？只需要回答数字。"
                }
            ],
            "temperature": 0.1,
            "max_tokens": 50
        }
        
        print("\n📤 发送测试请求...")
        response = requests.post(
            Config.K2_API_URL,
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("✅ API连接成功")
                print(f"📋 响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                # 检查响应格式
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0].get('message', {}).get('content', '')
                    print(f"🤖 AI回答: {content}")
                    return True
                else:
                    print("⚠️ 响应格式异常，缺少choices字段")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ 响应JSON解析失败: {str(e)}")
                print(f"原始响应: {response.text}")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ API请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器")
        return False
    except Exception as e:
        print(f"❌ API连接测试失败: {str(e)}")
        return False


def test_k2_house_parsing():
    """测试K2模型房源解析功能"""
    print("\n🧪 开始测试K2模型房源解析功能...")
    
    try:
        # 创建API客户端和爬虫实例
        api_client = APIClient()
        scraper = AliAssetScraper(api_client)
        
        # 测试房源信息
        test_text = """
        房源标题: 一拍 重庆市沙坪坝区建设坡91-1-1号房屋
        基本信息: 建筑面积：50.18平方米 楼层：1/7 电梯房 毛坯房
        详细描述: 起拍价：123270元 评估价：176100元 保证金：20000元 加价幅度：1000元
        公告详情: 拍卖时间：将于2025年07月31日10：00时起至2025年08月01日10：00时止
        房屋类型：住宅 产权性质：商品房
        """
        
        image_urls = "https://cos.cqjxzc.com.cn/2025/08/01/test.jpg"
        original_url = "https://sf-item.taobao.com/sf_item/958511291138.htm"
        
        print("📋 测试数据:")
        print(f"  文本长度: {len(test_text)} 字符")
        print(f"  图片URL: {image_urls}")
        print(f"  原始URL: {original_url}")
        
        print("\n🤖 开始AI解析...")
        result = scraper.parse_with_ai(test_text, image_urls, original_url)
        
        if result:
            print("✅ AI解析成功")
            print(f"📋 解析结果:")
            
            # 格式化输出结果
            for key, value in result.items():
                print(f"  {key}: {value}")
            
            # 验证关键字段
            required_fields = ['title', 'startingPrice', 'evaluationPrice', 'buildingArea', 'startTime', 'endTime']
            missing_fields = []
            
            for field in required_fields:
                if field not in result or result[field] is None:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"⚠️ 缺少关键字段: {missing_fields}")
            else:
                print("✅ 所有关键字段都已提取")
            
            # 验证数据类型
            numeric_fields = ['startingPrice', 'evaluationPrice', 'buildingArea', 'deposit']
            type_errors = []
            
            for field in numeric_fields:
                if field in result and result[field] is not None:
                    if not isinstance(result[field], (int, float)):
                        type_errors.append(f"{field}: {type(result[field])}")
            
            if type_errors:
                print(f"⚠️ 数据类型错误: {type_errors}")
            else:
                print("✅ 数据类型验证通过")
            
            return True
        else:
            print("❌ AI解析失败")
            return False
            
    except Exception as e:
        print(f"❌ 房源解析测试失败: {str(e)}")
        return False


def test_k2_json_format():
    """测试K2模型JSON格式输出"""
    print("\n🧪 开始测试K2模型JSON格式输出...")
    
    try:
        # 直接调用K2 API测试JSON输出
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {Config.K2_API_KEY}'
        }
        
        payload = {
            "model": Config.K2_MODEL,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个数据分析师，请严格按照JSON格式返回数据，不要添加任何额外的解释。"
                },
                {
                    "role": "user",
                    "content": """请将以下信息转换为JSON格式：
                    姓名：张三
                    年龄：25
                    城市：北京
                    
                    JSON格式：
                    {
                      "name": "姓名",
                      "age": 年龄数字,
                      "city": "城市名称"
                    }"""
                }
            ],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        response = requests.post(
            Config.K2_API_URL,
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            ai_content = result['choices'][0]['message']['content']
            
            print(f"🤖 AI原始输出:")
            print(ai_content)
            
            # 尝试解析JSON
            try:
                # 提取JSON部分
                json_start = ai_content.find('{')
                json_end = ai_content.rfind('}') + 1
                
                if json_start >= 0 and json_end > json_start:
                    json_str = ai_content[json_start:json_end]
                    parsed_json = json.loads(json_str)
                    
                    print("✅ JSON格式解析成功")
                    print(f"📋 解析结果: {json.dumps(parsed_json, indent=2, ensure_ascii=False)}")
                    return True
                else:
                    print("❌ 未找到有效的JSON格式")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {str(e)}")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ JSON格式测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始K2模型综合测试...")
    print("=" * 60)
    
    # 测试结果统计
    test_results = []
    
    # 测试1: API连接
    print("📋 测试1: K2 API连接")
    result1 = test_k2_api_connection()
    test_results.append(("API连接", result1))
    
    if result1:
        # 测试2: JSON格式输出
        print("\n📋 测试2: JSON格式输出")
        result2 = test_k2_json_format()
        test_results.append(("JSON格式", result2))
        
        # 测试3: 房源解析
        print("\n📋 测试3: 房源解析功能")
        result3 = test_k2_house_parsing()
        test_results.append(("房源解析", result3))
    else:
        print("\n⚠️ API连接失败，跳过后续测试")
        test_results.append(("JSON格式", False))
        test_results.append(("房源解析", False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("🎉 K2模型测试完成！")
    print("\n📊 测试结果汇总:")
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    # 总体评估
    passed_count = sum(1 for _, result in test_results if result)
    total_count = len(test_results)
    
    print(f"\n📈 总体通过率: {passed_count}/{total_count} ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎯 所有测试通过，K2模型配置成功！")
    elif passed_count > 0:
        print("⚠️ 部分测试通过，请检查失败的测试项")
    else:
        print("❌ 所有测试失败，请检查K2模型配置")
    
    print("\n📋 下一步建议:")
    if not test_results[0][1]:  # API连接失败
        print("1. 检查K2_API_KEY、K2_API_URL、K2_MODEL配置")
        print("2. 确认API密钥有效且有足够额度")
        print("3. 检查网络连接和防火墙设置")
    elif not test_results[1][1]:  # JSON格式失败
        print("1. 调整temperature参数")
        print("2. 优化系统提示词")
        print("3. 检查模型是否支持结构化输出")
    elif not test_results[2][1]:  # 房源解析失败
        print("1. 检查提示词是否过于复杂")
        print("2. 调整max_tokens参数")
        print("3. 简化JSON格式要求")


if __name__ == "__main__":
    main()
