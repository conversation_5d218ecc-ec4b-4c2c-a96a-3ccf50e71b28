package com.example.cos.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * Knife4j API文档配置
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableSwagger2
@EnableKnife4j
public class Knife4jConfig implements WebMvcConfigurer {

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.example.cos.controller"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("锦绣资产小程序API")
                .description("基于锦绣资产小程序的文件存储服务API接口文档\n\n" +
                        "## 功能特性\n" +
                        "- 文件上传（单文件/多文件）\n" +
                        "- 文件下载\n" +
                        "- 文件删除\n" +
                        "- 文件存在检查\n" +
                        "- 获取文件访问URL\n\n" +
                        "## 技术栈\n" +
                        "- Java 8\n" +
                        "- Spring Boot 2.7.18\n" +
                        "- Knife4j API文档\n\n" +
                        "## 许可证\n" +
                        "Licensed under the Apache License, Version 2.0 (the \"License\");\n" +
                        "you may not use this file except in compliance with the License.\n" +
                        "You may obtain a copy of the License at\n\n" +
                        "    http://www.apache.org/licenses/LICENSE-2.0\n\n" +
                        "Unless required by applicable law or agreed to in writing, software\n" +
                        "distributed under the License is distributed on an \"AS IS\" BASIS,\n" +
                        "WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n" +
                        "See the License for the specific language governing permissions and\n" +
                        "limitations under the License.")
                .termsOfServiceUrl("https://www.apache.org/licenses/LICENSE-2.0")
                .contact(new Contact("Knife4j", "https://doc.xiaominfo.com/", "<EMAIL>"))
                .version("1.0.0")
                .license("Apache License 2.0")
                .licenseUrl("https://www.apache.org/licenses/LICENSE-2.0")
                .build();
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addRedirectViewController("/api/doc", "/doc.html");
    }
}
