package com.example.cos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.cos.dto.AdminCreateDTO;
import com.example.cos.dto.AdminLoginDTO;
import com.example.cos.dto.AdminLoginResultDTO;
import com.example.cos.dto.AdminUpdateDTO;
import com.example.cos.entity.Admin;

import java.util.List;

/**
 * 管理员服务接口
 */
public interface AdminService extends IService<Admin> {

    /**
     * 管理员登录
     */
    AdminLoginResultDTO login(AdminLoginDTO loginDTO);

    /**
     * 根据用户名查询管理员
     */
    Admin findByUsername(String username);

    /**
     * 根据用户名和状态查询管理员
     */
    Admin findByUsernameAndStatus(String username, Integer status);

    /**
     * 创建管理员
     */
    Admin createAdmin(String username, String password, String role);

    /**
     * 更新管理员密码
     */
    boolean updatePassword(Integer adminId, String oldPassword, String newPassword);

    /**
     * 启用/禁用管理员
     */
    boolean updateStatus(Integer adminId, Integer status);

    /**
     * 验证密码
     */
    boolean validatePassword(String rawPassword, String encodedPassword);

    /**
     * 加密密码
     */
    String encodePassword(String rawPassword);

    /**
     * 获取所有管理员列表（超级管理员专用）
     */
    List<Admin> getAllAdmins();

    /**
     * 创建管理员（超级管理员专用）
     */
    Admin createAdmin(AdminCreateDTO createDTO);

    /**
     * 更新管理员信息（超级管理员专用）
     */
    Admin updateAdmin(AdminUpdateDTO updateDTO);

    /**
     * 删除管理员（超级管理员专用）
     */
    boolean deleteAdmin(Integer adminId);

    /**
     * 批量删除管理员（超级管理员专用）
     */
    boolean batchDeleteAdmins(List<Integer> adminIds);

    /**
     * 切换管理员状态（超级管理员专用）
     */
    Admin toggleAdminStatus(Integer adminId);

    /**
     * 重置管理员密码（超级管理员专用）
     */
    boolean resetAdminPassword(Integer adminId, String newPassword);

    /**
     * 检查用户名是否已存在
     */
    boolean isUsernameExists(String username);

    /**
     * 检查用户名是否已存在（排除指定ID）
     */
    boolean isUsernameExists(String username, Integer excludeId);
}
