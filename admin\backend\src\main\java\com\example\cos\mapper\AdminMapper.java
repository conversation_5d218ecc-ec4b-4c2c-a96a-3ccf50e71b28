package com.example.cos.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.cos.entity.Admin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 管理员数据访问层
 */
@Mapper
public interface AdminMapper extends BaseMapper<Admin> {

    /**
     * 根据用户名查询管理员
     */
    @Select("SELECT * FROM admin_table WHERE username = #{username}")
    Admin findByUsername(@Param("username") String username);

    /**
     * 根据用户名和状态查询管理员
     */
    @Select("SELECT * FROM admin_table WHERE username = #{username} AND status = #{status}")
    Admin findByUsernameAndStatus(@Param("username") String username, @Param("status") Integer status);
}
