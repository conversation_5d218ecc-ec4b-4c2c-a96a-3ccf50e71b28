/**
 * 轮播图管理API
 */

import request from '@/utils/request'

// API路径
const API_BASE = '/carousel'

/**
 * 获取所有轮播图列表（管理端）
 */
export function getCarousels() {
  return request({
    url: `${API_BASE}/list`,
    method: 'get'
  })
}

/**
 * 获取启用的轮播图列表（前端展示）
 */
export function getEnabledCarousels() {
  return request({
    url: `${API_BASE}/enabled`,
    method: 'get'
  })
}

/**
 * 根据ID获取轮播图详情
 * @param {number} id 轮播图ID
 */
export function getCarouselById(id) {
  return request({
    url: `${API_BASE}/${id}`,
    method: 'get'
  })
}

/**
 * 创建轮播图
 * @param {object} data 轮播图数据
 */
export function createCarousel(data) {
  return request({
    url: API_BASE,
    method: 'post',
    data
  })
}

/**
 * 更新轮播图
 * @param {object} data 轮播图数据
 */
export function updateCarousel(data) {
  return request({
    url: API_BASE,
    method: 'put',
    data
  })
}

/**
 * 删除轮播图
 * @param {number} id 轮播图ID
 */
export function deleteCarousel(id) {
  return request({
    url: `${API_BASE}/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除轮播图
 * @param {array} ids 轮播图ID列表
 */
export function batchDeleteCarousels(ids) {
  return request({
    url: `${API_BASE}/batch`,
    method: 'delete',
    data: ids
  })
}

/**
 * 切换轮播图启用状态
 * @param {number} id 轮播图ID
 */
export function toggleCarouselStatus(id) {
  return request({
    url: `${API_BASE}/${id}/toggle`,
    method: 'put'
  })
}

/**
 * 批量更新轮播图排序
 * @param {array} carousels 轮播图列表（包含ID和排序序号）
 */
export function batchUpdateCarouselSort(carousels) {
  return request({
    url: `${API_BASE}/batch-sort`,
    method: 'put',
    data: carousels
  })
}
