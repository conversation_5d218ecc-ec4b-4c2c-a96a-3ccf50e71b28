<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
    class="consultation-form"
  >
    <el-form-item label="用户ID" prop="userId">
      <el-input-number
        v-model="form.userId"
        :min="1"
        placeholder="请输入用户ID"
        style="width: 100%"
      />
    </el-form-item>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="城市" prop="city">
          <el-input v-model="form.city" placeholder="请输入城市" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="form.gender">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="联系方式" prop="contactInfo">
          <el-input v-model="form.contactInfo" placeholder="请输入联系方式" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="咨询内容" prop="consultationContent">
      <el-input
        v-model="form.consultationContent"
        type="textarea"
        :rows="4"
        placeholder="请输入咨询内容"
      />
    </el-form-item>

    <el-form-item label="备注" prop="remarks">
      <el-input
        v-model="form.remarks"
        type="textarea"
        :rows="3"
        placeholder="请输入备注信息"
      />
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="submitForm">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
      <el-button @click="resetForm">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  consultationData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit'])

const formRef = ref()

// 表单数据
const form = reactive({
  id: null,
  userId: 1,
  city: '',
  name: '',
  gender: '男',
  contactInfo: '',
  consultationContent: '',
  remarks: ''
})

// 表单验证规则
const rules = {
  userId: [
    { required: true, message: '请输入用户ID', trigger: 'blur' },
    { type: 'number', min: 1, message: '用户ID必须大于0', trigger: 'blur' }
  ],
  city: [
    { required: true, message: '请输入城市', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  contactInfo: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  consultationContent: [
    { required: true, message: '请输入咨询内容', trigger: 'blur' },
    { min: 10, message: '咨询内容至少10个字符', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.consultationData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(form, newData)
  }
}, { immediate: true, deep: true })

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()
    emit('submit', { ...form })
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
  Object.assign(form, {
    id: null,
    userId: 1,
    city: '',
    name: '',
    gender: '男',
    contactInfo: '',
    consultationContent: '',
    remarks: ''
  })
}

// 暴露方法给父组件
defineExpose({
  resetForm
})
</script>

<style lang="scss" scoped>
.consultation-form {
  // 样式可以根据需要添加
}
</style>
