package com.example.cos.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户角色更新DTO
 */
@ApiModel(description = "用户角色更新请求")
public class UserRoleUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", example = "1", required = true)
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    @ApiModelProperty(value = "用户角色", example = "1", notes = "0=普通用户，1=有权限用户", required = true)
    @NotNull(message = "用户角色不能为空")
    private Integer role;

    public UserRoleUpdateDTO() {}

    public UserRoleUpdateDTO(Integer userId, Integer role) {
        this.userId = userId;
        this.role = role;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    @Override
    public String toString() {
        return "UserRoleUpdateDTO{" +
                "userId=" + userId +
                ", role=" + role +
                '}';
    }
}
