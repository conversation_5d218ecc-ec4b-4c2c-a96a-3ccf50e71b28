package com.example.cos.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 房屋资源实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@ApiModel(description = "房屋资源信息")
@TableName(value = "house_resource")
public class HouseResource implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "房源唯一ID", example = "1")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "拍卖状态（0-未开拍，1-一拍中，2-二拍中，3-变卖中，4-已结束）", example = "1")
    @TableField("auction_status")
    @Min(value = 0, message = "拍卖状态值不能小于0")
    @Max(value = 4, message = "拍卖状态值不能大于4")
    private Integer auctionStatus;

    @ApiModelProperty(value = "房源标题", example = "阳光花园三室两厅精装修")
    @TableField("title")
    @NotBlank(message = "房源标题不能为空")
    @Size(max = 255, message = "房源标题长度不能超过255个字符")
    private String title;

    @ApiModelProperty(value = "起拍价（元）", example = "1200000.00")
    @TableField("starting_price")
    @DecimalMin(value = "0.01", message = "起拍价必须大于0")
    private BigDecimal startingPrice;

    @ApiModelProperty(value = "评估价（元）", example = "1500000.00")
    @TableField("evaluation_price")
    @DecimalMin(value = "0.01", message = "评估价必须大于0")
    private BigDecimal evaluationPrice;

    @ApiModelProperty(value = "起拍时间", example = "2025-01-15 10:00:00")
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间", example = "2025-01-22 10:00:00")
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "竞价周期（天）", example = "7", notes = "自动计算字段")
    @TableField(value = "auction_cycle", updateStrategy = FieldStrategy.NEVER)
    private Integer auctionCycle;

    @ApiModelProperty(value = "户型", example = "3室2厅1卫")
    @TableField("house_type")
    @Size(max = 50, message = "户型长度不能超过50个字符")
    private String houseType;

    @ApiModelProperty(value = "建筑面积（㎡）", example = "120.50")
    @TableField("building_area")
    @DecimalMin(value = "0.01", message = "建筑面积必须大于0")
    private BigDecimal buildingArea;

    @ApiModelProperty(value = "小区名称", example = "阳光花园")
    @TableField("community_name")
    @Size(max = 100, message = "小区名称长度不能超过100个字符")
    private String communityName;

    @ApiModelProperty(value = "梯部类型（0-楼梯，1-电梯）", example = "1")
    @TableField("stairs_type")
    @Min(value = 0, message = "梯部类型值不能小于0")
    @Max(value = 1, message = "梯部类型值不能大于1")
    private Integer stairsType;

    @ApiModelProperty(value = "物业类型（0-低层，1-中层，2-高层）", example = "2")
    @TableField("property_type")
    @Min(value = 0, message = "物业类型值不能小于0")
    @Max(value = 2, message = "物业类型值不能大于2")
    private Integer propertyType;

    @ApiModelProperty(value = "保证金（元）", example = "120000.00")
    @TableField("deposit")
    @DecimalMin(value = "0.01", message = "保证金必须大于0")
    private BigDecimal deposit;

    @ApiModelProperty(value = "建筑年份", example = "2010")
    @TableField("construction_year")
    @Min(value = 1900, message = "建筑年份不能小于1900")
    @Max(value = 2100, message = "建筑年份不能大于2100")
    private Integer constructionYear;

    @ApiModelProperty(value = "楼层", example = "15/30")
    @TableField("floor")
    @Size(max = 20, message = "楼层长度不能超过20个字符")
    private String floor;

    @ApiModelProperty(value = "拍卖次数", example = "1")
    @TableField("auction_times")
    @Min(value = 1, message = "拍卖次数不能小于1")
    private Integer auctionTimes;

    @ApiModelProperty(value = "加价幅度（元）", example = "10000.00")
    @TableField("price_increment")
    @DecimalMin(value = "0.01", message = "加价幅度必须大于0")
    private BigDecimal priceIncrement;

    @ApiModelProperty(value = "装修情况（0-毛坯，1-简装，2-精装）", example = "2")
    @TableField("decoration")
    @Min(value = 0, message = "装修情况值不能小于0")
    @Max(value = 2, message = "装修情况值不能大于2")
    private Integer decoration;

    @ApiModelProperty(value = "是否精选（0-否，1-是）", example = "1")
    @TableField("is_selected")
    @Min(value = 0, message = "是否精选值不能小于0")
    @Max(value = 1, message = "是否精选值不能大于1")
    private Integer isSelected;

    @ApiModelProperty(value = "是否特殊房屋（0-否，1-是）", example = "0")
    @TableField("is_special")
    @Min(value = 0, message = "是否特殊房屋值不能小于0")
    @Max(value = 1, message = "是否特殊房屋值不能大于1")
    private Integer isSpecial;

    @ApiModelProperty(value = "原链接", example = "https://example.com/house/123")
    @TableField("original_url")
    @NotBlank(message = "原链接不能为空")
    @Size(max = 512, message = "原链接长度不能超过512个字符")
    private String originalUrl;

    @ApiModelProperty(value = "房屋类型（0-住宅，1-商办）", example = "0")
    @TableField("house_category")
    @Min(value = 0, message = "房屋类型值不能小于0")
    @Max(value = 1, message = "房屋类型值不能大于1")
    private Integer houseCategory;

    @ApiModelProperty(value = "房屋图链接（逗号分隔多个URL）", example = "https://img1.jpg,https://img2.jpg")
    @TableField("image_urls")
    private String imageUrls;

    @ApiModelProperty(value = "经度", example = "116.397128")
    @TableField("longitude")
    @DecimalMin(value = "-180.0", message = "经度值不能小于-180")
    @DecimalMax(value = "180.0", message = "经度值不能大于180")
    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度", example = "39.916527")
    @TableField("latitude")
    @DecimalMin(value = "-90.0", message = "纬度值不能小于-90")
    @DecimalMax(value = "90.0", message = "纬度值不能大于90")
    private BigDecimal latitude;

    @ApiModelProperty(value = "房屋标签（逗号分隔）", example = "学区房,地铁房,精装修")
    @TableField("tags")
    @Size(max = 255, message = "房屋标签长度不能超过255个字符")
    private String tags;

    @ApiModelProperty(value = "折扣率（%）", example = "80.00", notes = "自动计算字段")
    @TableField(value = "discount_rate", updateStrategy = FieldStrategy.NEVER)
    private BigDecimal discountRate;

    @ApiModelProperty(value = "捡漏空间（元）", example = "300000.00", notes = "自动计算字段")
    @TableField(value = "bargain_space", updateStrategy = FieldStrategy.NEVER)
    private BigDecimal bargainSpace;

    @ApiModelProperty(value = "市场单价（元/㎡）", example = "12500.00", notes = "自动计算字段")
    @TableField(value = "market_unit_price", updateStrategy = FieldStrategy.NEVER)
    private BigDecimal marketUnitPrice;

    @ApiModelProperty(value = "起拍单价（元/㎡）", example = "10000.00", notes = "自动计算字段")
    @TableField(value = "starting_unit_price", updateStrategy = FieldStrategy.NEVER)
    private BigDecimal startingUnitPrice;

    @ApiModelProperty(value = "创建时间", example = "2025-01-15 10:00:00")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    public HouseResource() {}

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getAuctionStatus() {
        return auctionStatus;
    }

    public void setAuctionStatus(Integer auctionStatus) {
        this.auctionStatus = auctionStatus;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public BigDecimal getStartingPrice() {
        return startingPrice;
    }

    public void setStartingPrice(BigDecimal startingPrice) {
        this.startingPrice = startingPrice;
    }

    public BigDecimal getEvaluationPrice() {
        return evaluationPrice;
    }

    public void setEvaluationPrice(BigDecimal evaluationPrice) {
        this.evaluationPrice = evaluationPrice;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Integer getAuctionCycle() {
        return auctionCycle;
    }

    public void setAuctionCycle(Integer auctionCycle) {
        this.auctionCycle = auctionCycle;
    }

    public String getHouseType() {
        return houseType;
    }

    public void setHouseType(String houseType) {
        this.houseType = houseType;
    }

    public BigDecimal getBuildingArea() {
        return buildingArea;
    }

    public void setBuildingArea(BigDecimal buildingArea) {
        this.buildingArea = buildingArea;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public Integer getStairsType() {
        return stairsType;
    }

    public void setStairsType(Integer stairsType) {
        this.stairsType = stairsType;
    }

    public Integer getPropertyType() {
        return propertyType;
    }

    public void setPropertyType(Integer propertyType) {
        this.propertyType = propertyType;
    }

    public BigDecimal getDeposit() {
        return deposit;
    }

    public void setDeposit(BigDecimal deposit) {
        this.deposit = deposit;
    }

    public Integer getConstructionYear() {
        return constructionYear;
    }

    public void setConstructionYear(Integer constructionYear) {
        this.constructionYear = constructionYear;
    }

    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }

    public Integer getAuctionTimes() {
        return auctionTimes;
    }

    public void setAuctionTimes(Integer auctionTimes) {
        this.auctionTimes = auctionTimes;
    }

    public BigDecimal getPriceIncrement() {
        return priceIncrement;
    }

    public void setPriceIncrement(BigDecimal priceIncrement) {
        this.priceIncrement = priceIncrement;
    }

    public Integer getDecoration() {
        return decoration;
    }

    public void setDecoration(Integer decoration) {
        this.decoration = decoration;
    }

    public Integer getIsSelected() {
        return isSelected;
    }

    public void setIsSelected(Integer isSelected) {
        this.isSelected = isSelected;
    }

    public Integer getIsSpecial() {
        return isSpecial;
    }

    public void setIsSpecial(Integer isSpecial) {
        this.isSpecial = isSpecial;
    }

    public String getOriginalUrl() {
        return originalUrl;
    }

    public void setOriginalUrl(String originalUrl) {
        this.originalUrl = originalUrl;
    }

    public Integer getHouseCategory() {
        return houseCategory;
    }

    public void setHouseCategory(Integer houseCategory) {
        this.houseCategory = houseCategory;
    }

    public String getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(String imageUrls) {
        this.imageUrls = imageUrls;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public BigDecimal getBargainSpace() {
        return bargainSpace;
    }

    public void setBargainSpace(BigDecimal bargainSpace) {
        this.bargainSpace = bargainSpace;
    }

    public BigDecimal getMarketUnitPrice() {
        return marketUnitPrice;
    }

    public void setMarketUnitPrice(BigDecimal marketUnitPrice) {
        this.marketUnitPrice = marketUnitPrice;
    }

    public BigDecimal getStartingUnitPrice() {
        return startingUnitPrice;
    }

    public void setStartingUnitPrice(BigDecimal startingUnitPrice) {
        this.startingUnitPrice = startingUnitPrice;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "HouseResource{" +
                "id=" + id +
                ", auctionStatus=" + auctionStatus +
                ", title='" + title + '\'' +
                ", startingPrice=" + startingPrice +
                ", evaluationPrice=" + evaluationPrice +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", auctionCycle=" + auctionCycle +
                ", houseType='" + houseType + '\'' +
                ", buildingArea=" + buildingArea +
                ", communityName='" + communityName + '\'' +
                ", stairsType=" + stairsType +
                ", propertyType=" + propertyType +
                ", deposit=" + deposit +
                ", constructionYear=" + constructionYear +
                ", floor='" + floor + '\'' +
                ", auctionTimes=" + auctionTimes +
                ", priceIncrement=" + priceIncrement +
                ", decoration=" + decoration +
                ", isSelected=" + isSelected +
                ", isSpecial=" + isSpecial +
                ", originalUrl='" + originalUrl + '\'' +
                ", houseCategory=" + houseCategory +
                ", imageUrls='" + imageUrls + '\'' +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", tags='" + tags + '\'' +
                ", discountRate=" + discountRate +
                ", bargainSpace=" + bargainSpace +
                ", marketUnitPrice=" + marketUnitPrice +
                ", startingUnitPrice=" + startingUnitPrice +
                ", createTime=" + createTime +
                '}';
    }
}