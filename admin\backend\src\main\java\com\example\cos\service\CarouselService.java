package com.example.cos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.cos.dto.CarouselCreateDTO;
import com.example.cos.dto.CarouselUpdateDTO;
import com.example.cos.entity.Carousel;

import java.util.List;

/**
 * 轮播图服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CarouselService extends IService<Carousel> {

    /**
     * 创建轮播图
     * @param createDTO 创建请求DTO
     * @return 创建的轮播图
     */
    Carousel createCarousel(CarouselCreateDTO createDTO);

    /**
     * 更新轮播图
     * @param updateDTO 更新请求DTO
     * @return 更新的轮播图
     */
    Carousel updateCarousel(CarouselUpdateDTO updateDTO);

    /**
     * 删除轮播图
     * @param id 轮播图ID
     * @return 是否删除成功
     */
    boolean deleteCarousel(Long id);

    /**
     * 批量删除轮播图
     * @param ids 轮播图ID列表
     * @return 是否删除成功
     */
    boolean batchDeleteCarousels(List<Long> ids);

    /**
     * 获取启用的轮播图列表（按排序序号升序）
     * @return 启用的轮播图列表
     */
    List<Carousel> getEnabledCarousels();

    /**
     * 获取所有轮播图列表（按排序序号升序）
     * @return 所有轮播图列表
     */
    List<Carousel> getAllCarouselsOrderBySort();

    /**
     * 切换轮播图启用状态
     * @param id 轮播图ID
     * @return 切换后的轮播图
     */
    Carousel toggleCarouselStatus(Long id);

    /**
     * 批量更新轮播图排序
     * @param carousels 轮播图列表（包含ID和新的排序序号）
     * @return 是否更新成功
     */
    boolean batchUpdateSort(List<Carousel> carousels);
}
