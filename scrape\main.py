# -*- coding: utf-8 -*-
"""
主程序 - GUI界面和应用程序控制
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import queue

from datetime import datetime

from config import Config
from scraper import HouseScraper
from jd_scraper import JDScraper
from ali_asset_scraper import AliAssetScraper
from api_client import APIClient

class ScraperGUI:
    """爬虫GUI应用程序"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.scraper = None
        self.scraping_thread = None
        self.log_queue = queue.Queue()
        self.is_scraping = False
        
        # 统计数据
        self.stats = {
            'total': 0,
            'success': 0,
            'error': 0,
            'current': 0
        }
        
        self.setup_gui()
        self.setup_callbacks()
        
        # 启动日志处理
        self.process_log_queue()
    
    def setup_gui(self):
        """设置GUI界面"""
        # 主窗口配置
        self.root.title(Config.GUI_CONFIG['window_title'])
        self.root.geometry(Config.GUI_CONFIG['window_size'])
        self.root.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 创建控制面板
        self.create_control_panel(main_frame)
        
        # 创建状态面板
        self.create_status_panel(main_frame)
        
        # 创建日志面板
        self.create_log_panel(main_frame)
        
        # 创建设置面板
        self.create_settings_panel(main_frame)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="10")
        control_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 全局设置区域
        settings_frame = ttk.Frame(control_frame)
        settings_frame.pack(fill=tk.X, pady=(0, 10))

        # 平台选择
        ttk.Label(settings_frame, text="拍卖平台:").pack(side=tk.LEFT)
        self.platform_var = tk.StringVar(value="阿里资产")
        self.platform_combo = ttk.Combobox(
            settings_frame,
            textvariable=self.platform_var,
            values=["阿里资产", "阿里拍卖", "京东拍卖", "中拍平台", "公拍网", "北京产权交易所", "融e购", "人民法院诉讼资产网"],
            state="readonly",
            width=15
        )
        self.platform_combo.pack(side=tk.LEFT, padx=(5, 15))
        self.platform_combo.bind("<<ComboboxSelected>>", self.on_platform_change)

        # 住宅页面数量设置
        ttk.Label(settings_frame, text="住宅页数:").pack(side=tk.LEFT)
        self.residential_pages_entry = ttk.Entry(settings_frame, width=5)
        self.residential_pages_entry.pack(side=tk.LEFT, padx=(5, 10))
        self.residential_pages_entry.insert(0, "1")  # 默认值1页

        # 商办页面数量设置（仅阿里拍卖显示）
        self.commercial_label = ttk.Label(settings_frame, text="商办页数:")
        self.commercial_label.pack(side=tk.LEFT, padx=(15, 0))
        self.commercial_pages_entry = ttk.Entry(settings_frame, width=5)
        self.commercial_pages_entry.pack(side=tk.LEFT, padx=(5, 10))
        self.commercial_pages_entry.insert(0, "1")  # 默认值1页

        # 添加说明标签
        ttk.Label(settings_frame, text="(0=跳过)", font=("Arial", 8)).pack(side=tk.LEFT, padx=(5, 0))

        # 按钮框架
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)
        
        # 开始按钮
        self.start_button = ttk.Button(
            button_frame, 
            text="开始爬取", 
            command=self.start_scraping,
            style="Accent.TButton"
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 暂停按钮
        self.pause_button = ttk.Button(
            button_frame, 
            text="暂停", 
            command=self.pause_scraping,
            state=tk.DISABLED
        )
        self.pause_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 停止按钮
        self.stop_button = ttk.Button(
            button_frame, 
            text="停止", 
            command=self.stop_scraping,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 阿里资产登录按钮
        self.ali_login_button = ttk.Button(
            button_frame,
            text="阿里资产登录",
            command=self.ali_asset_login
        )
        self.ali_login_button.pack(side=tk.LEFT, padx=(0, 5))

        # 清空日志按钮
        self.clear_log_button = ttk.Button(
            button_frame,
            text="清空日志",
            command=self.clear_log
        )
        self.clear_log_button.pack(side=tk.RIGHT)
    
    def create_status_panel(self, parent):
        """创建状态面板"""
        status_frame = ttk.LabelFrame(parent, text="状态信息", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 进度条
        progress_frame = ttk.Frame(status_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(progress_frame, text="进度:").pack(side=tk.LEFT)
        self.progress_var = tk.StringVar(value="就绪")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.pack(side=tk.LEFT, padx=(10, 0))
        
        self.progress_bar = ttk.Progressbar(
            progress_frame, 
            mode='determinate',
            length=300
        )
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 统计信息
        stats_frame = ttk.Frame(status_frame)
        stats_frame.pack(fill=tk.X)
        
        # 总数
        ttk.Label(stats_frame, text="总计:").grid(row=0, column=0, sticky=tk.W)
        self.total_var = tk.StringVar(value="0")
        ttk.Label(stats_frame, textvariable=self.total_var).grid(row=0, column=1, sticky=tk.W, padx=(5, 20))
        
        # 成功数
        ttk.Label(stats_frame, text="成功:").grid(row=0, column=2, sticky=tk.W)
        self.success_var = tk.StringVar(value="0")
        ttk.Label(stats_frame, textvariable=self.success_var, foreground="green").grid(row=0, column=3, sticky=tk.W, padx=(5, 20))
        
        # 失败数
        ttk.Label(stats_frame, text="失败:").grid(row=0, column=4, sticky=tk.W)
        self.error_var = tk.StringVar(value="0")
        ttk.Label(stats_frame, textvariable=self.error_var, foreground="red").grid(row=0, column=5, sticky=tk.W, padx=(5, 20))
        
        # 状态
        ttk.Label(stats_frame, text="状态:").grid(row=0, column=6, sticky=tk.W)
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(stats_frame, textvariable=self.status_var)
        self.status_label.grid(row=0, column=7, sticky=tk.W, padx=(5, 0))
    
    def create_log_panel(self, parent):
        """创建日志面板"""
        log_frame = ttk.LabelFrame(parent, text="运行日志", padding="10")
        log_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=15,
            wrap=tk.WORD,
            font=("Consolas", 9)
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 添加初始日志
        self.add_log("应用程序已启动")
        self.add_log("目标网站: " + str(Config.TARGET_URL))
        self.add_log("API地址: " + str(Config.API_BASE_URL) + str(Config.API_ENDPOINT))
    
    def create_settings_panel(self, parent):
        """创建设置面板"""
        settings_frame = ttk.LabelFrame(parent, text="设置", padding="10")
        settings_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E))

        # 目标网站显示
        website_frame = ttk.Frame(settings_frame)
        website_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(website_frame, text="目标网站:").pack(side=tk.LEFT)
        ttk.Label(website_frame, text="https://z.taobao.com/", foreground="blue").pack(side=tk.LEFT, padx=(10, 0))

        # 延迟设置
        delay_frame = ttk.Frame(settings_frame)
        delay_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(delay_frame, text="延迟范围 (秒):").pack(side=tk.LEFT)

        self.min_delay_var = tk.StringVar(value=str(Config.ANTI_SCRAPING['min_delay']))
        ttk.Entry(delay_frame, textvariable=self.min_delay_var, width=5).pack(side=tk.LEFT, padx=(10, 5))

        ttk.Label(delay_frame, text="到").pack(side=tk.LEFT)

        self.max_delay_var = tk.StringVar(value=str(Config.ANTI_SCRAPING['max_delay']))
        ttk.Entry(delay_frame, textvariable=self.max_delay_var, width=5).pack(side=tk.LEFT, padx=(5, 10))

        # 重试设置
        retry_frame = ttk.Frame(settings_frame)
        retry_frame.pack(fill=tk.X)

        ttk.Label(retry_frame, text="最大重试次数:").pack(side=tk.LEFT)

        self.max_retries_var = tk.StringVar(value=str(Config.ANTI_SCRAPING['max_retries']))
        ttk.Entry(retry_frame, textvariable=self.max_retries_var, width=5).pack(side=tk.LEFT, padx=(10, 0))

    def setup_callbacks(self):
        """设置回调函数"""
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def on_platform_change(self, event=None):
        """平台切换回调函数"""
        selected_platform = self.platform_var.get()

        if selected_platform in ["阿里资产", "京东拍卖", "中拍平台", "公拍网", "北京产权交易所", "融e购", "人民法院诉讼资产网"]:
            # 阿里资产和其他非阿里拍卖平台：隐藏商办页数设置（暂时只支持住宅）
            self.commercial_label.pack_forget()
            self.commercial_pages_entry.pack_forget()
            # 设置商办页数为1（虽然隐藏但保持默认值）
            self.commercial_pages_entry.delete(0, tk.END)
            self.commercial_pages_entry.insert(0, "1")
        else:
            # 阿里拍卖：显示商办页数设置
            self.commercial_label.pack(side=tk.LEFT, padx=(15, 0))
            self.commercial_pages_entry.pack(side=tk.LEFT, padx=(5, 10))



    def progress_callback(self, current, total, status=""):
        """进度回调函数"""
        self.stats['current'] = current

        # 更新进度条
        if total > 0:
            progress_percent = (current / total) * 100
            self.progress_bar['value'] = progress_percent

        # 更新进度文本
        progress_text = str(current) + "/" + str(total)
        if status:
            progress_text += " - " + status

        self.progress_var.set(progress_text)

    def log_callback(self, message):
        """日志回调函数"""
        self.log_queue.put(message)

    def add_log(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = "[" + timestamp + "] " + message + "\n"

        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)

        # 限制日志行数
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > Config.GUI_CONFIG['log_max_lines']:
            self.log_text.delete('1.0', '2.0')

    def process_log_queue(self):
        """处理日志队列"""
        try:
            while True:
                message = self.log_queue.get_nowait()
                self.add_log(message)
        except queue.Empty:
            pass

        # 每100ms检查一次
        self.root.after(100, self.process_log_queue)

    def update_stats(self, stats):
        """更新统计信息"""
        self.stats.update(stats)
        self.total_var.set(str(self.stats['total']))
        self.success_var.set(str(self.stats['success']))
        self.error_var.set(str(self.stats['error']))

    def update_ui_state(self, is_scraping, is_paused=False):
        """更新UI状态"""
        self.is_scraping = is_scraping

        if is_scraping:
            # 爬取中：禁用开始和登录按钮，启用停止和暂停按钮
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.ali_login_button.config(state=tk.DISABLED)

            # 处理暂停按钮（如果存在）
            if hasattr(self, 'pause_button'):
                self.pause_button.config(state=tk.NORMAL)
                if is_paused:
                    self.pause_button.config(text="恢复")
                    self.status_var.set("已暂停")
                else:
                    self.pause_button.config(text="暂停")
                    self.status_var.set("运行中")
            else:
                self.status_var.set("运行中")
        else:
            # 未爬取：启用开始和登录按钮，禁用停止按钮
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.ali_login_button.config(state=tk.NORMAL)

            # 处理暂停按钮（如果存在）
            if hasattr(self, 'pause_button'):
                self.pause_button.config(state=tk.DISABLED, text="暂停")

            self.status_var.set("就绪")
            self.progress_var.set("就绪")
            self.progress_bar['value'] = 0

    def start_scraping(self):
        """开始爬取"""
        if self.is_scraping:
            return

        try:
            # 获取平台选择
            selected_platform = self.platform_var.get()

            # 获取页面数量设置
            try:
                residential_pages = int(self.residential_pages_entry.get().strip())
                commercial_pages = int(self.commercial_pages_entry.get().strip())

                if residential_pages < 0 or commercial_pages < 0:
                    messagebox.showwarning("警告", "页面数量不能小于0")
                    return
                if residential_pages > 50 or commercial_pages > 50:
                    messagebox.showwarning("警告", "页面数量不能超过50页")
                    return

                # 根据平台调整验证逻辑
                if selected_platform in ["阿里资产", "京东拍卖", "中拍平台"]:
                    if residential_pages <= 0:
                        messagebox.showwarning("警告", selected_platform + "住宅页面数量必须大于0")
                        return
                else:
                    if residential_pages <= 0 and commercial_pages <= 0:
                        messagebox.showwarning("警告", "住宅和商办页面数量不能同时为0或负数")
                        return

            except ValueError:
                messagebox.showwarning("警告", "请输入有效的页面数量（数字）")
                return

            # 更新配置
            self.update_config_from_ui()

            # 根据平台创建对应的爬虫实例
            if selected_platform == "阿里资产":
                # 创建API客户端
                api_client = APIClient()
                self.scraper = AliAssetScraper(api_client)
                self.scraper.max_pages = residential_pages  # 阿里资产只有住宅页数
                platform_name = "阿里资产"
            elif selected_platform == "京东拍卖":
                self.scraper = JDScraper(
                    progress_callback=self.progress_callback,
                    log_callback=self.log_callback
                )
                self.scraper.max_pages = residential_pages  # 京东拍卖只有住宅页数
                platform_name = "京东拍卖"
            elif selected_platform in ["中拍平台", "公拍网", "北京产权交易所", "融e购", "人民法院诉讼资产网"]:
                # 这些平台暂未实现，显示提示信息
                platform_names = {
                    "中拍平台": "中拍平台",
                    "公拍网": "公拍网",
                    "北京产权交易所": "北京产权交易所",
                    "融e购": "融e购",
                    "人民法院诉讼资产网": "人民法院诉讼资产网"
                }
                messagebox.showinfo("提示", platform_names[selected_platform] + "爬虫正在开发中，敬请期待！")
                return
            else:
                # 阿里拍卖（原有的）
                self.scraper = HouseScraper(
                    progress_callback=self.progress_callback,
                    log_callback=self.log_callback
                )
                # 设置目标网址
                self.scraper.target_url = "https://z.taobao.com/"
                self.scraper.scrape_mode = "auto"  # 固定为自动模式
                self.scraper.residential_pages = residential_pages  # 设置住宅页面数量
                self.scraper.commercial_pages = commercial_pages  # 设置商办页面数量
                platform_name = "阿里拍卖"

            # 重置统计
            self.stats = {'total': 0, 'success': 0, 'error': 0, 'current': 0}
            self.update_stats(self.stats)

            # 更新UI状态
            self.update_ui_state(True)

            # 启动爬取线程
            self.scraping_thread = threading.Thread(target=self.run_scraping, daemon=True)
            self.scraping_thread.start()

            self.add_log("开始爬取" + platform_name + "房源信息...")

        except Exception as e:
            self.add_log("启动爬取失败: " + str(e))
            messagebox.showerror("错误", "启动爬取失败: " + str(e))
            self.update_ui_state(False)

    def run_scraping(self):
        """运行爬取任务"""
        try:
            # 检查是否是阿里资产爬虫
            if isinstance(self.scraper, AliAssetScraper):
                # 阿里资产爬虫使用不同的方法
                result = self.scraper.scrape_houses(
                    max_pages=self.scraper.max_pages,
                    max_houses_per_page=20
                )

                # 转换结果格式以兼容UI
                converted_result = {
                    'total': result['total_processed'],
                    'success': result['success_count'],
                    'error': result['failed_count']
                }

                self.update_stats(converted_result)
                self.add_log("爬取任务完成! 总计: " + str(converted_result['total']) + ", 成功: " + str(converted_result['success']) + ", 失败: " + str(converted_result['error']))
            else:
                # 其他爬虫使用原有方法
                result = self.scraper.start_scraping()

                if result:
                    self.update_stats(result)
                    self.add_log("爬取任务完成! 总计: " + str(result['total']) + ", 成功: " + str(result['success']) + ", 失败: " + str(result['error']))
                else:
                    self.add_log("爬取任务被中断")

        except Exception as e:
            self.add_log("爬取任务异常: " + str(e))
        finally:
            # 更新UI状态
            self.root.after(0, lambda: self.update_ui_state(False))

    def pause_scraping(self):
        """暂停/恢复爬取"""
        if not self.scraper:
            return

        if self.scraper.is_paused:
            self.scraper.resume_scraping()
            self.update_ui_state(True, False)
        else:
            self.scraper.pause_scraping()
            self.update_ui_state(True, True)

    def stop_scraping(self):
        """停止爬取"""
        if self.scraper:
            self.scraper.stop_scraping()
            self.add_log("正在停止爬取任务...")

        self.update_ui_state(False)



    def ali_asset_login(self):
        """阿里资产登录"""
        if self.is_scraping:
            messagebox.showwarning("警告", "爬取进行中，无法进行登录操作")
            return

        # 禁用登录按钮
        self.ali_login_button.config(state=tk.DISABLED)
        self.ali_login_button.config(text="登录中...")

        # 在新线程中执行登录
        def login_thread():
            try:
                self.add_log("开始阿里资产登录流程...")

                # 创建API客户端和爬虫实例
                api_client = APIClient()
                scraper = AliAssetScraper(api_client)

                # 初始化浏览器（不自动登录）
                if scraper.setup_driver(auto_login=False):
                    self.add_log("浏览器初始化成功")

                    # 尝试使用cookies自动登录
                    if scraper.login_with_cookies():
                        self.add_log("✅ 使用保存的cookies登录成功")
                        self.root.after(0, lambda: messagebox.showinfo("成功", "阿里资产登录成功！\n已保存登录状态，下次可直接使用。"))

                        # 登录成功后关闭浏览器
                        try:
                            scraper.close()
                        except:
                            pass
                    else:
                        # 需要手动登录，打开登录页面
                        self.add_log("需要手动登录，正在打开淘宝登录页面...")

                        if scraper.open_login_page():
                            self.add_log("✅ 登录页面已打开")

                            # 显示登录提示对话框
                            def show_login_dialog():
                                result = messagebox.askyesno(
                                    "淘宝登录",
                                    "淘宝登录页面已在浏览器中打开。\n\n"
                                    "请在浏览器中：\n"
                                    "1. 完成淘宝账号登录\n"
                                    "2. 登录成功后会自动跳转到阿里资产页面\n"
                                    "3. 确认跳转完成后点击'是'\n"
                                    "4. 如需取消请点击'否'\n\n"
                                    "是否已完成登录并跳转？"
                                )

                                if result:
                                    # 用户确认已登录，验证登录状态并保存cookies
                                    if scraper.verify_login_and_save():
                                        self.add_log("✅ 登录验证成功，cookies已保存")
                                        messagebox.showinfo("成功", "阿里资产登录成功！\n登录状态已保存，下次可直接使用。")
                                    else:
                                        self.add_log("❌ 登录验证失败，请重试")
                                        messagebox.showerror("失败", "登录验证失败，请确保已完成登录并跳转到阿里资产页面。")
                                else:
                                    self.add_log("用户取消登录")

                                # 无论成功还是失败，都关闭浏览器
                                try:
                                    scraper.close()
                                except:
                                    pass

                            # 在主线程中显示对话框
                            self.root.after(0, show_login_dialog)
                        else:
                            self.add_log("❌ 打开登录页面失败")
                            self.root.after(0, lambda: messagebox.showerror("错误", "无法打开登录页面，请重试。"))
                            try:
                                scraper.close()
                            except:
                                pass

                else:
                    self.add_log("❌ 浏览器初始化失败")
                    self.root.after(0, lambda: messagebox.showerror("错误", "浏览器初始化失败，请检查Chrome浏览器是否正确安装。"))

            except Exception as e:
                error_msg = "阿里资产登录失败: " + str(e)
                self.add_log(error_msg)
                self.root.after(0, lambda: messagebox.showerror("错误", error_msg))

            finally:
                # 恢复按钮状态
                self.root.after(0, lambda: (
                    self.ali_login_button.config(state=tk.NORMAL),
                    self.ali_login_button.config(text="阿里资产登录")
                ))

        # 启动线程
        thread = threading.Thread(target=login_thread, daemon=True)
        thread.start()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete('1.0', tk.END)
        self.add_log("日志已清空")





    def update_config_from_ui(self):
        """从UI更新配置"""
        try:
            Config.ANTI_SCRAPING['min_delay'] = float(self.min_delay_var.get())
            Config.ANTI_SCRAPING['max_delay'] = float(self.max_delay_var.get())
            Config.ANTI_SCRAPING['max_retries'] = int(self.max_retries_var.get())
        except ValueError as e:
            self.add_log("配置更新失败: " + str(e))

    def on_closing(self):
        """窗口关闭事件"""
        if self.is_scraping:
            if messagebox.askokcancel("确认", "爬取任务正在运行，确定要退出吗？"):
                if self.scraper:
                    # 保存认证数据（如果是京东爬虫）
                    if hasattr(self.scraper, 'save_cookies_and_tokens'):
                        self.add_log("程序退出前保存认证数据...")
                        self.scraper.save_cookies_and_tokens()

                    self.scraper.stop_scraping()
                self.root.destroy()
        else:
            # 即使没有正在运行的任务，也检查是否需要保存认证数据
            if self.scraper and hasattr(self.scraper, 'save_cookies_and_tokens'):
                self.add_log("程序退出前保存认证数据...")
                self.scraper.save_cookies_and_tokens()

            self.root.destroy()

    def run(self):
        """运行应用程序"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = ScraperGUI()
        app.run()
    except Exception as e:
        print("应用程序启动失败: " + str(e))
        messagebox.showerror("错误", "应用程序启动失败: " + str(e))

if __name__ == "__main__":
    main()
