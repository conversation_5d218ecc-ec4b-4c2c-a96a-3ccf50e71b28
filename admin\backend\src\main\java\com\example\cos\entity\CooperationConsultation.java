package com.example.cos.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 合作咨询实体类
 */
@ApiModel(description = "合作咨询信息")
@TableName("cooperation_consultation_table")
public class CooperationConsultation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "咨询ID", example = "1")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户ID", example = "1", required = true)
    @TableField("user_id")
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    @ApiModelProperty(value = "城市", example = "北京")
    @TableField("city")
    private String city;

    @ApiModelProperty(value = "姓名", example = "张三")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "性别", example = "男")
    @TableField("gender")
    private String gender;

    @ApiModelProperty(value = "联系方式", example = "13800138000")
    @TableField("contact_info")
    private String contactInfo;

    @ApiModelProperty(value = "咨询内容", example = "希望了解房产投资相关信息")
    @TableField("consultation_content")
    private String consultationContent;

    @ApiModelProperty(value = "备注", example = "客户比较关注学区房")
    @TableField("remarks")
    private String remarks;

    public CooperationConsultation() {}

    // Getter and Setter methods
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public Integer getUserId() { return userId; }
    public void setUserId(Integer userId) { this.userId = userId; }
    public String getCity() { return city; }
    public void setCity(String city) { this.city = city; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getGender() { return gender; }
    public void setGender(String gender) { this.gender = gender; }
    public String getContactInfo() { return contactInfo; }
    public void setContactInfo(String contactInfo) { this.contactInfo = contactInfo; }
    public String getConsultationContent() { return consultationContent; }
    public void setConsultationContent(String consultationContent) { this.consultationContent = consultationContent; }
    public String getRemarks() { return remarks; }
    public void setRemarks(String remarks) { this.remarks = remarks; }
}
