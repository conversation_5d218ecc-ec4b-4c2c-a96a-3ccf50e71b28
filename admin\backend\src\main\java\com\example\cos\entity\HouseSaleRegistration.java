package com.example.cos.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 房源出售登记实体类
 */
@ApiModel(description = "房源出售登记信息")
@TableName("house_sale_registration_table")
public class HouseSaleRegistration implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "登记ID", example = "1")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户ID", example = "1", required = true)
    @TableField("user_id")
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    @ApiModelProperty(value = "房屋类型", example = "住宅")
    @TableField("house_type")
    private String houseType;

    @ApiModelProperty(value = "城市", example = "北京")
    @TableField("city")
    private String city;

    @ApiModelProperty(value = "小区名称", example = "阳光花园")
    @TableField("community_name")
    private String communityName;

    @ApiModelProperty(value = "房屋面积", example = "120.50")
    @TableField("housing_area")
    private BigDecimal housingArea;

    @ApiModelProperty(value = "期望价格", example = "1500000.00")
    @TableField("expected_price")
    private BigDecimal expectedPrice;

    @ApiModelProperty(value = "联系人", example = "张三")
    @TableField("contact_person")
    private String contactPerson;

    @ApiModelProperty(value = "性别", example = "男")
    @TableField("gender")
    private String gender;

    @ApiModelProperty(value = "联系方式", example = "13800138000")
    @TableField("contact_info")
    private String contactInfo;

    @ApiModelProperty(value = "备注", example = "房屋精装修，南北通透")
    @TableField("remarks")
    private String remarks;

    public HouseSaleRegistration() {}

    // Getter and Setter methods
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public Integer getUserId() { return userId; }
    public void setUserId(Integer userId) { this.userId = userId; }
    public String getHouseType() { return houseType; }
    public void setHouseType(String houseType) { this.houseType = houseType; }
    public String getCity() { return city; }
    public void setCity(String city) { this.city = city; }
    public String getCommunityName() { return communityName; }
    public void setCommunityName(String communityName) { this.communityName = communityName; }
    public BigDecimal getHousingArea() { return housingArea; }
    public void setHousingArea(BigDecimal housingArea) { this.housingArea = housingArea; }
    public BigDecimal getExpectedPrice() { return expectedPrice; }
    public void setExpectedPrice(BigDecimal expectedPrice) { this.expectedPrice = expectedPrice; }
    public String getContactPerson() { return contactPerson; }
    public void setContactPerson(String contactPerson) { this.contactPerson = contactPerson; }
    public String getGender() { return gender; }
    public void setGender(String gender) { this.gender = gender; }
    public String getContactInfo() { return contactInfo; }
    public void setContactInfo(String contactInfo) { this.contactInfo = contactInfo; }
    public String getRemarks() { return remarks; }
    public void setRemarks(String remarks) { this.remarks = remarks; }
}
