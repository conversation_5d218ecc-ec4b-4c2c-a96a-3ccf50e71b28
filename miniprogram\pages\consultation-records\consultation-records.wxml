<!--pages/consultation-records/consultation-records.wxml-->
<view class="container">
  
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 咨询记录列表 -->
  <view class="records-container" wx:elif="{{!loading && recordList.length > 0}}">
    <view class="record-item" wx:for="{{recordList}}" wx:key="id">
      <view class="record-header">
        <view class="record-id">咨询ID: {{item.id}}</view>
        <view class="record-time">{{item.createTime || '暂无时间'}}</view>
      </view>
      
      <view class="record-content">
        <view class="record-row">
          <view class="record-label">咨询城市：</view>
          <view class="record-value">{{item.city}}</view>
        </view>
        
        <view class="record-row">
          <view class="record-label">联系人：</view>
          <view class="record-value">{{item.name}} ({{item.gender}})</view>
        </view>
        
        <view class="record-row">
          <view class="record-label">联系方式：</view>
          <view class="record-value">{{item.contactInfo}}</view>
        </view>
        
        <view class="record-row content-row">
          <view class="record-label">咨询内容：</view>
          <view class="record-value content-text">{{item.consultationContent}}</view>
        </view>
        
        <view class="record-row" wx:if="{{item.remarks}}">
          <view class="record-label">备注：</view>
          <view class="record-value">{{item.remarks}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{!loading && recordList.length === 0}}">
    <view class="empty-icon">📋</view>
    <view class="empty-title">暂无咨询记录</view>
    <view class="empty-desc">您还没有提交过合作咨询</view>
    <button class="empty-btn" bindtap="goToConsultation">立即咨询</button>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:elif="{{error}}">
    <view class="error-icon">⚠️</view>
    <view class="error-title">加载失败</view>
    <view class="error-desc">{{errorMessage}}</view>
    <button class="error-btn" bindtap="loadRecords">重新加载</button>
  </view>

</view>
