<!--
权限控制组件
用法：
<Permission permission="create">
  <el-button>新增</el-button>
</Permission>

<Permission :permissions="['create', 'update']">
  <el-button>编辑</el-button>
</Permission>

<Permission :permissions="['create', 'update']" require-all>
  <el-button>高级操作</el-button>
</Permission>
-->

<template>
  <div v-if="hasPermission">
    <slot />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { permissionManager } from '@/utils/permission'

const props = defineProps({
  // 单个权限
  permission: {
    type: String,
    default: ''
  },
  // 多个权限
  permissions: {
    type: Array,
    default: () => []
  },
  // 是否需要所有权限（默认只需要任意一个）
  requireAll: {
    type: Boolean,
    default: false
  }
})

// 计算是否有权限
const hasPermission = computed(() => {
  // 如果指定了单个权限
  if (props.permission) {
    return permissionManager.hasPermission(props.permission)
  }
  
  // 如果指定了多个权限
  if (props.permissions && props.permissions.length > 0) {
    if (props.requireAll) {
      // 需要所有权限
      return permissionManager.hasAllPermissions(props.permissions)
    } else {
      // 只需要任意一个权限
      return permissionManager.hasAnyPermission(props.permissions)
    }
  }
  
  // 没有指定权限时默认显示
  return true
})
</script>
