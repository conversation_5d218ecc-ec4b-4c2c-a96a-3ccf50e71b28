/**
 * 用户服务模块
 */
const api = require('../config/api.js')
const util = require('../utils/util.js')

/**
 * 微信登录
 * @param {Object} loginData 登录数据
 * @param {string} loginData.code 微信授权码
 * @param {string} loginData.nickname 用户昵称
 * @param {string} loginData.avatarUrl 用户头像URL
 * @param {string} loginData.phoneNumber 用户手机号
 */
const wechatLogin = (loginData) => {
  return new Promise((resolve, reject) => {
    api.post(api.API.WECHAT_LOGIN, loginData)
      .then(response => {
        if (response.code === 200) {
          // 保存用户信息到本地存储
          const userData = response.data
          wx.setStorageSync('userInfo', userData.user)
          wx.setStorageSync('userId', userData.user.id) // 单独保存userId
          wx.setStorageSync('accessToken', userData.accessToken)
          wx.setStorageSync('refreshToken', userData.refreshToken)
          wx.setStorageSync('openid', userData.openid)
          wx.setStorageSync('unionid', userData.unionid)
          wx.setStorageSync('isNewUser', userData.isNewUser)
          
          // 设置token过期时间
          const expiresTime = Date.now() + (userData.expiresIn * 1000)
          wx.setStorageSync('tokenExpiresTime', expiresTime)
          
          resolve(userData)
        } else {
          reject(new Error(response.message || '登录失败'))
        }
      })
      .catch(error => {
        console.error('微信登录失败:', error)
        reject(error)
      })
  })
}

/**
 * 获取微信授权码
 */
const getWechatCode = () => {
  return new Promise((resolve, reject) => {
    wx.login({
      success: (res) => {
        if (res.code) {
          resolve(res.code)
        } else {
          reject(new Error('获取微信授权码失败'))
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

/**
 * 获取用户信息 (注意：getUserProfile必须在用户点击事件中直接调用)
 * 此函数已移除，请在页面中直接调用wx.getUserProfile
 */

/**
 * 获取用户手机号
 */
const getUserPhoneNumber = (e) => {
  return new Promise((resolve, reject) => {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 调用后端接口解密手机号
      const code = e.detail.code
      if (code) {
        // 调用后端获取手机号接口
        api.get(api.API.WECHAT_PHONE, { code: code })
          .then(response => {
            if (response.code === 200) {
              resolve(response.data) // 返回解密后的手机号
            } else {
              reject(new Error(response.message || '获取手机号失败'))
            }
          })
          .catch(error => {
            reject(error)
          })
      } else {
        reject(new Error('获取手机号授权码失败'))
      }
    } else {
      reject(new Error('用户拒绝授权手机号'))
    }
  })
}

/**
 * 检查登录状态
 */
const checkLoginStatus = () => {
  const accessToken = wx.getStorageSync('accessToken')
  const tokenExpiresTime = wx.getStorageSync('tokenExpiresTime')
  
  if (!accessToken || !tokenExpiresTime) {
    return false
  }
  
  // 检查token是否过期
  if (Date.now() >= tokenExpiresTime) {
    // token已过期，清除本地存储
    clearUserData()
    return false
  }
  
  return true
}

/**
 * 获取本地用户信息
 */
const getLocalUserInfo = () => {
  return wx.getStorageSync('userInfo') || null
}

/**
 * 清除用户数据
 */
const clearUserData = () => {
  wx.removeStorageSync('userInfo')
  wx.removeStorageSync('userId')
  wx.removeStorageSync('accessToken')
  wx.removeStorageSync('refreshToken')
  wx.removeStorageSync('openid')
  wx.removeStorageSync('unionid')
  wx.removeStorageSync('isNewUser')
  wx.removeStorageSync('tokenExpiresTime')
}

/**
 * 退出登录
 */
const logout = () => {
  return new Promise((resolve) => {
    clearUserData()
    resolve()
  })
}

/**
 * 绑定手机号（通过更新用户信息）
 */
const bindPhoneNumber = (phoneNumber) => {
  return new Promise((resolve, reject) => {
    const accessToken = wx.getStorageSync('accessToken')
    if (!accessToken) {
      reject(new Error('请先登录'))
      return
    }

    // 获取当前用户信息
    const currentUserInfo = getLocalUserInfo()
    if (!currentUserInfo) {
      reject(new Error('获取用户信息失败'))
      return
    }

    // 构建更新用户信息的请求数据
    const updateData = {
      id: currentUserInfo.id,
      nickname: currentUserInfo.nickname,
      avatarUrl: currentUserInfo.avatarUrl,
      phoneNumber: phoneNumber,
      wechatOpenid: currentUserInfo.wechatOpenid,
      role: currentUserInfo.role || 0,
      favoriteHouseIds: currentUserInfo.favoriteHouseIds || [],
      followedHouseIds: currentUserInfo.followedHouseIds || []
    }

    // 调用更新用户信息接口
    api.put(api.API.USER_UPDATE, updateData, {
      'Authorization': `Bearer ${accessToken}`
    })
      .then(response => {
        if (response.code === 200) {
          // 更新本地用户信息
          const updatedUserInfo = { ...currentUserInfo, phoneNumber: phoneNumber }
          wx.setStorageSync('userInfo', updatedUserInfo)
          resolve(response.data)
        } else {
          reject(new Error(response.message || '绑定手机号失败'))
        }
      })
      .catch(error => {
        reject(error)
      })
  })
}

/**
 * 刷新token
 */
const refreshToken = () => {
  return new Promise((resolve, reject) => {
    const refreshTokenValue = wx.getStorageSync('refreshToken')
    if (!refreshTokenValue) {
      reject(new Error('没有刷新token'))
      return
    }

    // 这里应该调用刷新token的API
    // 暂时返回失败，需要重新登录
    reject(new Error('token已过期，请重新登录'))
  })
}

/**
 * 更新用户信息
 * @param {Object} updateData 要更新的用户信息
 * @param {string} updateData.nickname 用户昵称
 * @param {string} updateData.avatarUrl 用户头像URL
 * @param {string} updateData.phoneNumber 用户手机号
 */
const updateUserInfo = (updateData) => {
  return new Promise((resolve, reject) => {
    // 检查是否已登录
    if (!checkLoginStatus()) {
      reject(new Error('用户未登录'))
      return
    }

    // 调用更新用户信息API
    api.put(api.API.USER_UPDATE, updateData)
      .then(response => {
        if (response.code === 200) {
          resolve(response.data)
        } else {
          reject(new Error(response.message || '更新用户信息失败'))
        }
      })
      .catch(error => {
        console.error('更新用户信息失败:', error)
        reject(error)
      })
  })
}

/**
 * 保存本地用户信息
 * @param {Object} userInfo 用户信息
 */
const saveLocalUserInfo = (userInfo) => {
  try {
    wx.setStorageSync('userInfo', userInfo)
    console.log('本地用户信息保存成功')
  } catch (error) {
    console.error('保存本地用户信息失败:', error)
    throw error
  }
}

module.exports = {
  wechatLogin,
  getWechatCode,
  getUserPhoneNumber,
  bindPhoneNumber,
  checkLoginStatus,
  getLocalUserInfo,
  clearUserData,
  logout,
  refreshToken,
  updateUserInfo,
  saveLocalUserInfo
}
