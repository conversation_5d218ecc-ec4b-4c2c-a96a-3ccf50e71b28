<template>
  <div class="follows-manager">
    <div class="manager-header">
      <el-button type="primary" @click="handleAddFollow">
        <el-icon><Plus /></el-icon>
        添加关注
      </el-button>
    </div>

    <!-- 当前关注列表 -->
    <el-table :data="followedHouses" style="width: 100%">
      <el-table-column label="房源ID" prop="id" width="80" />
      <el-table-column label="小区名称" prop="communityName" min-width="150" />
      <el-table-column label="房屋类型" prop="houseType" width="100" />
      <el-table-column label="面积(㎡)" prop="area" width="100" />
      <el-table-column label="价格" prop="startingPrice" width="120">
        <template #default="scope">
          <span class="price">¥{{ formatPrice(scope.row.startingPrice) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template #default="scope">
          <el-button type="danger" size="small" @click="handleRemoveFollow(scope.row.id)">
            取消关注
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加关注对话框 -->
    <el-dialog
      title="添加关注房源"
      v-model="addDialogVisible"
      width="600px"
    >
      <el-form :model="addForm" label-width="100px">
        <el-form-item label="选择房源">
          <el-select
            v-model="addForm.houseId"
            placeholder="请选择要关注的房源"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="house in availableHouses"
              :key="house.id"
              :label="`${house.communityName} - ${house.houseType}`"
              :value="house.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAddFollow">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, defineProps, defineEmits } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { addFollowedHouse, removeFollowedHouse, getFollowedHouses } from '@/api/user'
import { getHouseList } from '@/api/house'

const props = defineProps({
  userId: {
    type: Number,
    required: true
  },
  followedHouseIds: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update'])

const followedHouses = ref([])
const availableHouses = ref([])
const addDialogVisible = ref(false)

const addForm = reactive({
  houseId: null
})

// 获取关注房源列表
const getFollowedList = async () => {
  try {
    // 模拟数据
    followedHouses.value = [
      {
        id: 1,
        communityName: '阳光花园',
        houseType: '住宅',
        area: 120,
        startingPrice: 1500000
      },
      {
        id: 3,
        communityName: '绿城花园',
        houseType: '公寓',
        area: 80,
        startingPrice: 800000
      }
    ]
  } catch (error) {
    ElMessage.error('获取关注列表失败')
  }
}

// 获取可选房源列表
const getAvailableHouses = async () => {
  try {
    // 模拟数据
    availableHouses.value = [
      {
        id: 2,
        communityName: '海景别墅',
        houseType: '别墅',
        area: 300,
        startingPrice: 5000000
      },
      {
        id: 4,
        communityName: '万科城',
        houseType: '住宅',
        area: 150,
        startingPrice: 2000000
      }
    ]
  } catch (error) {
    ElMessage.error('获取房源列表失败')
  }
}

// 添加关注
const handleAddFollow = () => {
  addForm.houseId = null
  addDialogVisible.value = true
}

// 确认添加关注
const confirmAddFollow = async () => {
  if (!addForm.houseId) {
    ElMessage.warning('请选择要关注的房源')
    return
  }

  try {
    await addFollowedHouse(props.userId, addForm.houseId)
    ElMessage.success('添加关注成功')
    addDialogVisible.value = false
    getFollowedList()
    emit('update')
  } catch (error) {
    ElMessage.error('添加关注失败')
  }
}

// 取消关注
const handleRemoveFollow = async (houseId) => {
  try {
    await ElMessageBox.confirm(
      '确定要取消关注这个房源吗？',
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await removeFollowedHouse(props.userId, houseId)
    ElMessage.success('取消关注成功')
    getFollowedList()
    emit('update')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消关注失败')
    }
  }
}

// 格式化价格
const formatPrice = (price) => {
  return price ? Number(price).toLocaleString() : '0'
}

onMounted(() => {
  getFollowedList()
  getAvailableHouses()
})
</script>

<style lang="scss" scoped>
.follows-manager {
  .manager-header {
    margin-bottom: 20px;
  }

  .price {
    color: #e6a23c;
    font-weight: 600;
  }
}
</style>
